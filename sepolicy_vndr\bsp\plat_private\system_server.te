# ==============================================
# Common SEPolicy Rule
# ==============================================

# Date : WK15.30
# Operation : Migration
# Purpose : for device bring up, not to block early migration/sanity
allow system_server aal_service:service_manager find;

# Date : 2017/01/24
# Purpose : Add permission for DRM / DRI GPU driver
allow system_server gas_srv_service:service_manager find;

# Date : 2017/4/14
# Purpose : Add permission for registering MtkTelecomService to ServiceManager
allow system_server mtk_telecom_service:service_manager add;

# Date : 2017/09/15
# Purpose : Add mtk_connmetrics_service for CTA's celluar data control
allow system_server mtk_connmetrics_service:service_manager add;

# Date:W17.20
# Operation : wifioffload hal developing
# Purpose : Allow to use HAL Wfo
hal_client_domain(system_server, hal_mtk_wfo)

# Date : W17.26
# Purpose: Allow to use phoneEx
allow system_server mtk_radio_service:service_manager find;

# Date : 2017/10/09
# Purpose : Record and get permission
allow system_server mtk_permrecords_service:service_manager add;

# Date : W17.36
# Operation : Migration
# Purpose : Allow system_server to add anrmanager
allow system_server mtk_anrmanager_service:service_manager add;

# Date: W17.42
# Operation : Migration
# Purpose : for WFD functionality
set_prop(system_server, system_mtk_media_wfd_prop)
set_prop(system_server, wifi_prop)

# Date:W17.47
# Purpose : Allow to enable/disable log too much
set_prop(system_server, system_mtk_logmuch_prop)
binder_call(system_server, hal_mtk_fm)

# Date: 2018/07/04
# Operation: P migration
# Purpose : allow radio get vzw device type property
get_prop(system_server, system_mtk_persist_vendor_vzw_device_type_prop)

# Date : 2018/07/03
# Stage: Migration
# Purpose: allow system server to get RTT property
get_prop(system_server, system_mtk_rtt_prop)

# Date : W18.27
# Operation : Migration
allow system_server mtk_data_shaping_service:service_manager add;

# Date : W18.28
# Operation : Support telephony log
get_prop(system_server, system_mtk_em_tel_log_prop)

# Date : W18.29
# Operation : For background data disable function
get_prop(system_server, system_mtk_bgdata_disabled_prop)

# Date : W18.24
# Operation : for AMS log
set_prop(system_server, system_mtk_amslog_prop)

# Date : W18.25
# Operation : for AMS-aal
set_prop(system_server, system_mtk_amsaal_prop)

# Date : W18.31
# Purpose : Support Trustonic TeeService
binder_call(system_server, teed_app)
binder_call(system_server, teeregistryd_app)
allow system_server tee_service:service_manager find;
allow system_server teeregistry_service:service_manager find;

# Date : W19.12
# Operation : For DuraSpeed Migration
set_prop(system_server, system_mtk_duraspeed_drop_caches_prop)

# Date : W19.12
# Operation : For DuraSpeed Migration
allow system_server mtk_duraspeed_service:service_manager add;

# Date : 2019/06/03
# Operation : Q Migration split build
# Purpose : allow to get system_mtk_rsc_sys_prop
get_prop(system_server, system_mtk_rsc_sys_prop)

# Date : W19.29
# Operation : Support heavy loading
get_prop(system_server, system_mtk_heavy_loading_prop)

# Date : WK19.29
# Operation : touchll hal
# Purpose : touchll hal permission
hal_client_domain(system_server, hal_mtk_touchll)

# Date: 2020/01/16
# Purpose : Allow system server to read tll dev
allow system_server tll_device:chr_file r_file_perms;

# Date : 2020/03/20
# Operation: R migration
get_prop(system_server, system_mtk_telecom_vibrate_prop)

# Date:2020/03/26
# Operation:Q Migration
allow system_server proc_battery_cmd:dir search;

# Date : 2020/04/14
# Purpose: Allow ConnectivityService to get USB tethering system property for auto test
get_prop(system_server, system_mtk_usb_tethering_prop)

# Date : 2020/05/18
# Operation : R Migration
get_prop(system_server, system_mtk_graphics_sf_gll_ro_prop)

# Date : 2020/05/19
# Purpose : Add mtk_autoboot_service for CTA's autoboot app control
allow system_server mtk_autoboot_service:service_manager add;

# Date : 2020/06/01
# Operation : R Migration
allow system_server sysfs_HDMI_audio_extcon_state:file r_file_perms;

# Date : 2020/07/13
# Purpose : Add permission for AMS access to report Java Layer Exception
allow system_server crash_dump:process { getpgid setsched };

# Date : 2020/07/20
# Purpose : Add permission for AMS access to report Java Layer Exception
allow system_server zygote:process getpgid;

# Date : 2020/07/23
# Purpose : Add permission for AMS access to report Java Layer Exception
allow system_server app_zygote:process getpgid;

# Date:2020/07/27
# Operation:R Migration
allow system_server installd:process signal;

# Date:2020/09/04
# Operation:R Migration, add permission for AMS dump binderinfo when ANR happened in user load
allow system_server binderfs_logs:dir r_dir_perms;
allow system_server binderfs_logs:file r_file_perms;
allow system_server binderfs_logs_proc:dir r_dir_perms;
allow system_server binderfs_logs_proc:file r_file_perms;

# Date:2020/09/24
# Operation:R Migration, add permission for PMS access /data/media
allow system_server media_rw_data_file:dir setattr;

# Date:2020/09/25
# Operation:R Migration, don't audit for PMS access /mnt/media_rw/XXXX-XXXX/Android/obb
dontaudit system_server vfat:dir r_dir_perms;

# Date:2021/11/13
# Operation: Add for DSDA in Telecom, add permission for accessing vendor.radio.dsda.state
get_prop(system_server, system_mtk_common_data_prop)

