# ==============================================
# Common SEPolicy Rule
# ==============================================

type emdlogger_exec, system_file_type, exec_type, file_type;
typeattribute emdlogger coredomain;
typeattribute emdlogger mlstrustedsubject;

init_daemon_domain(emdlogger)
binder_use(emdlogger)
binder_service(emdlogger)

# for modem logging sdcard access
allow emdlogger sdcard_type:dir create_dir_perms;
allow emdlogger sdcard_type:file create_file_perms;

# modem logger socket access
allow emdlogger platform_app:unix_stream_socket connectto;
allow emdlogger shell_exec:file rx_file_perms;
allow emdlogger system_file:file x_file_perms;
allow emdlogger zygote_exec:file rx_file_perms;

#modem logger SD logging in factory mode
allow emdlogger vfat:dir create_dir_perms;
allow emdlogger vfat:file create_file_perms;

#modem logger permission in storage in android M version
allow emdlogger mnt_user_file:dir search;
allow emdlogger mnt_user_file:lnk_file r_file_perms;
allow emdlogger storage_file:lnk_file r_file_perms;

#permission for storage link access in vzw Project
allow emdlogger mnt_media_rw_file:dir search;


#permission for use SELinux API
#avc: denied { read } for pid=576 comm="emdlogger1" name="selinux_version" dev="rootfs"
allow emdlogger rootfs:file r_file_perms;

#permission for storage access storage
allow emdlogger storage_file:dir create_dir_perms;
allow emdlogger tmpfs:lnk_file r_file_perms;
allow emdlogger storage_file:file create_file_perms;

# Allow read avc: denied { read } for name="mddb" dev="mmcblk0p25" ino=681
# scontext=u:r:emdlogger:s0 tcontext=u:object_r:system_file:s0 tclass=dir permissive=0
allow emdlogger system_file:dir r_dir_perms;

# permission for android N policy
allow emdlogger toolbox_exec:file rx_file_perms;

# purpose: allow emdlogger to access storage in N version
allow emdlogger media_rw_data_file:file create_file_perms;
allow emdlogger media_rw_data_file:dir create_dir_perms;

## Android P migration
## purpose:  denied { read } for name="cmdline" dev="proc"
#denied { search } for name="android" dev="sysfs"
#for name="compatible" dev="sysfs" ino=2985 scontext=u
#:r:emdlogger:s0 tcontext=u:object_r:sysfs_dt_firmware_android:s0
#avc: denied { open } for path="/system/etc/mddb"
#avc: denied { read } for name="u:object_r:vendor_default_prop:s0"
allow emdlogger proc_cmdline:file r_file_perms;
allow emdlogger sysfs_dt_firmware_android:dir r_dir_perms;
allow emdlogger tmpfs:dir w_dir_perms;
allow emdlogger sysfs_dt_firmware_android:file r_file_perms;
set_prop(emdlogger, system_mtk_persist_mtklog_prop)
set_prop(emdlogger, system_mtk_mdl_prop)
set_prop(emdlogger, system_mtk_mdl_start_prop)
set_prop(emdlogger, system_mtk_debug_mdlogger_prop)
set_prop(emdlogger, system_mtk_persist_mdlog_prop)
set_prop(emdlogger, system_mtk_mdl_pulllog_prop)
set_prop(emdlogger, usb_prop)
set_prop(emdlogger, debug_prop)
set_prop(emdlogger, usb_control_prop)

## Android Q migration
## purpose:  read modem db and filter folder and file
allow emdlogger mddb_filter_data_file:dir r_dir_perms;
allow emdlogger mddb_filter_data_file:file r_file_perms;

# save log into /data/debuglogger
allow emdlogger debuglog_data_file:dir {relabelto create_dir_perms};
allow emdlogger debuglog_data_file:file create_file_perms;

# get persist.sys. proeprty
get_prop(emdlogger, system_prop)
