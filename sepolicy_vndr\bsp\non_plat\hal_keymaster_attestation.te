# ==============================================
# MICROTRUST SEPolicy Rule
# ==============================================

allow hal_keymaster_attestation ut_keymaster_device:chr_file rw_file_perms;
allow hal_keymaster_attestation teei_client_device:chr_file rw_file_perms;
hal_client_domain(hal_keymaster_attestation, hal_teei_capi)
hal_client_domain(hal_keymaster_attestation, hal_allocator)
hal_client_domain(hal_keymaster_attestation, hal_keymaster)
set_prop(hal_keymaster_attestation, vendor_mtk_soter_teei_prop)
allow hal_keymaster_attestation tkcore_admin_device:chr_file rw_file_perms;

# Date : 2017/08/08 (or WK17.32)
# Operation : Keymaster 3.0 Migration
# Purpose : Set sepolicy for Keymaster attestation key injection
allow hal_keymaster_attestation mobicore:unix_stream_socket connectto;
allow hal_keymaster_attestation mobicore_user_device:chr_file rw_file_perms;
