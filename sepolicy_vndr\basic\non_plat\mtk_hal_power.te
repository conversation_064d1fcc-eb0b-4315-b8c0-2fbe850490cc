# ==============================================
# Common SEPolicy Rule
# ==============================================

type mtk_hal_power, domain;
type mtk_hal_power_exec, exec_type, file_type, vendor_file_type;

# hwbinder access
init_daemon_domain(mtk_hal_power)

hal_server_domain(mtk_hal_power, hal_power)
hal_server_domain(mtk_hal_power, hal_wifi)

# Allow mtkpower stub service to call powerhal
binder_call(mtk_hal_power, hal_power_default)

# sysfs
allow mtk_hal_power sysfs_devices_system_cpu:file rw_file_perms;
allow mtk_hal_power sysfs_mtk_core_ctl:dir r_dir_perms;
allow mtk_hal_power sysfs_mtk_core_ctl:file rw_file_perms;


# proc_thermal
allow mtk_hal_power proc_thermal:file rw_file_perms;

# proc info
allow mtk_hal_power hal_audio_default:dir r_dir_perms;

# Date : 2017/10/02
# Operation: SQC
# Purpose : Allow powerHAL to access perfmgr
allow mtk_hal_power proc_perfmgr:dir r_dir_perms;
allow mtk_hal_power proc_perfmgr:file rw_file_perms;
allowxperm mtk_hal_power proc_perfmgr:file ioctl {
    PERFMGR_FPSGO_TOUCH
    PERFMGR_FPSGO_GET_FPS
    PERFMGR_FPSGO_GET_FSTB_ACTIVE
    PERFMGR_FPSGO_WAIT_FSTB_ACTIVE
    PERFMGR_FPSGO_SBE_RESCUE
    EAS_SYNC_SET
    EAS_PERTASK_LS_SET
    CORE_CTL_FORCE_PAUSE_CPU
    CORE_CTL_SET_OFFLINE_THROTTLE_MS
    CORE_CTL_SET_LIMIT_CPUS
    CORE_CTL_SET_NOT_PREFERRED
    CORE_CTL_SET_BOOST
    CORE_CTL_SET_UP_THRES
    CPUQOS_V3_SET_CPUQOS_MODE
    CPUQOS_V3_SET_CT_TASK
    CPUQOS_V3_SET_CT_GROUP
    EAS_NEWLY_IDLE_BALANCE_INTERVAL_SET
    EAS_GET_THERMAL_HEADROOM_INTERVAL_SET
    };

# Date : 2017/10/11
# Operation: SQC
# Purpose : Allow powerHAL to access powerhal folder
allow mtk_hal_power sdcard_type:dir create_dir_perms;
allow mtk_hal_power sdcard_type:file create_file_perms;
allow mtk_hal_power eemcs_device:chr_file rw_file_perms;
allow mtk_hal_power mnt_user_file:dir create_dir_perms;

allow mtk_hal_power mtk_powerhal_data_file:dir create_dir_perms;
allow mtk_hal_power mtk_powerhal_data_file:file create_file_perms;
allow mtk_hal_power mtk_powerhal_data_file:sock_file create_file_perms;

#camera contorl cpu
allow mtk_hal_power mtk_hal_camera:dir r_dir_perms;
allow mtk_hal_power mtk_hal_camera:file r_file_perms;

# Date : 2017/10/24
# Operation: SQC
# Purpose : Allow powerHAL to access thermal
allow mtk_hal_power proc_thermal:dir r_dir_perms;

# Date : 2017/12/19
# Operation: SQC
# Purpose : Allow powerHAL to access wlan
allow mtk_hal_power proc_net:file w_file_perms;

# Date : 2017/12/21
# Operation: SQC
# Purpose : Allow powerHAL to access mediacodec
allow mtk_hal_power mediacodec:dir r_dir_perms;
allow mtk_hal_power mediacodec:file r_file_perms;

allow mtk_hal_power mediaserver:dir r_dir_perms;
allow mtk_hal_power mediaserver:file r_file_perms;

set_prop(mtk_hal_power, vendor_mtk_thermal_config_prop)
set_prop(mtk_hal_power, vendor_mtk_powerhal_gpu_prop)

# Date : 2018/06/26
# Operation: Thermal change policy in perfservice
allow mtk_hal_power thermal_manager_data_file:file create_file_perms;
allow mtk_hal_power thermal_manager_data_file:dir { rw_dir_perms setattr };
allow mtk_hal_power thermalloadalgod:unix_stream_socket connectto;

allow mtk_hal_power proc_mtkcooler:dir r_dir_perms;
allow mtk_hal_power proc_mtkcooler:file rw_file_perms;
allow mtk_hal_power proc_mtktz:dir r_dir_perms;
allow mtk_hal_power proc_mtktz:file rw_file_perms;

# Date : 2019/05/08
# Operation: SQC
# Purpose : Allow powerHAL to access /proc/[pid]
allow mtk_hal_power system_server:dir r_dir_perms;
allow mtk_hal_power system_server:file r_file_perms;


allow mtk_hal_power rild_oem_socket:sock_file w_file_perms;
allow mtk_hal_power rild:unix_stream_socket connectto;

# Date : 2019/05/22
# Operation: SQC
# Purpose : Allow powerHAL to access block read ahead
allow mtk_hal_power sysfs_dm:dir r_dir_perms;
allow mtk_hal_power sysfs_dm:file rw_file_perms;
allow mtk_hal_power sysfs_devices_block:dir r_dir_perms;
allow mtk_hal_power sysfs_devices_block:file rw_file_perms;


# Date : 2019/05/22
# Operation: SQC
# Purpose : Allow powerHAL to access prop
set_prop(mtk_hal_power, vendor_mtk_powerhal_prop)

# Date : 2019/05/29
# Operation: SQC
# Purpose : Allow powerHAL to access wifi driver
allow mtk_hal_power self:udp_socket create_socket_perms_no_ioctl;
allow mtk_hal_power kernel:system module_request;
allow mtk_hal_power self:capability sys_module;
allowxperm mtk_hal_power self:udp_socket ioctl priv_sock_ioctls;

# Date : 2019/09/05
# Operation: SQC
# Purpose : Add procfs, sysfs policy
allow mtk_hal_power proc_ppm:dir r_dir_perms;
allow mtk_hal_power proc_ppm:file rw_file_perms;
allow mtk_hal_power proc_cpufreq:dir r_dir_perms;
allow mtk_hal_power proc_cpufreq:file rw_file_perms;
allow mtk_hal_power proc_hps:dir r_dir_perms;
allow mtk_hal_power proc_hps:file rw_file_perms;
allow mtk_hal_power proc_cm_mgr:dir r_dir_perms;
allow mtk_hal_power proc_cm_mgr:file rw_file_perms;
allow mtk_hal_power proc_fliperfs:dir r_dir_perms;
allow mtk_hal_power proc_fliperfs:file rw_file_perms;
allow mtk_hal_power sysfs_ged:dir r_dir_perms;
allow mtk_hal_power sysfs_ged:file rw_file_perms;
allow mtk_hal_power sysfs_fbt_cpu:dir r_dir_perms;
allow mtk_hal_power sysfs_fbt_cpu:file rw_file_perms;
allow mtk_hal_power sysfs_fbt_fteh:dir r_dir_perms;
allow mtk_hal_power sysfs_fbt_fteh:file rw_file_perms;
allow mtk_hal_power sysfs_xgf:dir r_dir_perms;
allow mtk_hal_power sysfs_xgf:file rw_file_perms;
allow mtk_hal_power sysfs_mtk_fpsgo:dir r_dir_perms;
allow mtk_hal_power sysfs_mtk_fpsgo:file rw_file_perms;
allow mtk_hal_power sysfs_fpsgo:dir r_dir_perms;
allow mtk_hal_power sysfs_fpsgo:file rw_file_perms;
allow mtk_hal_power sysfs_gbe:dir r_dir_perms;
allow mtk_hal_power sysfs_gbe:file rw_file_perms;
allow mtk_hal_power gbe_native:dir r_dir_perms;
allow mtk_hal_power gbe_native:file r_file_perms;
allow mtk_hal_power fpsgo_native:dir r_dir_perms;
allow mtk_hal_power fpsgo_native:file r_file_perms;

# Date : 2019/09/17
# Operation: SQC
# Purpose : Add cache audit
allow mtk_hal_power sysfs_cache_ctrl:dir r_dir_perms;
allow mtk_hal_power sysfs_cache_ctrl:file rw_file_perms;
allow mtk_hal_power sysfs_pftch_qos:dir r_dir_perms;
allow mtk_hal_power sysfs_pftch_qos:file rw_file_perms;

# Date : 2019/09/18
# Operation: SQC
# Purpose : Add f2fs permission
allow mtk_hal_power sysfs_fs_f2fs:dir r_dir_perms;
allow mtk_hal_power sysfs_fs_f2fs:file rw_file_perms;

# Date : 2019/09/19
# Operation: SQC
# Purpose : Add task turbo
allow mtk_hal_power sysfs_task_turbo:dir r_dir_perms;
allow mtk_hal_power sysfs_task_turbo:file rw_file_perms;

# Date : 2019/09/23
# Operation: SQC
# Purpose : Allow powerHAL to access touch boost
allow mtk_hal_power sysfs_change_rate:file rw_file_perms;

# Date : 2019/10/16
# Operation: SQC
allow mtk_hal_power sysfs_ext4_disable_barrier:file w_file_perms;
allow mtk_hal_power block_device:dir search;

# Date : 2019/11/14
# Operation: SQC
# Purpose : Allow powerhal to control MCDI
allow mtk_hal_power proc_cpuidle:dir r_dir_perms;
allow mtk_hal_power proc_cpuidle:file rw_file_perms;

# Date : 2020/06/12
# Operation: SQC
# Purpose : Allow powerhal to control mali power policy
allow mtk_hal_power sysfs_mali_power_policy:file rw_file_perms;

# Date : 2020/06/12
# Operation: SQC
# Purpose : Allow powerhal to control displowpower
allow mtk_hal_power proc_displowpower:dir r_dir_perms;
allow mtk_hal_power proc_displowpower:file rw_file_perms;

# Date : 2020/07/31
# Purpose: add permission for /sys/kernel/apusys/
allow mtk_hal_power sysfs_apusys:dir r_dir_perms;
allow mtk_hal_power sysfs_apusys:file rw_file_perms;

# vpud_native control CPU
# Date : 2020/8/7
# Operation: video playback
allow mtk_hal_power vpud_native:dir r_dir_perms;
allow mtk_hal_power vpud_native:file rw_file_perms;
allow mtk_hal_power mtk_hal_c2:dir r_dir_perms;
allow mtk_hal_power mtk_hal_c2:file rw_file_perms;

# Date : 2020/08/19
# Purpose: add permission for /sys/class/devfreq/mtk-dvfsrc-devfreq/userspace
allow mtk_hal_power sysfs_dvfsrc_devfreq:dir r_dir_perms;
allow mtk_hal_power sysfs_dvfsrc_devfreq:file rw_file_perms;

# Date : 2020/04/15
# Operation: SQC
# Purpose : Allow powerhal to control video mode property
set_prop(mtk_hal_power, vendor_mtk_video_prop)

# Date : 2020/09/18
# Purpose: allow powerhal to control mcdi
allow mtk_hal_power proc_mcdi:dir r_dir_perms;
allow mtk_hal_power proc_mcdi:file rw_file_perms;

# Date : 2020/09/29
# Purpose: add permission for /sys/kernel/eara_thermal/
allow mtk_hal_power sysfs_eara_thermal:dir r_dir_perms;
allow mtk_hal_power sysfs_eara_thermal:file rw_file_perms;

# Date : 2021/3/12
# Purpose: add permission for /sys/class/misc/mali0/device/pm_poweroff
allow mtk_hal_power sysfs_mali_poweroff:dir r_dir_perms;
allow mtk_hal_power sysfs_mali_poweroff:file rw_file_perms;

# Date : 2021/03/19
# Purpose: add permission for EARA IO Service
allow mtk_hal_power eara_io:dir r_dir_perms;
allow mtk_hal_power eara_io:file r_file_perms;

# Date : 2021/03/15
# Purpose: add permission for /dev/cpu_dma_latency
allow mtk_hal_power cpu_dma_latency_device:chr_file rw_file_perms;

# Date : 2021/4/14
# change policy via socket to thermal core
allow mtk_hal_power thermal_socket:sock_file write;
allow mtk_hal_power thermal_core:unix_stream_socket connectto;

# Date : 2021/03/15
# Purpose: add powerhal to control eara property
set_prop(mtk_hal_power, vendor_mtk_frs_prop)

# Date : 2021/04/20
# Purpose: add permission for vendor.sys.vm.swappiness/dropcaches/extrafreekbytesadj/watermarkscalefactor
set_prop(mtk_hal_power, vendor_mtk_vm_prop)

# Date : 2021/04/26
# Purpose: allow thermal core to limit CPU and GPU
allow mtk_hal_power thermal_core:dir { getattr search };
allow mtk_hal_power thermal_core:file r_file_perms;

# Date : 2021/05/07
# add permission for dev/cpuctl/
allow mtk_hal_power cgroup:file rw_file_perms_no_map;
allow mtk_hal_power cgroup:dir r_dir_perms;

allow mtk_hal_power mtk_hal_bluetooth_audio_hwservice:hwservice_manager find;

# Date: WK21.33
# Purpose: allow Power-HAL to access /proc/[surfaceflinger_pid]
allow mtk_hal_power surfaceflinger:dir r_dir_perms;
allow mtk_hal_power surfaceflinger:file r_file_perms;

# 2021/8/25
# allow powerhal to access /proc/cpuhvfs/cpufreq_cci_mode
allow mtk_hal_power proc_cpuhvfs:dir r_dir_perms;
allow mtk_hal_power proc_cpuhvfs:file rw_file_perms;

# 2021/8/25
# allow powerhal to access /sys/kernel/cm_mgr/dbg_cm_mgr
allow mtk_hal_power sysfs_cm_mgr:dir r_dir_perms;
allow mtk_hal_power sysfs_cm_mgr:file rw_file_perms;

set_prop(mtk_hal_power, vendor_mtk_bt_perf_prop)

# Date : 2021/10/14
# Purpose: add permission for get lmkd_config_prop
get_prop(mtk_hal_power, lmkd_config_prop)

# Date: 2021/12/08
# allow powerhal to access /sys/kernel/thermal/sports_mode
allow mtk_hal_power sysfs_thermal_sram:file w_file_perms;

# Date: 2022/01/17
# allow powerhal to access /sys/kernel/helio-dvfsrc/dvfsrc_qos_mode
allow mtk_hal_power sysfs_dvfsrc_dbg:dir r_dir_perms;
allow mtk_hal_power sysfs_dvfsrc_dbg:file rw_file_perms;

# Date: 2021/12/24
# allow powerhal to access /proc/mgq
allow mtk_hal_power proc_mgq:dir r_dir_perms;
allow mtk_hal_power proc_mgq:file w_file_perms;

# Allow to access thermal node
allow mtk_hal_power sysfs_therm:file { write open };
