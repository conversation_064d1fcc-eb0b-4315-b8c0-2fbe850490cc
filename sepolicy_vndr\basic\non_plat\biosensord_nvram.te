# ==============================================
# Policy File of /vendor/bin/biosensord_nvram Executable File

# ==============================================
# Type Declaration
# ==============================================
type biosensord_nvram, domain;
type biosensord_nvram_exec , exec_type, file_type, vendor_file_type;

# ==============================================
# Common SEPolicy Rule
# ==============================================
init_daemon_domain(biosensord_nvram)

# Data : WK16.21
# Operation : New Feature
# Purpose : For biosensor daemon can do nvram r/w to save calibration data
allow biosensord_nvram nvdata_file:dir rw_dir_perms;
allow biosensord_nvram nvdata_file:file create_file_perms;
allow biosensord_nvram nvram_data_file:lnk_file rw_file_perms;
allow biosensord_nvram biometric_device:chr_file rw_file_perms;
allow biosensord_nvram self:capability { chown fsetid };
