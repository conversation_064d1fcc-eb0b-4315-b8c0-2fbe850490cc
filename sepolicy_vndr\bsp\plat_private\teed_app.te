# ==============================================
# Common SEPolicy Rule
# ==============================================

typeattribute teed_app coredomain;

app_domain(teed_app)
binder_service(teed_app)
binder_use(teed_app)

add_service(teed_app, tee_service)

hal_client_domain(teed_app, hal_tee)
hal_client_domain(teed_app, hal_allocator)

allow teed_app activity_service:service_manager find;
allow teed_app connectivity_service:service_manager find;
allow teed_app display_service:service_manager find;
allow teed_app network_management_service:service_manager find;
allow teed_app notification_service:service_manager find;

allow teed_app system_app_data_file:dir { getattr search };

#============= teed_app for TUI ==============
allow teed_app surfaceflinger_service:service_manager find;
allow teed_app activity_task_service:service_manager find;
allow teed_app media_session_service:service_manager find;
allow teed_app system_data_file:dir search;
allow teed_app user_profile_root_file:dir search;
allow teed_app audio_service:service_manager find;
allow teed_app content_capture_service:service_manager find;
allow teed_app gpu_service:service_manager find;

#============= teed_app for thermal_service ==============
allow teed_app thermal_service:service_manager find;
