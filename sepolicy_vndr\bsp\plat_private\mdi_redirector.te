# ==============================================
# Policy File of /system/bin/mdi_redirector Executable File

# ==============================================
# Common SEPolicy Rule
# ==============================================

type mdi_redirector_exec ,exec_type, file_type, system_file_type;
typeattribute mdi_redirector coredomain;

init_daemon_domain(mdi_redirector)
net_domain(mdi_redirector)

# Date : WK19.07 2019/02/15
# Operation : mdi_redirector integration test with AT&T Linkmaster
# Purpose : To allow mdi_redirector create socket to forward KPI to PC tool
allow mdi_redirector fwmarkd_socket:sock_file write;
allow mdi_redirector self:tcp_socket create_stream_socket_perms;
allow mdi_redirector self:udp_socket create_stream_socket_perms;
allow mdi_redirector node:tcp_socket node_bind;
allow mdi_redirector port:tcp_socket name_bind;
allow mdi_redirector netd:unix_stream_socket connectto;

# Allow to connect DMC HIDL server
hal_client_domain(mdi_redirector, hal_mtk_dmc)
