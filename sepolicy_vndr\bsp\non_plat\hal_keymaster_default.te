# ==============================================
# Common SEPolicy Rule
# ==============================================

# Date : WK17.42 2017/10/19
# Operation: Keymaster 3.0
# Purpose: Access attestation key in persist partition
allow hal_keymaster_default mnt_vendor_file:dir search;
allow hal_keymaster_default persist_data_file:dir search;
allow hal_keymaster_default persist_data_file:file r_file_perms;

# Date : WK17.22 2017/06/02 (Revised for HIDL)
# Operation : keystore CTS
# Purpose : Open MobiCore access permission for keystore CTS hardware-backed solution
allow hal_keymaster_default mobicore:unix_stream_socket { connectto read write };
allow hal_keymaster_default mobicore_user_device:chr_file rw_file_perms;

# Date : 2016/06/01
# Operation: TEEI integration
# Purpose: Microtrust HW-backed Keymaster
allow hal_keymaster_default ut_keymaster_device:chr_file rw_file_perms;
allow hal_keymaster_default teei_client_device:chr_file rw_file_perms;
set_prop(hal_keymaster_default, vendor_mtk_soter_teei_prop)
hal_client_domain(hal_keymaster_default, hal_teei_capi)
hal_client_domain(hal_keymaster_default, hal_allocator)

# Purpose: TrustKernel HW-backed Keymaster
allow hal_keymaster_default tkcore_admin_device:chr_file rw_file_perms;

# Date : 2018/09/11
# Operation: MTEE Keymaster
# Purpose: Access kisd to get key & certs
allow hal_keymaster_default kisd:unix_stream_socket connectto;
