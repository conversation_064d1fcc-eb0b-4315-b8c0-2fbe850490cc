# ==============================================
# Policy File of /system/bin/vtservice Executable File

# ==============================================
# Common SEPolicy Rule
# ==============================================

typeattribute vtservice coredomain;
type vtservice_exec, system_file_type, exec_type, file_type;

init_daemon_domain(vtservice)
binder_use(vtservice)
binder_call(vtservice, mediaserver)
binder_service(vtservice)

# Date : WK15.33
# Purpose : Add vtservice to support video telephony functionality
#           3G VT/ViLTE both use this service which will also communication with IMCB/Rild
allow vtservice vtservice_service:service_manager add;

# Date: 2018/08/24
# Operation: add mdp
get_prop(vtservice, system_mtk_debug_bq_dump_prop)

# tel log property
get_prop(vtservice, system_mtk_em_tel_log_prop)
