# ==============================================
# Common SEPolicy Rule
# ==============================================

#cmddumper access external modem ttySDIO2
allow cmddumper ttySDIO_device:chr_file rw_file_perms;

# for modem logging sdcard access
allow cmddumper sdcard_type:dir create_dir_perms;
allow cmddumper sdcard_type:file create_file_perms;

# cmddumper  access on /data/mdlog
allow cmddumper mdlog_data_file:fifo_file create_file_perms;
allow cmddumper mdlog_data_file:file create_file_perms;
allow cmddumper mdlog_data_file:dir { create_dir_perms relabelto };

# purpose: allow cmddumper to access storage in N version
allow cmddumper media_rw_data_file:file create_file_perms;
allow cmddumper media_rw_data_file:dir create_dir_perms;

# purpose: access plat_file_contexts
allow cmddumper file_contexts_file:file r_file_perms;

# purpose: access /sys/devices/virtual/BOOT/BOOT/boot/boot_mode
allow cmddumper sysfs_boot_mode:file r_file_perms;

# Android P migration
allow cmddumper tmpfs:lnk_file r_file_perms;
allow cmddumper vmodem_device:chr_file rw_file_perms;
