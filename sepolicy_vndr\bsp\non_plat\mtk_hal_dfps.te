# ==============================================
# Type Declaration
# ==============================================
type mtk_hal_dfps_exec, exec_type, file_type, vendor_file_type;

# ==============================================
# Common SEPolicy Rule
# ==============================================
init_daemon_domain(mtk_hal_dfps)

hwbinder_use(mtk_hal_dfps);
vndbinder_use(mtk_hal_dfps);

hal_server_domain(mtk_hal_dfps, hal_dfps)

add_hwservice(hal_dfps_server, mtk_hal_dfps_hwservice)

# sysfs access.
r_dir_file(mtk_hal_dfps, proc_net);

get_prop(mtk_hal_dfps, hwservicemanager_prop)

allow mtk_hal_dfps mtk_dfrc_device:chr_file rw_file_perms;
