##
# Trustonic TeeService
#

type hal_tee_default_exec, exec_type, vendor_file_type, file_type;

init_daemon_domain(hal_tee_default)

hal_server_domain(hal_tee_default, hal_tee)
hal_client_domain(hal_tee_default, hal_allocator)

# Access to TEE driver nodes (user and tui)
allow hal_tee_default mobicore_user_device:chr_file rw_file_perms;
allow hal_tee_default mobicore_tui_device:chr_file rw_file_perms;
allow hal_tee_default system_app:fd use;

# HIDL memory is using this behind the scene
allow hal_tee_default untrusted_app_all:fd use;
allow hal_tee_default teeregistryd_app:fd use;

