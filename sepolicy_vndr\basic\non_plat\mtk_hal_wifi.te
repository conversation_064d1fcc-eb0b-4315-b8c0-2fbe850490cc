# ==============================================
# Common SEPolicy Rule
# ==============================================

type mtk_hal_wifi, domain;
type mtk_hal_wifi_exec, exec_type, vendor_file_type, file_type;

init_daemon_domain(mtk_hal_wifi)
hal_server_domain(mtk_hal_wifi, hal_wifi)

allow mtk_hal_wifi self:capability sys_module;
allow mtk_hal_wifi vendor_file:system module_load;
allow mtk_hal_wifi kernel:system module_request;
