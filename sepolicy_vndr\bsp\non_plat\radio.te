# ==============================================
# Common SEPolicy Rule
# ==============================================

# Date : WK16.09
# Operation : Migration for SWO policy package survey
allow radio rild_mal_socket:sock_file write;
allow radio rild_mal_at_socket:sock_file write;
allow radio rild_mal_md2_socket:sock_file write;
allow radio rild_mal_at_md2_socket:sock_file write;

# Date : 2018/06/25
# Purpose: for world phone get modem type
get_prop(radio, vendor_mtk_ril_active_md_prop)

# Date : WK15.33 2015/08/13
# Operation : IT
# Purpose : for setting volte enable property
get_prop(radio, vendor_mtk_volte_prop)

# Date : WK15.48 2015/11/23
# Operation : IT
# Purpose : for setting wfc enable property
get_prop(radio, vendor_mtk_wfc_prop)

# Date : WK16.47 2016/11/17
# Operation : IT
# Purpose : for setting vilte enable property after 93 modem
get_prop(radio, vendor_mtk_vilte_prop)

# Date : WK16.47 2016/11/17
# Operation : IT
# Purpose : for setting viwifi enable property
get_prop(radio, vendor_mtk_viwifi_prop)

# Date : WK15.48 2015/11/23
# Operation : IT
# Purpose : for setting vt enable property
get_prop(radio, vendor_mtk_vt_prop)

# Date : 2017/08/14
# Operation : VT development
# Purpose : Add vtservice to support video telephony functionality
#           3G VT/ViLTE both use this service which will also communication with IMCB/Rild
allow radio vtservice:binder call;
allow radio vtservice:binder transfer;

# Date: 2017/11/14
# Operation : rcs hal developing
# Purpose : Allow to use HAL rcs
hal_client_domain(radio, hal_rcs);

# Date : 2018/6/29
# Operation: P migration
# Purpose: Allow radio to get vendor_mtk_vsim_prop
get_prop(radio, vendor_mtk_vsim_prop)

# Date : 2018/05/16
# Operation: P migration
get_prop(radio, vendor_mtk_ims_prop)

# Date : 2018/05/23
# Purpose: for SWIFT connecting to ATCI
hal_client_domain(radio, hal_mtk_atci)

# Date: 2018/06/12
# Purpose: P SQC, for SMS framework access PplAgent HIDL interface
allow radio mtk_hal_pplagent_hwservice:hwservice_manager find;
allow radio ppl_agent:binder call;

# Date : 2018/06/19
# Operation : P migration
# Purpose : for SelfRegister to call nvram hal
hal_client_domain(radio, hal_mtk_nvramagent)

# Date : 2019/05/16
# Operation : IT
# Purpose : Add for HIDL service
hal_client_domain(radio, md_monitor_hal)

# Date : 2019/07/15
# Operation : IT
# Purpose : for setting ims nr enable property
get_prop(radio, vendor_mtk_vonr_prop)
get_prop(radio, vendor_mtk_vinr_prop)

# Date : 2019/08/20
# Operation : DMC Q Migration
# Purpose : allow to get vendor_mtk_dmc_prop
get_prop(radio, vendor_mtk_dmc_prop)

# Date : 2020/10/30
# Operation : QC
# Purpose : for getting ims nr force enable property
get_prop(radio, vendor_mtk_vonr_force_prop)

# Date: 2021/3/30
# Purpose: Allow rild to get call drop feature
get_prop(radio, vendor_mtk_call_drop_prop)

# Date : 2021/08/17
# Purpose : Allow radio to read ro.vendor.mtk_mcf_support
get_prop(radio, vendor_mtk_mcf_prop)
