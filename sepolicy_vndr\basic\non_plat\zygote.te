# ==============================================
# Common SEPolicy Rule
# ==============================================

# Date : WK16.33
# Purpose: Allow to access ged for gralloc_extra functions
allow zygote proc_ged:file rw_file_perms;

# Date : WK17.02
# Purpose: Allow to access gpu for memtrack functions
allow zygote gpu_device:dir search;
allow zygote gpu_device:chr_file rw_file_perms;

allow zygote proc_bootprof:file rw_file_perms;
allow zygote proc_uptime:file rw_file_perms;

# Date : WK21.29
# Purpose: Allow Zygote to unmount labeledfs
allow zygote labeledfs:filesystem { unmount };

# Date : WK21.41
# Purpose: Allow Zygote to access cgroup for statsd functions
allow zygote cgroup:file setattr;

# Date : WK21.47
# Purpose: donta<PERSON>t Zygote set its the nice value
dontaudit zygote self:capability sys_nice;

# Date : WK22.04
# Purpose: dontaudit Zygote write system_file when restarting Android
dontaudit zygote system_file:dir write;
