type mtk_hal_gpu, domain;
type mtk_hal_gpu_exec, exec_type, file_type, vendor_file_type;

# Setup for domain transition
init_daemon_domain(mtk_hal_gpu)

# Allow to use HWBinder IPC
hwbinder_use(mtk_hal_gpu);

# Allow a set of permissions required for a domain to be a server which provides a HAL implementation over HWBinder.
hal_server_domain(mtk_hal_gpu, hal_gpu)

# add/find permission rule to hwservicemanager
add_hwservice(hal_gpu, mtk_hal_gpu_hwservice)
allow hal_gpu_client mtk_hal_gpu_hwservice:hwservice_manager find;

# Allow to allocate hidl memory
hal_client_domain(mtk_hal_gpu, hal_allocator)

# Purpose : Allow to use kernel driver
allow mtk_hal_gpu graphics_device:chr_file rw_file_perms;

allow mtk_hal_gpu proc_ged:file rw_file_perms;
allowxperm mtk_hal_gpu proc_ged:file ioctl { proc_ged_ioctls };

allow mtk_hal_gpu hal_graphics_allocator_default:fd use;
allow mtk_hal_gpu ion_device:chr_file r_file_perms;
allow mtk_hal_gpu debugfs_ion:dir search;

allow mtk_hal_gpu merged_hal_service:fd use;
