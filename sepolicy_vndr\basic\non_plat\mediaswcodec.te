# ==============================================
# Common SEPolicy Rule
# ==============================================

# Date : WK19.25
# Operation : Migration
# Purpose : [ALPS04669482] DRTS failed due to avc denied
allow mediaswcodec debugfs_ion:dir rw_dir_perms;
allow mediaswcodec gpu_device:dir rw_dir_perms;
allow mediaswcodec gpu_device:chr_file rw_file_perms;
allow mediaswcodec dri_device:chr_file rw_file_perms;

# Date : WK20.38
# Purpose: Allow to access ged for gralloc_extra functions
allow mediaswcodec proc_ged:file rw_file_perms;
allowxperm mediaswcodec proc_ged:file ioctl { proc_ged_ioctls };

unix_socket_connect(mediaswcodec, logdr, logd)
