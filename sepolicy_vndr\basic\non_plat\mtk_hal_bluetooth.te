# ==============================================
# Common SEPolicy Rule
# ==============================================
type mtk_hal_bluetooth, domain;
type mtk_hal_bluetooth_exec, exec_type, vendor_file_type, file_type;

init_daemon_domain(mtk_hal_bluetooth)

wakelock_use(mtk_hal_bluetooth)

hal_server_domain(mtk_hal_bluetooth, hal_bluetooth)

# call into the Bluetooth process (callbacks)
binder_call(mtk_hal_bluetooth, bluetooth)

# bluetooth factory file accesses.
r_dir_file(mtk_hal_bluetooth, bluetooth_efs_file)

allow mtk_hal_bluetooth { uhid_device hci_attach_dev }:chr_file rw_file_perms;

# sysfs access.
allow mtk_hal_bluetooth sysfs_bluetooth_writable:file rw_file_perms;
allow mtk_hal_bluetooth self:capability2 wake_alarm;

# Allow write access to bluetooth-specific properties
set_prop(mtk_hal_bluetooth, bluetooth_prop)

# /proc access (bluesleep etc.).
allow mtk_hal_bluetooth proc_bluetooth_writable:file rw_file_perms;

# VTS tests need to be able to toggle rfkill
allow mtk_hal_bluetooth self:capability net_admin;

# Purpose : Set to access stpbt driver & NVRAM
allow mtk_hal_bluetooth stpbt_device:chr_file rw_file_perms;

allow mtk_hal_bluetooth nvdata_file:dir search;
allow mtk_hal_bluetooth nvdata_file:file rw_file_perms;
allow mtk_hal_bluetooth nvdata_file:lnk_file r_file_perms;
allow mtk_hal_bluetooth nvram_data_file:lnk_file r_file_perms;

# Purpose: Allow to search /mnt/vendor/* for fstab when using NVM_Init()
allow mtk_hal_bluetooth mnt_vendor_file:dir search;

# Purpose: Allow BT Driver to insmod
set_prop(mtk_hal_bluetooth, vendor_mtk_wmt_prop)

# Date :  2019/10/30
# Operation : get bt fw branch info, set to property for eng mode
# Purpose: get bt fw branch info, set to property for eng mode
allow mtk_hal_bluetooth proc_btdbg:file rw_file_perms;

# Date :  2019/12/03
# Operation : ability to enable bt driver thread as RT priority
# Purpose: ability to enable bt driver thread as RT priority
allow mtk_hal_bluetooth kernel:process setsched;

# Date :  2021/04/27
# Allow ReadDefaultFstab().
read_fstab(mtk_hal_bluetooth)

