# ==============================================
# Policy File of /vendor/bin/rild Executable File

# ==============================================
# Common SEPolicy Rule
# ==============================================
# Access to wake locks
wakelock_use(rild)

#Date : W17.21
#Purpose: Grant permission to access binder dev node
vndbinder_use(rild)

# Trigger module auto-load.
allow rild kernel:system module_request;

# Capabilities assigned for rild
allow rild self:capability { setuid net_admin net_raw };

# Control cgroups
allow rild cgroup:dir create_dir_perms;

# Property service
# allow set RIL related properties (radio./net./system./etc)
set_prop(rild, vendor_mtk_ril_active_md_prop)

# allow set muxreport control properties
set_prop(rild, vendor_mtk_ril_cdma_report_prop)
set_prop(rild, vendor_mtk_ril_mux_report_case_prop)
set_prop(rild, vendor_mtk_ctl_muxreport-daemon_prop)

# Allow access permission to efs files
allow rild efs_file:dir create_dir_perms;
allow rild efs_file:file create_file_perms;
allow rild bluetooth_efs_file:file r_file_perms;
allow rild bluetooth_efs_file:dir r_dir_perms;

# Allow access permission to dir/files
# (radio data/system data/proc/etc)
allow rild sdcardfs:dir r_dir_perms;
allow rild proc_net:file w_file_perms;

# Allow rild to create and use netlink sockets.
# Set and get routes directly via netlink.
allow rild self:netlink_route_socket nlmsg_write;

# Allow read/write to devices/files
allow rild mtk_radio_device:dir search;
allow rild radio_device:chr_file rw_file_perms;
allow rild radio_device:blk_file r_file_perms;
allow rild mtd_device:dir search;

# Allow read/write to tty devices
allow rild tty_device:chr_file rw_file_perms;
allow rild eemcs_device:chr_file rw_file_perms;

allow rild devmap_device:chr_file r_file_perms;
allow rild devpts:chr_file rw_file_perms;
allow rild ccci_device:chr_file rw_file_perms;
allow rild misc_device:chr_file rw_file_perms;
allow rild proc_lk_env:file rw_file_perms;
allow rild sysfs_vcorefs_pwrctrl:file w_file_perms;
allow rild para_block_device:blk_file rw_file_perms;

# Allow dir search, fd uses
allow rild block_device:dir search;
allow rild platform_app:fd use;
allow rild radio:fd use;

# For MAL MFI
allow rild mal_mfi_socket:sock_file w_file_perms;

# For ccci sysfs node
allow rild sysfs_ccci:dir search;
allow rild sysfs_ccci:file r_file_perms;

#Dat: 2017/03/27
#Purpose: allow set telephony Sensitive property
set_prop(rild, vendor_mtk_telephony_sensitive_prop)

set_prop(rild,vendor_mtk_md_prop)

# For AGPSD
allow rild mtk_agpsd:unix_stream_socket connectto;

#Date: 2017/12/6
#Purpose: allow set the RS times for /proc/sys/net/ipv6/conf/ccmniX/router_solicitations
allow rild vendor_shell_exec:file x_file_perms;
allow rild vendor_toolbox_exec:file x_file_perms;

# Date : WK18.16
# Operation: P migration
# Purpose: Allow rild to get vendor_mtk_tel_switch_prop
get_prop(rild, vendor_mtk_tel_switch_prop)

#Date: W1817
#Purpose: allow rild access property of vendor_mtk_radio_prop
set_prop(rild, vendor_mtk_radio_prop)

#Date : W18.21
#Purpose: allow rild access to vendor.ril.ipo system property
set_prop(rild, vendor_mtk_ril_ipo_prop)

# Date : WK18.26
# Operation: P migration
# Purpose: Allow carrier express HIDL to set vendor property
set_prop(rild, vendor_mtk_cxp_vendor_prop)
allow rild mnt_vendor_file:dir search;
allow rild mnt_vendor_file:file create_file_perms;
allow rild nvdata_file:dir create_dir_perms;
allow rild nvdata_file:file create_file_perms;

#Date : W18.29
#Purpose: allow rild access binder to mtk_hal_secure_element
allow rild mtk_hal_secure_element:binder call;

# Date : WK18.31
# Operation: P migration
# Purpose: Allow supplementary service HIDL to set vendor property
set_prop(rild, vendor_mtk_ss_vendor_prop)

# Date : 2018/2/27
# Purpose : for NVRAM recovery mechanism
set_prop(rild, powerctl_prop)

# Date: 2019/06/14
# Operation : Migration
allow rild proc_cmdline:file r_file_perms;

# Date: 2019/07/18
# Operation: AP wifi path
# Purpose: Allow packet can be filtered by RILD process
allow rild self:netlink_netfilter_socket { create_socket_perms_no_ioctl };

# Date : WK19.43
# Purpose: Allow wfc module from rild read system property from wfc module
get_prop(rild, vendor_mtk_wfc_serv_prop)

# Date: 2019/11/15
# Operation: RILD init flow
# Purpose: To handle illegal rild started
set_prop(rild, vendor_mtk_gsm0710muxd_prop)

# Date : 2019/10/29
# Operation: imstestmode
# Purpose: Allow HIDL to set vendor property
set_prop(rild, vendor_mtk_imstestmode_prop)

# Date : 2020/06/11
# Operation: R migration
# Purpose: Allow rild to get system_boot_reason_prop
get_prop(rild, system_boot_reason_prop)

# rild Bringup Policy
allow rild mtkrild:unix_stream_socket connectto;
set_prop(rild, radio_prop)

# Allow the socket read/write of netd for rild
allow rild netd_socket:sock_file { write read };

#Date : W17.20
#Purpose: allow access to audio hal
binder_call(rild, hal_audio_default)
hal_client_domain(rild, hal_audio)

# Date : W19.16
# Operation: Q migration
# Purpose: Allow rild access to send SUPL INIT to mnld
allow rild mnld:unix_dgram_socket sendto;

# Date : W19.35
# Operation: Q migration
# Purpose: Fix rilproxy SeLinux warning of pre-defined socket
allow rild gsmrild_socket:sock_file w_file_perms;

# Date: 2021/02/03
# Operation: for Gen98 RILD dev
# Allow read/write to devices/files
allow rild gsm0710muxd_device:chr_file rw_file_perms;

# Date : 2021/08/27
# Purpose: Allow rild to access ccci wifi proxy
allow rild ccci_wifi_proxy_device:chr_file rw_file_perms;

# Date: 2021/09/26
# Purpose: Add permission for vilte
allow rild ccci_vts_device:chr_file rw_file_perms_no_map;

# Date: 2022/11/30
# Purpose: Allow access to nvram hal
hal_client_domain(rild, hal_mtk_nvramagent)
