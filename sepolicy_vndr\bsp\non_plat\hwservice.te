type mtk_hal_wfo_hwservice, hwservice_manager_type;
type volte_uce_ua_hwservice, hwservice_manager_type;
type mtk_hal_videotelephony_hwservice, hwservice_manager_type;
type mtk_hal_codecservice_hwservice, hwservice_manager_type;
type mtk_hal_netdagent_hwservice, hwservice_manager_type;
type volte_rcs_ua_hwservice, hwservice_manager_type;
type mtk_hal_dfps_hwservice, hwservice_manager_type;
type mtk_hal_dplanner_hwservice, hwservice_manager_type;
type mtk_hal_keyinstall_hwservice, hwservice_manager_type;
type mtk_hal_pplagent_hwservice, hwservice_manager_type;
# omadm hidl
type mtk_hal_omadm_hwservice, hwservice_manager_type;
# DMC HIDL
type mtk_hal_dmc_hwservice, hwservice_manager_type;
# APM HIDL
type mtk_hal_apm_hwservice, hwservice_manager_type;
# nwk opt HIDL
type mtk_hal_nwk_opt_hwservice, hwservice_manager_type;
# touchll HIDL
type mtk_hal_touchll_hwservice, hwservice_manager_type;
# thp
type mtk_hal_thp_hwservice, hwservice_manager_type;

# MICROTRUST SEPolicy Rule
# microtrust THH service manager type
type teei_hal_thh_hwservice, hwservice_manager_type;

# microtrust TUI service manager type
type teei_hal_tui_hwservice, hwservice_manager_type;

# microtrust IFAA service manager type
type teei_hal_ifaa_hwservice, hwservice_manager_type;

# microtrust CAPI service manager type
type teei_hal_capi_hwservice, hwservice_manager_type;

# microtrust WECHAT service manager type
type teei_hal_wechat_hwservice, hwservice_manager_type;

# Trustonic SEPolicy Rule
type hal_tee_hwservice, hwservice_manager_type;
type hal_teeregistry_hwservice, hwservice_manager_type;

# Date : 2019/05/16
# Operation : IT
# Purpose : Add for HIDL service
type mtk_mdm_hidl_server, hwservice_manager_type;


#client API HIDL
type volte_clientapi_ua_hwservice, hwservice_manager_type;

# ==============================================
# HDCP SEPolicy Rule
# ==============================================
type tesiai_hal_hdcp_hwservice, hwservice_manager_type;
