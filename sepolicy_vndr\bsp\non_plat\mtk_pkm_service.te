# ==============================================
# Policy File of /vendor/bin/mtk_pkm_service Executable File

# ==============================================
# Common SEPolicy Rule
# ==============================================

type mtk_pkm_service_exec ,exec_type, file_type, vendor_file_type;

init_daemon_domain(mtk_pkm_service)

# Date : W1920
# Operation : Diagnostic framework Q migration
# Purpose : allow mtk_pkm_service to send KPI through APM service
hal_client_domain(mtk_pkm_service, hal_mtk_apm)

# Purpose : for mtk_pkm_service to connenct to md_monitor
hal_client_domain(mtk_pkm_service, md_monitor_hal)

# Purpose : for mtk_pkm_service to access /data/md_mon/
allow mtk_pkm_service md_monitor_vendor_file:dir r_dir_perms;
allow mtk_pkm_service md_monitor_vendor_file:file r_file_perms;

# Purpose : Allow mtk_pkm_service to get properties
# For PKM to know PDN status
# ro.vendor.md_auto_setup_ims
get_prop(mtk_pkm_service, vendor_mtk_ims_prop)
# vendor.ims.eims.pdn.info
get_prop(mtk_pkm_service, vendor_mtk_ims_eims_pdn_prop)
# vendor.ril.data.pdn_info*
get_prop(mtk_pkm_service, vendor_mtk_radio_prop)

# Purpose : Allow mtk_pkm_service to pull packet from netd
allow mtk_pkm_service self:capability net_raw;
allow mtk_pkm_service self:packet_socket { create_socket_perms };
allow mtk_pkm_service self:udp_socket { create_socket_perms };
allowxperm mtk_pkm_service self:packet_socket ioctl {SIOCGIFINDEX SIOCGSTAMP };
allowxperm mtk_pkm_service self:udp_socket ioctl {SIOCGIFINDEX SIOCGSTAMP };

# Add policy read property for init.svc.md_monitor
get_prop(mtk_pkm_service, system_mtk_init_svc_md_monitor_prop)

# Allow PKM service to read vendor.dmc.apm.active
get_prop(mtk_pkm_service, vendor_mtk_dmc_prop)
