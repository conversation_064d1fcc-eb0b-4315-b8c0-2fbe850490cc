# ==============================================
# Policy File of /vendor/bin/statusd Executable File

# ==============================================
# Common SEPolicy Rule
# =============================================

type statusd_exec, exec_type, file_type, vendor_file_type;
typeattribute statusd mtkimsapdomain;

init_daemon_domain(statusd)

# Dat: 2017/02/14
# Purpose: allow set telephony Sensitive property
set_prop(statusd, vendor_mtk_telephony_sensitive_prop)

allow statusd block_device:dir search;
allow statusd flashlessd_exec:file rx_file_perms;
set_prop(statusd, vendor_mtk_md_prop)
set_prop(statusd, vendor_mtk_net_cdma_mdmstat_prop)

allow statusd nvram_data_file:dir create_dir_perms;
allow statusd nvram_data_file:file create_file_perms;
allow statusd nvram_data_file:lnk_file read;
allow statusd nvdata_file:lnk_file read;
allow statusd nvdata_file:dir create_dir_perms;
allow statusd nvdata_file:file create_file_perms;
allow statusd nvram_device:chr_file rw_file_perms;
allow statusd nvram_device:blk_file rw_file_perms;

allow statusd nvdata_device:blk_file { read write open };
set_prop(statusd, vendor_mtk_ril_cdma_report_prop)
allow statusd self:capability net_admin;
allow statusd self:udp_socket { create ioctl };
allow statusd statusd_socket:sock_file { write setattr };
allow statusd sysfs_wake_lock:file { read write open };

allow statusd c2k_file:dir create_dir_perms;
allow statusd c2k_file:file create_file_perms;
allow statusd ttyMT_device:chr_file { read write ioctl open };
allow statusd ttySDIO_device:chr_file { read write open setattr ioctl};
allow statusd viarild_exec:file rx_file_perms;
allow statusd vmodem_device:chr_file { read write open setattr ioctl};

# property service
set_prop(statusd, vendor_mtk_ril_mux_report_case_prop)
set_prop(statusd, vendor_mtk_cdma_prop)

# Search permission for findPidByName
allow statusd domain:dir search;

# N bringup: viarild is lunched by Statusd, should add the following permission to Status.
allow statusd devpts:chr_file rw_file_perms;

# Andorid O : Add permission to statusd.
allowxperm statusd self:udp_socket ioctl {SIOCDELRT SIOCSIFFLAGS SIOCSIFADDR SIOCKILLADDR SIOCDEVPRIVATE SIOCDEVPRIVATE_1};
allow statusd sysfs_ccci:dir search;
allow statusd sysfs_ccci:file r_file_perms;
allow statusd vndbinder_device:chr_file r_file_perms;
