# ==============================================
# Policy File of /vendor/app/emcamera Executable File

# ==============================================
# Common SEPolicy Rule
# ==============================================

app_domain(emcamera_app)

# Date: WK1931
# Purpose:  write data/vendor/camera_dump
allow emcamera_app vendor_camera_dump_file:dir create_dir_perms;
allow emcamera_app vendor_camera_dump_file:file create_file_perms;

allow emcamera_app cameraserver_service:service_manager find;
allow emcamera_app activity_service:service_manager find;
allow emcamera_app surfaceflinger_service:service_manager find;
allow emcamera_app activity_task_service:service_manager find;
allow emcamera_app audio_service:service_manager find;
allow emcamera_app trust_service:service_manager find;
allow emcamera_app device_policy_service:service_manager find;
allow emcamera_app autofill_service:service_manager find;
allow emcamera_app sensorservice_service:service_manager find;

# Date: WK1931
# Purpose:  set em camera properties
set_prop(emcamera_app, vendor_mtk_em_prop)
set_prop(emcamera_app, vendor_mtk_emcamera_prop)
set_prop(emcamera_app, vendor_mtk_mediatek_prop)
