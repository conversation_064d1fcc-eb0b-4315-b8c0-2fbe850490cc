# ==============================================
# Common SEPolicy Rule
# ==============================================

# HwBinder IPC from clients into server, and callbacks
binder_call(hal_rcs_client, hal_rcs_server)
binder_call(hal_rcs_server, hal_rcs_client)

add_hwservice(hal_rcs_server, volte_rcs_ua_hwservice)

# give permission for hal client
allow hal_rcs_client volte_rcs_ua_hwservice:hwservice_manager find;

