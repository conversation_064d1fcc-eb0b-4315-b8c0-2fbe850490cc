##
# Trustonic TeeService
#

type hal_teeregistry_default_exec, exec_type, vendor_file_type, file_type;

hal_server_domain(hal_teeregistry_default, hal_teeregistry)

hal_client_domain(hal_teeregistry_default, hal_allocator)

# Access to TEE driver nodes (user and admin)
allow hal_teeregistry_default mobicore_user_device:chr_file rw_file_perms;
allow hal_teeregistry_default mobicore_admin_device:chr_file rw_file_perms;

# Registry need to be accessed by the HAL OTAv1

allow hal_teeregistry_default mobicore_data_file:dir { rw_dir_perms create rename rmdir };
allow hal_teeregistry_default mobicore_data_file:file { rw_file_perms rename create };
allow hal_teeregistry_default mobicore_vendor_file:file { r_file_perms };

init_daemon_domain(hal_teeregistry_default);
