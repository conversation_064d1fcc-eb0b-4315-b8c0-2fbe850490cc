# ==============================================
# Common SEPolicy Rule
# ==============================================

# Date : WK14.31
# Operation : Migration
# Purpose : camera devices access.
allow mediaserver camera_isp_device:chr_file rw_file_perms;
allow mediaserver ccu_device:chr_file rw_file_perms;
allow mediaserver vpu_device:chr_file rw_file_perms;
allow mediaserver mdla_device:chr_file rw_file_perms;
allow mediaserver apusys_device:chr_file rw_file_perms;
allow mediaserver sysfs_apusys_queue:dir r_dir_perms;
allow mediaserver sysfs_apusys_queue:file r_file_perms;
allow mediaserver kd_camera_hw_device:chr_file rw_file_perms;
allow mediaserver seninf_device:chr_file rw_file_perms;
allow mediaserver self:capability { setuid ipc_lock sys_nice net_admin };
allow mediaserver sysfs_wake_lock:file rw_file_perms;
allow mediaserver MTK_SMI_device:chr_file r_file_perms;
allow mediaserver camera_pipemgr_device:chr_file r_file_perms;
allow mediaserver kd_camera_flashlight_device:chr_file rw_file_perms;
allow mediaserver lens_device:chr_file rw_file_perms;

# Date : WK14.32
# Operation : Migration
# Purpose : Set audio driver permission to access SD card for debug purpose and accss NVRam.
allow mediaserver sdcard_type:dir create_dir_perms;
allow mediaserver sdcard_type:file create_file_perms;
allow mediaserver nvram_data_file:lnk_file read;
allow mediaserver nvdata_file:lnk_file read;

# Date : WK14.34
# Operation : Migration
# Purpose : nvram access (dumchar case for nand and legacy chip)
allow mediaserver nvram_device:chr_file rw_file_perms;

# Date : WK14.36
# Operation : Migration
# Purpose : media server and bt process communication for A2DP data.and other control flow
allow mediaserver bluetooth:unix_dgram_socket sendto;
allow mediaserver bt_a2dp_stream_socket:sock_file write;
allow mediaserver bt_int_adp_socket:sock_file write;

# Date : WK14.37
# Operation : Migration
# Purpose : camera ioctl
allow mediaserver camera_sysram_device:chr_file r_file_perms;

# Date : WK14.36
# Operation : Migration
# Purpose : VDEC/VENC device node
allow mediaserver Vcodec_device:chr_file rw_file_perms;

# Date : WK14.36
# Operation : Migration
# Purpose :  access nvram, otp, ccci cdoec devices.
allow mediaserver ccci_device:chr_file rw_file_perms;
allow mediaserver eemcs_device:chr_file rw_file_perms;
allow mediaserver devmap_device:chr_file r_file_perms;
allow mediaserver ebc_device:chr_file rw_file_perms;
allow mediaserver nvram_device:blk_file rw_file_perms;
allow mediaserver bootdevice_block_device:blk_file rw_file_perms;

# Date : WK14.36
# Operation : Migration
# Purpose : for SW codec VP/VR
allow mediaserver mtk_sched_device:chr_file rw_file_perms;

# Date : WK14.38
# Operation : Migration
# Purpose : FM driver access
allow mediaserver fm_device:chr_file rw_file_perms;

# Data : WK14.38
# Operation : Migration
# Purpose : for VP/VR
allow mediaserver FM50AF_device:chr_file rw_file_perms;
allow mediaserver AD5820AF_device:chr_file rw_file_perms;
allow mediaserver DW9714AF_device:chr_file rw_file_perms;
allow mediaserver DW9814AF_device:chr_file rw_file_perms;
allow mediaserver AK7345AF_device:chr_file rw_file_perms;
allow mediaserver DW9714A_device:chr_file rw_file_perms;
allow mediaserver LC898122AF_device:chr_file rw_file_perms;
allow mediaserver LC898212AF_device:chr_file rw_file_perms;
allow mediaserver BU6429AF_device:chr_file rw_file_perms;
allow mediaserver DW9718AF_device:chr_file rw_file_perms;
allow mediaserver BU64745GWZAF_device:chr_file rw_file_perms;
allow mediaserver MAINAF_device:chr_file rw_file_perms;
allow mediaserver MAIN2AF_device:chr_file rw_file_perms;
allow mediaserver MAIN3AF_device:chr_file rw_file_perms;
allow mediaserver MAIN4AF_device:chr_file rw_file_perms;
allow mediaserver SUBAF_device:chr_file rw_file_perms;
allow mediaserver SUB2AF_device:chr_file rw_file_perms;

# Data : WK14.38
# Operation : Migration
# Purpose : for boot animation.
binder_call(mediaserver, bootanim)
binder_call(mediaserver, mtkbootanimation)

# Date : WK14.39
# Operation : Migration
# Purpose : FDVT Driver
allow mediaserver camera_fdvt_device:chr_file rw_file_perms;

# Date : WK14.40
# Operation : Migration
# Purpose : HDMI driver access
allow mediaserver graphics_device:chr_file rw_file_perms;

# Date : WK14.40
# Operation : Migration
# Purpose : Smartpa
allow mediaserver smartpa_device:chr_file rw_file_perms;

# Data : WK14.40
# Operation : Migration
# Purpose : permit 'call' by audio tunning tool audiocmdservice_atci
binder_call(mediaserver, audiocmdservice_atci)

# Date : WK14.40
# Operation : Migration
# Purpose : mtk_jpeg
allow mediaserver mtk_jpeg_device:chr_file r_file_perms;

# Date : WK14.41
# Operation : Migration
# Purpose : WFD HID Driver
allow mediaserver uhid_device:chr_file rw_file_perms;

# Date : WK14.41
# Operation : Migration
# Purpose : Camera EEPROM Calibration
allow mediaserver CAM_CAL_DRV_device:chr_file rw_file_perms;
allow mediaserver CAM_CAL_DRV1_device:chr_file rw_file_perms;
allow mediaserver CAM_CAL_DRV2_device:chr_file rw_file_perms;
allow mediaserver camera_eeprom_device:chr_file rw_file_perms;
allow mediaserver seninf_n3d_device:chr_file rw_file_perms;

# Date : WK14.43
# Operation : Migration
# Purpose : VOW
allow mediaserver vow_device:chr_file rw_file_perms;

# Date: WK14.44
# Operation : Migration
# Purpose : EVDO
allow mediaserver rpc_socket:sock_file write;
allow mediaserver ttySDIO_device:chr_file rw_file_perms;

# Data: WK14.44
# Operation : Migration
# Purpose : VP
allow mediaserver surfaceflinger:file getattr;

# Data: WK14.44
# Operation : Migration
# Purpose : for low SD card latency issue
allow mediaserver sysfs_lowmemorykiller:file r_file_perms;

# Data: WK14.45
# Operation : Migration
# Purpose : for change thermal policy when needed
allow mediaserver proc_mtkcooler:dir search;
allow mediaserver proc_mtktz:dir search;
allow mediaserver proc_thermal:dir search;

# Date : WK14.46
# Operation : Migration
# Purpose : for MTK Emulator HW GPU
allow mediaserver qemu_pipe_device:chr_file rw_file_perms;

# Date : WK14.46
# Operation : Migration
# Purpose : for camera init
allow mediaserver system_server:unix_stream_socket rw_socket_perms_no_ioctl;

# Data : WK14.46
# Operation : Migration
# Purpose : for SMS app
allow mediaserver radio_data_file:dir search;
allow mediaserver radio_data_file:file open;

# Data : WK14.47
# Operation : Audio playback
# Purpose : Music as ringtone
allow mediaserver radio:dir r_dir_perms;
allow mediaserver radio:file r_file_perms;

# Data : WK14.47
# Operation : CTS
# Purpose : cts search strange app
allow mediaserver untrusted_app:dir search;

# Date : WK15.03
# Operation : Migration
# Purpose : offloadservice
allow mediaserver offloadservice_device:chr_file rw_file_perms;

# Date : WK15.32
# Operation : Pre-sanity
# Purpose : 3A algorithm need to access sensor service
allow mediaserver sensorservice_service:service_manager find;

# Date : WK15.34
# Operation : Migration
# Purpose: for camera middleware dump image buffer to sdcard & audio frameworks dump
allow mediaserver storage_file:lnk_file rw_file_perms;
allow mediaserver mnt_user_file:dir rw_dir_perms;
allow mediaserver mnt_user_file:lnk_file rw_file_perms;

# Date : WK15.35
# Operation : Migration
# Purpose: Allow mediaserver to read binder from surfaceflinger
allow mediaserver surfaceflinger:fifo_file rw_file_perms;

# Date : WK15.46
# Operation : Migration
# Purpose : DPE Driver
allow mediaserver camera_dpe_device:chr_file rw_file_perms;

# Date : WK15.46
# Operation : Migration
# Purpose : TSF Driver
allow mediaserver camera_tsf_device:chr_file rw_file_perms;

# Date : WK16.32
# Operation : N Migration
# Purpose : RSC Driver
allow mediaserver camera_rsc_device:chr_file rw_file_perms;

# Date : WK16.33
# Purpose: Allow to access ged for gralloc_extra functions
allow mediaserver proc_ged:file rw_file_perms;
allowxperm mediaserver proc_ged:file ioctl { proc_ged_ioctls };

# Date : WK16.33
# Operation : N Migration
# Purpose : GEPF Driver
allow mediaserver camera_gepf_device:chr_file rw_file_perms;

# Date : WK16.35
# Operation : Migration
# Purpose : Update camera flashlight driver device file
allow mediaserver flashlight_device:chr_file rw_file_perms;

# Date : WK16.43
# Operation : N Migration
# Purpose : WPE Driver
allow mediaserver camera_wpe_device:chr_file rw_file_perms;
allow mediaserver gpu_device:dir search;
allow mediaserver sw_sync_device:chr_file rw_file_perms;

# Date : WK17.19
# Operation : N Migration
# Purpose : OWE Driver
allow mediaserver camera_owe_device:chr_file rw_file_perms;

# Date : WK17.30
# Operation : O Migration
# Purpose: Allow to access cmdq driver
allow mediaserver mtk_cmdq_device:chr_file r_file_perms;
allow mediaserver mtk_mdp_device:chr_file r_file_perms;
allow mediaserver mtk_mdp_sync_device:chr_file r_file_perms;
hal_client_domain(mediaserver, hal_mtk_mms)

# Date : WK17.43
# Operation : Migration
# Purpose : DISP access
allow mediaserver graphics_device:dir search;

# Date : WK17.44
# Operation : Migration
# Purpose : DIP Driver
allow mediaserver camera_dip_device:chr_file rw_file_perms;

# Date : WK17.44
# Operation : Migration
# Purpose : MFB Driver
allow mediaserver camera_mfb_device:chr_file rw_file_perms;

# Date : WK17.49
# Operation : MT6771 SQC
# Purpose : Allow permgr access
allow mediaserver proc_perfmgr:dir r_dir_perms;
allow mediaserver proc_perfmgr:file r_file_perms;
allowxperm mediaserver proc_perfmgr:file ioctl {
 PERFMGR_FPSGO_DEQUEUE
 PERFMGR_FPSGO_QUEUE_CONNECT
 PERFMGR_FPSGO_QUEUE
 PERFMGR_FPSGO_BQID
};

# Date : WK18.18
# Operation : Migration
# Purpose : wifidisplay hdcp
# DRM Key Manage HIDL
binder_call(mediaserver, mtk_hal_keymanage)

# Date : WK21.25
# Operation : Migration
# Purpose : PDA Driver
allow mediaserver camera_pda_device:chr_file rw_file_perms;

# Purpose : Allow mediadrmserver  to call vendor.mediatek.hardware.keymanage@1.0-service.
hal_client_domain(mediaserver, hal_keymaster)
hal_client_domain(mediaserver, hal_power)

allow mediaserver vpud_device:chr_file rw_file_perms;

hal_client_domain(mediaserver, hal_mtkcodecservice)
