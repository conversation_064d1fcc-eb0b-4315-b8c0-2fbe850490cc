# ==============================================================================
# Type Declaration
# ==============================================================================
type merged_hal_service, domain;
type merged_hal_service_exec, exec_type, file_type, vendor_file_type;

# ==============================================
# Common SEPolicy Rule
# ==============================================

init_daemon_domain(merged_hal_service)

hal_server_domain(merged_hal_service, hal_vibrator)
hal_server_domain(merged_hal_service, hal_light)
hal_server_domain(merged_hal_service, hal_power)
hal_server_domain(merged_hal_service, hal_thermal)
hal_server_domain(merged_hal_service, hal_memtrack)

#mtk libs_hidl_service permissions
hal_server_domain(merged_hal_service, hal_mtk_lbs)

vndbinder_use(merged_hal_service)
unix_socket_connect(merged_hal_service, agpsd, mtk_agpsd)
allow merged_hal_service mtk_agpsd:unix_dgram_socket sendto;

#mtk_gnss permissions
hal_server_domain(merged_hal_service, hal_gnss)
allow merged_hal_service mnld_data_file:sock_file create_file_perms;
allow merged_hal_service mnld_data_file:dir create_dir_perms;
allow merged_hal_service mnld:unix_dgram_socket sendto;

#graphics allocator permissions
hal_server_domain(merged_hal_service, hal_graphics_allocator)
allow merged_hal_service gpu_device:dir search;
allow merged_hal_service sw_sync_device:chr_file rw_file_perms;
allow merged_hal_service debugfs_tracing:file w_file_perms;

#for ape hidl permissions
hal_server_domain(merged_hal_service, hal_mtk_codecservice)
hal_client_domain(merged_hal_service, hal_allocator)

#for default drm permissions
hal_server_domain(merged_hal_service, hal_drm)
allow merged_hal_service mediacodec:fd use;
allow merged_hal_service { appdomain -isolated_app_all }:fd use;

# Date : WK18.23
# Operation : P Migration
# Purpose : add grant permission for Thermal HAL mtktz and proc
allow merged_hal_service proc_mtktz:dir search;
allow merged_hal_service proc_mtktz:file r_file_perms;
allow merged_hal_service proc_stat:file r_file_perms;

#for uevent handle
allow merged_hal_service self:netlink_kobject_uevent_socket create_socket_perms_no_ioctl;

#for thermal sysfs
allow merged_hal_service sysfs_therm:file rw_file_perms;
allow merged_hal_service sysfs_therm:dir search;


# Date : WK19.11
# Operation : Q Migration
allowxperm merged_hal_service proc_ged:file ioctl { proc_ged_ioctls };

# Date: 2019/06/14
# Operation : Migration
hal_client_domain(merged_hal_service, hal_mtk_nvramagent)
