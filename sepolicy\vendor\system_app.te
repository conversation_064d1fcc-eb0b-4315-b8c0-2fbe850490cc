r_dir_file(system_app, vendor_sysfs_graphics)
r_dir_file(system_app, vendor_sysfs_usb_supply)
allow system_app vendor_sysfs_graphics:file rw_file_perms;
allow system_app vendor_sysfs_usb_supply:file rw_file_perms;
allow system_app vendor_sysfs_otg_switch:file rw_file_perms;
allow system_app vendor_sysfs_battery_supply:dir r_dir_perms;
allow system_app vendor_sysfs_battery_supply:file rw_file_perms;
allow system_app vendor_proc_display:dir r_dir_perms;
allow system_app vendor_proc_display:file rw_file_perms;

# Allow system_app to read thermals
r_dir_file(system_app, sysfs_therm)

# Allow system_app to read USB UVC property
get_prop(system_app, vendor_usb_uvc_enabled_prop)
