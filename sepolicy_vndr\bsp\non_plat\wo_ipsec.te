# ==============================================
# Policy File of /vendor/bin/wo_ipsec Executable File

# ==============================================
# Common SEPolicy Rule
# ==============================================

type wo_ipsec_exec, exec_type, file_type, vendor_file_type;

net_domain(wo_ipsec)

domain_auto_trans(wo_ipsec, netutils_wrapper_exec, netutils_wrapper)

# Purpose :  access xfrm
allow wo_ipsec proc_net:file w_file_perms;

# Purpose :  send command to epdg_wod
allow wo_ipsec wo_epdg_ipsec_socket:sock_file write;

# Purpose :  create socket for IKEv2 protocol
allow wo_ipsec node:udp_socket node_bind;
allow wo_ipsec port:tcp_socket name_connect;
allow wo_ipsec port:udp_socket name_bind;

# Purpose :  Query DNS address
allow wo_ipsec netd:unix_stream_socket connectto;
allow wo_ipsec dnsproxyd_socket:sock_file write;

# Purpose :  access socket of wod and property
allow wo_ipsec wo_epdg_client:unix_stream_socket { read write connectto };

# Purpose :  output to /dev/null
allow wo_ipsec wo_epdg_client:fd use;

# Purpose :  starter invoke charon
allow wo_ipsec wo_charon_exec:file execute_no_trans;

# Purpose :  charon set fwmark
allow wo_ipsec fwmarkd_socket:sock_file write;

# Purpose :  send/receive packet to/from peer
allow wo_ipsec self:tcp_socket { write getattr connect read getopt create };
allow wo_ipsec self:udp_socket { write bind create read setopt };

# Purpose :  kernel ip/route operations
allow wo_ipsec self:netlink_route_socket { write nlmsg_write read bind create nlmsg_read };
allow wo_ipsec self:netlink_xfrm_socket { write bind create read nlmsg_write nlmsg_read };

# Purpose :  charon read certs
allow wo_ipsec custom_file:dir r_dir_perms;
allow wo_ipsec custom_file:file r_file_perms;

# Purpose : set alarm for DPD
allow wo_ipsec self:capability2 wake_alarm;

allow wo_ipsec devpts:chr_file rw_file_perms;

allow wo_ipsec proc_modules:file r_file_perms;
allow wo_ipsec vendor_shell_exec:file rx_file_perms;;
allow wo_ipsec netd_socket:sock_file write;

allow wo_ipsec vendor_toolbox_exec:file x_file_perms;
allow wo_ipsec kernel:system module_request;

