# ==============================================
# Policy File of /vendor/bin/ipsec_mon  Executable File

# ==============================================
# Common SEPolicy Rule
# ==============================================

type ipsec_mon_exec, exec_type, file_type, vendor_file_type;

init_daemon_domain(ipsec_mon)

net_domain(ipsec_mon)

allow ipsec_mon self:netlink_xfrm_socket { write bind create read nlmsg_read nlmsg_write};
allow ipsec_mon ims_ipsec_data_file:dir w_dir_perms;
allow ipsec_mon ims_ipsec_data_file:file create_file_perms;
allow ipsec_mon self:key_socket { write read create setopt };

# Date: W17.36
# Purpose: ipsec_mon fulfill 3x solution
allow ipsec_mon self:capability { net_admin net_raw };
allow ipsec_mon self:udp_socket { create ioctl };
allow ipsec_mon self:netlink_route_socket { write read create nlmsg_read bind connect nlmsg_write};
allowxperm ipsec_mon self:udp_socket ioctl { SIOCDEVPRIVATE_2 };
allow ipsec_mon devpts:chr_file rw_file_perms;
allow ipsec_mon proc_net:file w_file_perms;

set_prop(ipsec_mon, vendor_mtk_network_prop)

allowxperm ipsec_mon self:udp_socket ioctl SIOCDEVPRIVATE;
dontaudit ipsec_mon kernel:system module_request;
