# ==============================================
# Common SEPolicy Rule
# ==============================================

# Date : WK14.34
# Operation : Migration
# Purpose :  For WIFI SANITY test to set FW path(STA/P2P/AP)
# Owner: TingTing Lei
allow netd wmtWifi_device:chr_file w_file_perms;

# Date : WK14.34
# Operation : Migration
# Purpose :  NA
# Owner: Changqing Sun
allow netd self:capability { fsetid setuid setgid };

# Date : WK14.34
# Operation : Migration
# Purpose: APP
allow netd platform_app:fd use;

# Date : WK14.37
# Operation : Migration
# Purpose :  PPPOE Test
# Owner : lina wang
allow netd ppp:process sigkill;

allow netd untrusted_app:fd use;

# Date : W15.02
# Operation :  SQC
# Purpose :  CTS for wifi
allow netd untrusted_app:unix_stream_socket rw_socket_perms_no_ioctl;
allow netd isolated_app_all:fd use;

# MTK support app feature
get_prop(netd, vendor_mtk_app_prop)


allow netd bip_ap:fd use;
allow netd bip_ap:tcp_socket { read write setopt getopt };
allow netd bip_ap:udp_socket {read write setopt getopt};
