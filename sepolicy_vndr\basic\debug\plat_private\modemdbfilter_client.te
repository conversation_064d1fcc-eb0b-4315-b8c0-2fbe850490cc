# ==============================================
# Policy File of /system/bin/modemdbfilter_client Executable File

# ==============================================
# Type Declaration
# ==============================================

type modemdbfilter_client_exec, exec_type, system_file_type, file_type;
typeattribute modemdbfilter_client coredomain;

# ==============================================
# Common SEPolicy Rule
# ==============================================

init_daemon_domain(modemdbfilter_client)

# Purpose : for create hidl client
hal_client_domain(modemdbfilter_client, hal_mtk_md_dbfilter)
allow modemdbfilter_client mddb_filter_data_file:dir { create_dir_perms relabelto };
allow modemdbfilter_client mddb_filter_data_file:file create_file_perms;
