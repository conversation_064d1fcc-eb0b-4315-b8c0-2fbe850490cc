# ==============================================
# Common SEPolicy Rule
# ==============================================
##########################
# Filesystem types
#
##########################
# Proc Filesystem types
#
type proc_secmem, fs_type, proc_type;
type proc_thermal, fs_type, proc_type;
type proc_mtkcooler, fs_type, proc_type;
type proc_mtktz, fs_type, proc_type;
type proc_mtd, fs_type, proc_type;
type proc_slogger, fs_type, proc_type;
type proc_lk_env, fs_type, proc_type;
type proc_ged, fs_type, proc_type;
type proc_mtk_jpeg, fs_type, proc_type, mlstrustedobject;
type proc_perfmgr, fs_type, proc_type;
type proc_wmtdbg, fs_type, proc_type;
type proc_zraminfo, fs_type, proc_type;
type proc_gpulog, fs_type, proc_type;
type proc_gpufreqv2, fs_type, proc_type;
type proc_sched_debug, fs_type, proc_type;
type proc_chip, fs_type, proc_type;
type proc_atf_log, fs_type, proc_type;
type proc_gz_log, fs_type, proc_type;
type proc_last_kmsg, fs_type, proc_type;
type proc_bootprof, fs_type, proc_type;
type proc_mtprintk, fs_type, proc_type;
type proc_pl_lk, fs_type, proc_type;
type proc_msdc_debug, fs_type, proc_type;
type proc_ufs_debug, fs_type, proc_type;
type proc_pidmap, fs_type, proc_type;
type proc_slabtrace, fs_type, proc_type;
type proc_cmdq_debug, fs_type, proc_type;
type proc_isp_p2, fs_type, proc_type;
type proc_dbg_repo, fs_type, proc_type;
type proc_isp_p2_dump, fs_type, proc_type;
type proc_isp_p2_kedump, fs_type, proc_type;
type proc_memory_usage, fs_type, proc_type;
type proc_gpu_memory, fs_type, proc_type;
type proc_mtk_es_reg_dump, fs_type, proc_type;
type proc_ccci_dump, fs_type, proc_type;
type proc_log_much, fs_type, proc_type;

#For icusb
type proc_icusb, fs_type, proc_type;

# for mt-ramdump reset
type proc_mrdump_rst, fs_type, proc_type;

# blockio procfs file
type proc_earaio, fs_type, proc_type;
type procfs_blockio, fs_type, proc_type;

# memtrack procfs file
type procfs_gpu_img, fs_type, proc_type;

# Date : WK19.27
# Purpose: Android Migration for SVP
type proc_m4u, fs_type, proc_type;

# Date : 2019/09/05
# Purpose: Allow powerhal to control kernel resources
type proc_ppm, fs_type, proc_type;
type proc_cpufreq, fs_type, proc_type;
type proc_hps, fs_type, proc_type;
type proc_cm_mgr, fs_type, proc_type;
type proc_fliperfs, fs_type, proc_type;

# Date : 2019/11/14
# Purpose: Allow powerhal to control MCDI
type proc_cpuidle, fs_type, proc_type;

# Date : 2019/10/11
# Purpose : allow system_server to access /proc/wlan/status for Q Migration
type proc_wlan_status, fs_type, proc_type;

# Date : 2019/12/10
# Purpose: Allow bt process or tool to control bt_dbg
type proc_btdbg, fs_type, proc_type;

type proc_wmt_aee, fs_type, proc_type;

# Date : 2020/01/16
# Purpose: Allow mtk_hal_neuralnetworks to read chip id and segment code
type proc_devinfo, fs_type, proc_type;

# Date : 2020/06/12
# Operation: R migration
# Purpose: Allow powerhal to control displowpower
type proc_displowpower, fs_type, proc_type;

# Date : 2020/06/29
# Operation: R migration
# Purpose: Add permission for access /proc/ion/*
type proc_ion, fs_type, proc_type;

# Date : 2020/07/01
# Operation: R migration
# Purpose: Add permission for access /proc/m4u_dbg/*
type proc_m4u_dbg, fs_type, proc_type;

# Date : WK20.31
# Operation: R migration
# Purpose : Add permission for /proc/bus/input/devices
type proc_bus_input, fs_type, proc_type;

# Date : 2020/08/05
# Purpose: Add permission for /proc/driver/wmt_user_proc
type proc_wmtuserproc, fs_type, proc_type;

# Date : 2020/09/07
# Purpose: mtk trigger panic by rcu stall
type proc_panic_on_rcu_stall, fs_type, proc_type;

# Date : 2020/09/18
# Purpose: add permission for /proc/mcdi/
type proc_mcdi, fs_type, proc_type;

# Date : 2020/09/22
# Purpose: define proc_ccci_sib
type proc_ccci_sib, fs_type, proc_type;

type proc_mtkfb, fs_type, proc_type;

# Date : 2020/07/08
# Purpose: add permission for /proc/sys/vm/swappiness
type proc_swappiness, fs_type, proc_type;

# Date : 2020/12/23
# Purpose: Add permission for /proc/driver/conninfra_dbg
type proc_conninfradbg, fs_type, proc_type;

# Data : 2021/4/21
# Purpose : add permission for /proc/mtk_usb, /proc/mtk_typec
type proc_usb_plat, fs_type, proc_type;

# Date 2021/05/10
# Purpose : init the default value before bootup
type proc_sched_migration_cost_ns, fs_type, proc_type;

# Date : 2021/5/21
# Purpose: Allow mobile log to read apusysy log
type proc_apusys_up_seq_logl, fs_type, proc_type;

# Date : 2021/8/24
# For CachedAppOptimizer
type proc_mtk_mdp_debug, fs_type, proc_type;

# 2021/8/25
# allow powerhal to access /proc/cpuhvfs/cpufreq_cci_mode
type proc_cpuhvfs, fs_type, proc_type;

##########################
# Sys Filesystem types
#
type sysfs_execstate, fs_type, sysfs_type;
type sysfs_therm, fs_type, sysfs_type;
type sysfs_thermal_sram, fs_type, sysfs_type;
type sysfs_charger_cooler, fs_type, sysfs_type;
type sysfs_fps, fs_type, sysfs_type;
type sysfs_ccci, fs_type, sysfs_type;
type sysfs_mdinfo, fs_type,sysfs_type;
type sysfs_ssw, fs_type,sysfs_type;
type sysfs_soc, fs_type, sysfs_type;
type sysfs_vcorefs_pwrctrl, fs_type, sysfs_type;
type sysfs_md32, fs_type, sysfs_type;
type sysfs_scp, fs_type, sysfs_type;
type sysfs_adsp, fs_type, sysfs_type;
type sysfs_rt_param, fs_type, sysfs_type;
type sysfs_rt_calib, fs_type, sysfs_type;
type sysfs_reset_dsp, fs_type, sysfs_type;
type sysfs_chip_vendor, fs_type, sysfs_type;
type sysfs_pa_num, fs_type, sysfs_type;
type sysfs_sspm, fs_type, sysfs_type;
type sysfs_devinfo, fs_type, sysfs_type, mlstrustedobject;
type sysfs_aee_enable, fs_type, sysfs_type;
type sysfs_dcm, fs_type, sysfs_type;
type sysfs_dcs, fs_type, sysfs_type;
type sysfs_vcore_debug, fs_type, sysfs_type;
type sysfs_systracker, fs_type, sysfs_type;
type sysfs_keypad_file, fs_type, sysfs_type;
type sysfs_vcp, fs_type, sysfs_type;

# apusys_queue sysfs file
type sysfs_apusys_queue, fs_type, sysfs_type;

# Date : WK1814
# Purpose : for factory to get boot mode and type
type sysfs_boot_mode, fs_type, sysfs_type;
type sysfs_boot_type, fs_type, sysfs_type;

# Date : WK1817
# Purpose : for meta to get com port type and uart port info
type sysfs_comport_type, fs_type, sysfs_type;
type sysfs_uart_info, fs_type, sysfs_type;
type sysfs_usb_nonplat, fs_type, sysfs_type;

# Date : WK1820
# Purpose : for charger to access pump_express
type sysfs_pump_express, fs_type, sysfs_type;
type sysfs_chg2_present, fs_type, sysfs_type;

# Date : 2018/10/25
# Purpose : mtk GPU drvb permission setting
type sysfs_gpu_mtk, fs_type, sysfs_type, mlstrustedobject;

# Touch parameters file
type sysfs_tpd_setting, fs_type, sysfs_type;

# Date : 2019/09/17
# Purpose : mtk factory fingerprint settings
type sysfs_gf_spi_tee, fs_type, sysfs_type;

# Backlight brightness file
type sysfs_leds_setting, fs_type, sysfs_type;

# Vibrator vibrate file
type sysfs_vibrator_setting, fs_type, sysfs_type;

# Date : WK18.16
# Purpose: Android Migration
type sysfs_mmcblk, fs_type, sysfs_type;
type sysfs_mmcblk1, fs_type, sysfs_type;

# Date : 2019/08/24
type sysfs_sensor, fs_type, sysfs_type;

#MTEE trusty
type mtee_trusty_file, fs_type, sysfs_type;

type sysfs_ged, fs_type, sysfs_type;
type sysfs_fbt_cpu, fs_type, sysfs_type;
type sysfs_fbt_fteh, fs_type, sysfs_type;
type sysfs_fpsgo, fs_type, sysfs_type;
type sysfs_xgf, fs_type, sysfs_type;
type sysfs_gbe, fs_type, sysfs_type;
type sysfs_mtk_fpsgo, fs_type, sysfs_type;
type sysfs_mtk_core_ctl, fs_type, sysfs_type;

# Date : 2019/09/17
# Purpose: Allow powerhal to control cache audit
type sysfs_cache_ctrl, fs_type, sysfs_type;
type sysfs_pftch_qos, fs_type, sysfs_type;

# Date : 2019/09/19
# Purpose: Allow powerhal to trigger task-turbo
type sysfs_task_turbo, fs_type, sysfs_type;

# Date : 2019/09/23
# Purpose: Define change_rate fs_type
type sysfs_change_rate, fs_type, sysfs_type;

# Date : 2019/10/16
# Purpose: Define sysfs_ext4_disable_barrier fs_type
type sysfs_ext4_disable_barrier, fs_type, sysfs_type;

# Date : WK19.38
# Purpose: Android Migration for video codec driver
type sysfs_device_tree_model, fs_type, sysfs_type;

# Date : 2019/10/11
# Purpose : allow system_server to access /sys/kernel/mm/ksm/pages_xxx
type sysfs_pages_shared, fs_type, sysfs_type;
type sysfs_pages_sharing, fs_type, sysfs_type;
type sysfs_pages_unshared, fs_type, sysfs_type;
type sysfs_pages_volatile, fs_type, sysfs_type;

# Date : 2019/10/22
# Purpose : allow aee_aedv write /sys/module/mrdump/parameters/lbaooo
type sysfs_mrdump, fs_type, sysfs_type;
type sysfs_memory, fs_type, sysfs_type;

# Date : 2019/10/25
# Purpose : To avoid using the SELabel of u:object_r:proc:s0 or u:object_r:sysfs:s0
# to access /proc/device-tree/chosen/atag,chipid or  /sysfs/firmware/devicetree/base/chosen/atag,chipid
type sysfs_chipid, fs_type, sysfs_type;

# Date : 2019/12/12
# Purpose : allow media sources to access /sys/bus/platform/drivers/mem_bw_ctrl/*
type sysfs_concurrency_scenario, fs_type, sysfs_type;

# Date : WK20.07
# Operation: R migration
# Purpose : Add permission for new device node.
type sysfs_meta_info, fs_type, sysfs_type;

type sysfs_cache_status, fs_type, sysfs_type;

# Date : 2020/06/12
# Purpose: define sysfs_mali_power_policy fs_type
type sysfs_mali_power_policy, fs_type, sysfs_type;

# Date : 20120/07/02
# Purpose: define sysfs_mtk_nanohub_state fs_type
type sysfs_mtk_nanohub_state, fs_type, sysfs_type;

# Date : 20120/07/13
# Purpose: define sysfs_dvfsrc_dbg fs_type
type sysfs_dvfsrc_dbg, fs_type, sysfs_type;

# Date : 2020/07/31
# Purpose: add permission for /sys/kernel/apusys/
type sysfs_apusys, fs_type, sysfs_type;

# Date : WK20.33
# Operation: R migration
# Purpose : Add permission for access aux_adc
type sys_mt6577_auxadc, fs_type, sysfs_type;

# Date : 2020/07/10
# Purpose : allow media sources to access /sys/bus/platform/drivers/emi_ctrl/*
type sysfs_emi_ctrl_concurrency_scenario, fs_type, sysfs_type;

# Date : 20120/08/19
# Purpose: define sysfs_dvfsrc_devfreq fs_type
type sysfs_dvfsrc_devfreq, fs_type, sysfs_type;

# Date : 2020/09/03
# Purpose: mtk MMQoS set camera max BW
type sysfs_camera_max_bw, fs_type, sysfs_type;
type sysfs_camera_max_bw_v2, fs_type, sysfs_type;

# Date : 2021/06/15
# Purpose: mtk MMQoS scenario change
type sysfs_mtk_mmqos_scen, fs_type, sysfs_type;
type sysfs_mtk_mmqos_scen_v2, fs_type, sysfs_type;

# Date : 2020/09/29
# Purpose: add permission for /sys/kernel/eara_thermal/
type sysfs_eara_thermal, fs_type, sysfs_type;

# Date : 2021/3/12
# Purpose: add permission for /sys/class/misc/mali0/device/pm_poweroff
type sysfs_mali_poweroff, fs_type, sysfs_type;

# Date : 2020/12/14
# Purpose: allow dumpstate/crash_dump/aee_aedv to read /sys/kernel/mm/mlog/dump
type sysfs_mm, fs_type, sysfs_type;

type sysfs_vpd, fs_type, sysfs_type;

# Date : 2021/06/01
# Purpose: for dcxo calibration
type sysfs_dcxo, fs_type, sysfs_type;

# 2021/05/26
# RTK Device
type sysfs_extdev, fs_type, sysfs_type;

# 2021/07/06
# charger configuration
type sysfs_chg_cfg, fs_type, sysfs_type;

# 2021/07/29
# boot mode access
type sysfs_boot_info, fs_type, sysfs_type;

# 2021/8/25
# allow powerhal to access /sys/kernel/cm_mgr/dbg_cm_mgr
type sysfs_cm_mgr, fs_type, sysfs_type;

##########################
# Debug Filesystem types
#

# display debugfs file
type debugfs_fb, fs_type, debugfs_type;

# fpsgo debugfs file
type debugfs_fpsgo, fs_type, debugfs_type;

# memtrack debugfs file
type debugfs_ion, fs_type, debugfs_type;

##########################
# Other Filesystem types
#
# rawfs for /protect_f on NAND projects
type rawfs, fs_type, mlstrustedobject;

##########################
# File types
#
# Date : 2019/12/19
# Purpose : Allow ccci_mdinit read /vendor/etc/md
type vendor_etc_md_file, vendor_file_type, file_type;

# Date : 2021/12/10
# Purpose : Allow mtk_hal_camera read /vendor/etc/nn
type vendor_etc_nn_file, vendor_file_type, file_type;


# Data : 2020/12/30
# Purpose : DBReleasePlan
type vendor_bin_crossbuild_file, vendor_file_type, file_type;
##########################
# File types
# Data file types
type custom_file, file_type, data_file_type;
type lost_found_data_file, file_type, data_file_type;
type dontpanic_data_file, file_type, data_file_type;
type resource_cache_data_file, file_type, data_file_type;
type http_proxy_cfg_data_file, file_type, data_file_type;
type acdapi_data_file, file_type, data_file_type;
type ppp_data_file, file_type, data_file_type;
type wpa_supplicant_data_file, file_type, data_file_type;
type radvd_data_file, file_type, data_file_type;
type mal_data_file, file_type, data_file_type;
type bt_data_file, file_type, data_file_type;
type agpsd_data_file, file_type, data_file_type;
type mnld_data_file, file_type, data_file_type;
type gps_data_file, file_type, data_file_type;
type MPED_data_file, file_type, data_file_type;
type protect_f_data_file, file_type, data_file_type;
type protect_s_data_file, file_type, data_file_type;
type persist_data_file, file_type, data_file_type;
type nvram_data_file, file_type, data_file_type;
type nvdata_file, file_type, data_file_type;
type nvcfg_file, file_type, data_file_type;
type mcf_ota_file, file_type, data_file_type;
type cct_data_file, file_type, data_file_type;
type mediaserver_data_file, file_type, data_file_type;
type mediacodec_data_file, file_type, data_file_type;
type connsyslog_data_vendor_file, file_type, data_file_type;
type nfc_data_vendor_file, file_type, data_file_type;

# AAO
type data_vendor_aao_file, file_type, data_file_type;
type data_vendor_aaoHwBuf_file, file_type, data_file_type;
type data_vendor_AAObitTrue_file, file_type, data_file_type;

# Flash
type data_vendor_flash_file, file_type, data_file_type;

# Flicker
type data_vendor_flicker_file, file_type, data_file_type;

# AFO
type data_vendor_afo_file, file_type, data_file_type;

# PDO
type data_vendor_pdo_file, file_type, data_file_type;

# NE core_forwarder
type aee_core_vendor_file, file_type, data_file_type;

type aee_dumpsys_vendor_file, file_type, data_file_type;

type ccci_cfg_file, file_type, data_file_type;
type ccci_data_md1_file, file_type, data_file_type;
type c2k_file, file_type, data_file_type;

#For sensor
type sensor_data_file, file_type, data_file_type;

type stp_dump_data_file, file_type, data_file_type;
type wifi_dump_data_file, file_type, data_file_type;
type bt_dump_data_file, file_type, data_file_type;

# data_tmpfs_log
type vendor_tmpfs_log_file, file_type, data_file_type;

# fat on nand fat.img
type fon_image_data_file, file_type, data_file_type;

# ims ipsec config file
type ims_ipsec_data_file, file_type, data_file_type;

# thermal manager config file
type thermal_manager_data_file, file_type, data_file_type;

# thermal core config file
type thermal_core_data_file, file_type, data_file_type;

# Thermal link device
type thermal_link_device, dev_type;

#autokd data file
type autokd_data_file, file_type, data_file_type;

# MTK audio HAL folder
type mtk_audiohal_data_file, file_type, data_file_type;

# MTK Power HAL folder
type mtk_powerhal_data_file, file_type, data_file_type;

# Date : WK1743
# Purpose : for meta_tst copy MD DB from MD image
type mddb_data_file, file_type, data_file_type;

# Widevine move data/mediadrm folder from system to vendor
type mediadrm_vendor_data_file, file_type, data_file_type;

# drm key manager
type provision_file, file_type, data_file_type;
type key_install_data_file, file_type, data_file_type;

type aee_dipdebug_vendor_file, file_type, data_file_type;

# Date : WK19.34
# Purpose: Android Migration for video codec driver
type vcodec_file, file_type, data_file_type, mlstrustedobject;

# Date : 2019/12/23
# Purpose : Allow ccci_mdinit read /data/vendor_de/md
type data_vendor_de_md_file, data_file_type, file_type;

# Date : 2019/04/23
# Operation: R migration
# Purpose : Add permission for acess vendor_de.
type factory_vendor_file, file_type, data_file_type;

# Date : 2020/09/24
# Purpose: mtk camsys raw buffer dump
type data_vendor_raw_file, file_type, data_file_type;

type vendor_nfc_socket_file, file_type, data_file_type;

# Date : W2109
# Purpose: add permission for /data/vendor/gpu_dump
type gpu_dump_vendor_file, file_type, data_file_type;

# Date : 2021/2/22
# Purpose: Add permission for EARA-IO
type eara_io_data_file, file_type, data_file_type;

##########################
# File types
# Core domain data file types
#
#mobilelog data/misc/mblog
type logmisc_data_file, file_type, data_file_type, core_data_file_type;

#mobilelog data/log_temp
type logtemp_data_file, file_type, data_file_type, core_data_file_type;

# NE core_forwarder
type aee_core_data_file, file_type, data_file_type, core_data_file_type;

type aee_dumpsys_data_file, file_type, data_file_type, core_data_file_type;

# SF rtt dump
type sf_rtt_file, file_type, data_file_type, core_data_file_type;

# data_tmpfs_log
type data_tmpfs_log_file, file_type, data_file_type, core_data_file_type;

# adbd config file
type adbd_data_file, file_type, data_file_type, core_data_file_type;

# SF bqdump
type sf_bqdump_data_file, file_type, data_file_type, core_data_file_type;

# factory data file
type factory_data_file, file_type, data_file_type, core_data_file_type;

# Modem Log folder
type mdlog_data_file, file_type, data_file_type, core_data_file_type;

# consys Log folder
type consyslog_data_file, file_type, data_file_type, core_data_file_type;

type nfc_socket_file, file_type, data_file_type, core_data_file_type;

##########################
# Socket types
#
type volte_vt_socket, file_type;
type dfo_socket, file_type;
type gsmrild_socket, file_type;
type rild2_socket, file_type;
type rild3_socket, file_type;
type rild4_socket, file_type;
type rild_mal_socket, file_type;
type rild_mal_at_socket, file_type;
type rild_mal_md2_socket, file_type;
type rild_mal_at_md2_socket, file_type;
type rild_ims_socket, file_type;
type rild_imsm_socket, file_type;
type rild_oem_socket, file_type;
type rild_mtk_ut_socket, file_type;
type rild_mtk_ut_2_socket, file_type;
type rild_mtk_modem_socket, file_type;
type rild_md2_socket, file_type;
type rild2_md2_socket, file_type;
type rild_debug_md2_socket, file_type;
type rild_oem_md2_socket, file_type;
type rild_mtk_ut_md2_socket, file_type;
type rild_mtk_ut_2_md2_socket, file_type;
type rild_mtk_modem_md2_socket, file_type;
type rild_vsim_socket, file_type;
type rild_vsim_md2_socket, file_type;
type mal_mfi_socket, file_type;
type netdiag_socket, file_type, mlstrustedobject;
type wpa_wlan0_socket, file_type;
type soc_vt_imcb_socket, file_type;
type soc_vt_tcv_socket, file_type;
type soc_vt_stk_socket, file_type;
type soc_vt_svc_socket, file_type;
type dbus_bluetooth_socket, file_type;
type bt_int_adp_socket, file_type;
type bt_a2dp_stream_socket, file_type;
type agpsd_socket, file_type;
type mnld_socket, file_type;
type MPED_socket, file_type;
type sysctl_socket, file_type;
type backuprestore_socket, file_type;

#for 3Gdongle
type rild-dongle_socket, file_type;

type rild_via_socket, file_type;
type rpc_socket, file_type;
type rild_ctclient_socket, file_type;

# socket between atci_service and audio-daemon
type atci-audio_socket, file_type;

# socket between atcid and meta_tst
type meta_atci_socket, file_type;

# socket between atcid and factory
type factory_atci_socket, file_type;

# ATCI socket types
type rild_atci_socket, file_type;
type rilproxy_atci_socket, file_type;
type atci_service_socket, file_type;
type adb_atci_socket, file_type;

type netd_socket, file_type, coredomain_socket;

# thermal hal socket file
type thermal_hal_socket, file_type;

# thermal core socket file
type thermal_socket, file_type;

type xcap_socket, file_type;

# Data : 2021/08/24
# Operaton: S development
# Purpose: Add permission for node /proc/dma_heap
type proc_dmaheap, fs_type, proc_type;

# Date: 2021/12/24
# Purpose: add new label for /proc/mgq
type proc_mgq, fs_type, proc_type;
