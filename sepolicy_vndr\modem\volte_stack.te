# ==============================================
# Policy File of /system/bin/volte_stack Executable File

# ==============================================
# Type Declaration
# ==============================================
type volte_stack, domain, mtkimsmddomain;
type volte_stack_exec, exec_type, file_type, vendor_file_type;

# ==============================================
# Common SEPolicy Rule
# ==============================================
#permissive volte_stack;
init_daemon_domain(volte_stack)
net_domain(volte_stack)


# Date : WK14.42
# Operation : Migration
# Purpose : for VoLTE L early bring up and first call
allow volte_stack self:key_socket { write read create setopt };
allow volte_stack self:capability net_admin;
allow volte_stack self:capability { setuid setgid };
allow volte_stack self:tcp_socket { bind create setopt listen };
allow volte_stack self:udp_socket { write bind read setopt };
allow volte_stack self:udp_socket create;
allow volte_stack self:tcp_socket shutdown;
allow volte_stack self:udp_socket shutdown;
allow volte_stack node:tcp_socket node_bind;
allow volte_stack node:udp_socket node_bind;
allow volte_stack port:tcp_socket name_bind;
allow volte_stack port:udp_socket name_bind;

# Date : 2015/01/07
# Operation : Migration
# Purpose : for VoLTE L Pre-FT test, Pre-FT error show we need add tcp rule
allow volte_stack self:tcp_socket accept;
allow volte_stack self:tcp_socket read;
allow volte_stack self:tcp_socket write;
allow volte_stack self:tcp_socket getattr;
allow volte_stack self:tcp_socket connect;
allow volte_stack port:tcp_socket name_connect;

allow volte_stack volte_stack_socket:sock_file write;

# Date : 2016/06/21
# Operation : ims_ipsec_lib performance
# Purpose : use netlink
allow volte_stack self:netlink_xfrm_socket { write bind create read nlmsg_write nlmsg_read};

# to NETD
allow volte_stack netd:unix_stream_socket connectto;
allow volte_stack netd_socket:sock_file write;
allow netd volte_stack:fd use;
allow netd volte_stack:tcp_socket { read write setopt getopt };
allow netd volte_stack:udp_socket {read write setopt getopt};
