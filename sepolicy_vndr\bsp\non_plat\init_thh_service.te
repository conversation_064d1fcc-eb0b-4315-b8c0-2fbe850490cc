# ==============================================
# MICROTRUST SEPolicy Rule
# ==============================================

# Date : 2016/06/01
# Operation: TEEI integration
# Purpose: Microtrust service
type init_thh_service_exec, exec_type, file_type, vendor_file_type;

# set up domain
init_daemon_domain(init_thh_service)

allow init_thh_service teei_data_file:dir create_dir_perms;
allow init_thh_service teei_data_file:file create_file_perms;
allow init_thh_service tee_device:chr_file rw_file_perms;

# enable access android property
set_prop(init_thh_service, vendor_mtk_soter_teei_prop)

hal_client_domain(init_thh_service, hal_teei_capi)
hal_client_domain(init_thh_service, hal_allocator)
hal_client_domain(init_thh_service, hal_teei_thh)
