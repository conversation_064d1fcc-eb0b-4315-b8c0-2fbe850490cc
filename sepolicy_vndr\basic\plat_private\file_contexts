# ==============================================
# Common SEPolicy Rule
# ==============================================

##########################
# Data files
#
/data/system_de/mdfilter(/.*)? u:object_r:mddb_filter_data_file:s0
/data/debuglogger(/.*)?        u:object_r:debuglog_data_file:s0
/data/ramdump(/.*)?            u:object_r:debuglog_data_file:s0

##########################
# System files
#
/system/bin/cmddumper            u:object_r:cmddumper_exec:s0
/system/bin/em_svr               u:object_r:em_svr_exec:s0
/system/bin/lbs_dbg              u:object_r:lbs_dbg_exec:s0

# wifi standalone log
/data/log_wifi_temp(/.*)? u:object_r:logtemp_data_file:s0

# storagemanager daemon
# it is used to mount all storages in meta/factory mode
/system/bin/storagemanagerd u:object_r:vold_exec:s0

# MTK Bootanim
/system/bin/mtkbootanimation  u:object_r:mtkbootanimation_exec:s0
/system/bin/boot_logo_updater u:object_r:boot_logo_updater_exec:s0

# Date: 2020/08/17
# Operation: R migration
# Purpose: Add permission for pl path utilities for OTA
/system/bin/mtk_plpath_utils u:object_r:mtk_plpath_utils_exec:s0

# mediaserver 64 bit support
/system/bin/mediahelper    u:object_r:mediahelper_exec:s0

##########################
# SystemExt files
#
##########################
# Devices
#
/dev/ubi_ctrl           u:object_r:mtd_device:s0
/dev/ubi[_0-9]*         u:object_r:mtd_device:s0
/dev/block/mtd(.*)?     u:object_r:mtd_device:s0
/dev/block/mntlblk(.*)? u:object_r:mtd_device:s0
/dev/mcupm(/.*)?        u:object_r:mcupm_device:s0

# support system server domain do ioctl with framework-res
/system/framework/framework-res.apk u:object_r:system_mtk_pmb_file:s0
