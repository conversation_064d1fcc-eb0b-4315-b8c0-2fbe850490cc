# ==============================================
# Common SEPolicy Rule
# ==============================================

allow radio proc_ccci_lp_mem:file r_file_perms;

#Data : 2021/08/11
# Operattion : DEBUG
# Purpose : Allow process at radio domain get mediametrics_service from service_manager
allow radio mediametrics_service:service_manager find;

# Date : 2018/07/03
# Purpose : Allow sim system to set prop
set_prop(radio, system_mtk_sim_system_prop)

# Operation : DEBUG
# Purpose : Allow to use system_mtk_bgdata_disabled_prop
set_prop(radio, system_mtk_bgdata_disabled_prop)

# Date : 2018/07/03
# Operation : DEBUG
# Purpose : Allow to use system_mtk_gprs_attach_type_prop
set_prop(radio, system_mtk_gprs_attach_type_prop)

#Date : 2018/11/02
# Operation : Allow radio set system_mtk_persist_xcap_rawurl_prop
# Purpose : for set telephony xcap use raw url property in IMS SS
set_prop(radio, system_mtk_persist_xcap_rawurl_prop)

#Date : 2021/09/06
# Operation : Allow radio set system_mtk_rcs_single_reg_support_prop
# Purpose : To use SIngle Registration property which will enable Google UCE flow in IMS and Presence
set_prop(radio, system_mtk_rcs_single_reg_support_prop)

#Date : 2021/11/10
# Operation : Allow radio set for pco_property
# Purpose : which will run pco flow
set_prop(radio, system_mtk_pco_prop)
