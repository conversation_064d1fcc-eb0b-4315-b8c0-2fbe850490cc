# ==============================================
# Common SEPolicy Rule
# ==============================================

# Date : 2014/10/28
# Operation : hs_xiangxu
# Purpose : [ALPS01782971]Settings need read&write to system_app_data_file
# Package: com.android.settings
allow platform_app system_app_data_file:file rw_file_perms;

# Date : 2015/09/06
# Operation : SQC
# Purpose : [NFC][can not get nfc service]
# Package: com.android.gallery3d
allow platform_app nfc_service:service_manager find;

# Date : 2017/06/01
# Operation : Migration
# Purpose : Allow camera app to find advcam servive
allow platform_app mtk_advcamserver_service:service_manager find;

# Date: 2017/06/29
# Operation: Migration
# Purpose : Allow UICC terminal to find phoneEx service
# Package: org.simalliance.openmobileapi
allow platform_app mtk_radio_service:service_manager find;

# Date : 2017/12/21
# Operation: IT
# Purpose : For hongbao optimization
allow platform_app mtk_connmetrics_service:service_manager find;

# Date: 2018/07/03
# Operation: Migration
# Purpose : Allow Rcs to get system_mtk_rcs_support_prop/system_mtk_em_tel_log_prop
# Package: om.mediatek.rcs
get_prop(platform_app, system_mtk_rcs_support_prop)
get_prop(platform_app, system_mtk_em_tel_log_prop)

# Date: 2018/07/03
# Operation: Migration
# Purpose : Allow Contacts to get system_mtk_uce_support_prop
# Package: com.android.contacts
get_prop(platform_app, system_mtk_uce_support_prop)

# Date: 2018/07/04
# Operation: P migration
# Purpose : allow radio get vzw device type property
get_prop(platform_app, system_mtk_persist_vendor_vzw_device_type_prop)

# Date : 2018/07/02
# Operation : Migration
# Purpose : Allow platform app to get ECBM property
get_prop(platform_app, system_mtk_cdma_ecm_prop)

# Date: 2018/07/06
# Operation: Migration
# Purpose : Allow Entitlement to get system_mtk_wfc_entitlement_prop
# Package: com.mediatek.entitlement
get_prop(platform_app, system_mtk_wfc_entitlement_prop)

# Date : 2018/07/27
# Operation : Migration
# Purpose : allow platform_app to find aal_service
allow platform_app aal_service:service_manager find;

# Date: 2018/10/25
# Operation: Clientapi Develope
# Purpose : Allow Contacts to get system_mtk_clientapi_support_prop
# Package: com.android.contacts
get_prop(platform_app, system_mtk_clientapi_support_prop)

# Date: 2018/10/26
# Purpose: Allow platform app to set and get Subsidy Lock properties
set_prop(platform_app, system_mtk_subsidylock_connect_prop)

# Date: 2018/10/26
# Purpose: Allow platform app to set and get Subsidy Lock properties
set_prop(platform_app, system_mtk_subsidylock_prop)

# Date: 2019/02/13
# Purpose : Allow ACS to get system_mtk_acs_url_prop, system_mtk_acs_version_prop and system_mtk_acs_support_prop
get_prop(platform_app, system_mtk_acs_url_prop)
get_prop(platform_app, system_mtk_acs_version_prop)
get_prop(platform_app, system_mtk_acs_support_prop)

# Date : 2019/05/28
# Operation : Q Migration
# Purpose : allow to get mtk_cta_set and mtk_cta_support property
get_prop(platform_app, system_mtk_cta_set_prop)

# Date: 2019/05/29
# Operation : Migration
# Purpose : Camera need read cl_cam_status
# Package: com.mediatek.camera
allow platform_app proc_cl_cam_status:file r_file_perms;

# Date : 2019/06/03
# Operation : Q Migration split build
# Purpose : allow to get system_mtk_rsc_sys_prop
get_prop(platform_app, system_mtk_rsc_sys_prop)

# Date : 2019/06/27
# Purpose : allow to set ctl.start/stop/restart property
set_prop(platform_app, system_mtk_ctl_campostalgo_prop)

# Date : 2019/06/27
# Purpose : allow to find camera postalgo service.
allow platform_app camerapostalgo_service:service_manager find;

# Date : 2019/09/27
# Operation : MDM IT with MDMLSample app
# Purpose : For MDMLSample to auto start md_monitor
set_prop(platform_app, config_prop)

# Date: 2019/09/27
# Operation : MDM IT with MDMLSample app
# Purpose: allow to read init.svc.md_monitor property for calling SystemService.waitForState()
get_prop(platform_app, system_mtk_init_svc_md_monitor_prop)

# Date : 2020/03/26
# Purpose : allow to find cta_networkdatacontroller_service.
allow platform_app cta_networkdatacontroller_service:service_manager find;

# Data : 2020/09/21
# Purpose : allow to setprop persist.vendor.radio.bgdata.disable
set_prop(platform_app, system_mtk_bgdata_disabled_prop)

# Allow platform_app to interact with hal_teei_tui
hal_client_domain(platform_app, hal_teei_tui)

# Allow platform_app to interact with hal_teei_capi
hal_client_domain(platform_app, hal_teei_capi)

# Allow platform_app to interact with hal_teei_thh
hal_client_domain(platform_app, hal_teei_thh)

hal_client_domain(platform_app, hal_teei_ifaa)
hal_client_domain(platform_app, hal_teei_wechat)

# Date:2021/11/13
# Operation: Add for DSDA in Dialer, add permission for accessing vendor.radio.dsda.state
get_prop(platform_app, system_mtk_common_data_prop)

# VoNR : 2021/11/26
# Purpose : allow to setprop persist.vendor.dbg.vonr_ui_ovr
set_prop(platform_app,system_mtk_dbg_ims_prop);
