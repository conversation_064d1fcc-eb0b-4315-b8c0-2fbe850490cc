# callback to /vendor/bin/aee_aedv for aee debugging
binder_call(mtk_hal_camera, aee_aedv)

# -----------------------------------
# Android O
# Purpose: AEE Debugging
# -----------------------------------
# Purpose: Allow aee_dumpstate to invoke "lshal debug <interface>", where <interface> is "ICameraProvider".
allow mtk_hal_camera dumpstate:binder { call };
allow mtk_hal_camera dumpstate:unix_stream_socket { read write };
allow mtk_hal_camera dumpstate:fd { use };
allow mtk_hal_camera dumpstate:fifo_file w_file_perms;

# Purpose: Allow camerahalserver to dump debug info to SYS_DEBUG_MTKCAM via aee_aedv.
# avc: denied { write } for path="/data/vendor/mtklog/aee_exp/temp/db.9oRG8O/SYS_DEBUG_MTKCAM"
# dev="dm-2" ino=1458278 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:aee_exp_vendor_file:s0
# tclass=file permissive=0
allow mtk_hal_camera aee_exp_vendor_file:dir w_dir_perms;
allow mtk_hal_camera aee_exp_vendor_file:file create_file_perms;

# Date : WK18.01
# Operation : label aee_aed sockets
# Purpose : Engineering mode need access for aee commmand
userdebug_or_eng(`
allow mtk_hal_camera aee_aedv:unix_stream_socket connectto;
')
