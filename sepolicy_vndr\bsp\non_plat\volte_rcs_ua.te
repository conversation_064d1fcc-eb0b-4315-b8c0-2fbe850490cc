# ==============================================
# Policy File of /vendor/bin/volte_rcs_ua Executable File

# ==============================================
# Common SEPolicy Rule
# ==============================================
type volte_rcs_ua_exec, exec_type, file_type, vendor_file_type;

init_daemon_domain(volte_rcs_ua)

# hwbinder access

hal_server_domain(volte_rcs_ua, hal_rcs)

# call into system_app process (callbacks)
binder_call(volte_rcs_ua, system_app)

# Date : W17.31
# Operation : IT
# Purpose: Rcs HIDL Migration
allow volte_rcs_ua debugfs_tracing:file { write open };

# Date : W1747
# Operation: RCS over Internet development
# Purpose: For volte_rcs_ua to be able to talk to rcs_volte_stack
allow volte_rcs_ua rcs_volte_stack_socket:sock_file { open getattr read write append };
allow volte_rcs_ua rcs_volte_stack:unix_stream_socket { read getattr connectto };

# Date : W1827
# Operation: P migration
# Purpose: Allow rcs ua to set rcs property
set_prop(volte_rcs_ua, vendor_mtk_service_rcs_prop)

# Date : W1929
# Operation: Volte stack submarine development
# Purpose: For volte_rcs_ua to be able to talk to rcs_rild
allow volte_rcs_ua rcs_rild_socket:sock_file { open getattr read write append };
allow volte_rcs_ua rild:unix_stream_socket { read getattr connectto };
