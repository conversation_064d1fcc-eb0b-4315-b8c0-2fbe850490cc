# ==============================================
# Policy File of /system/bin/loghidlsysservice Executable File

# ==============================================
# Type Declaration
# ==============================================
type loghidlsysservice_exec, system_file_type, exec_type, file_type;
typeattribute loghidlsysservice coredomain;

# ==============================================
# Common SEPolicy Rule
# ==============================================
init_daemon_domain(loghidlsysservice)

allow loghidlsysservice emdlogger:unix_stream_socket connectto;
allow loghidlsysservice mobile_log_d:unix_stream_socket connectto;
