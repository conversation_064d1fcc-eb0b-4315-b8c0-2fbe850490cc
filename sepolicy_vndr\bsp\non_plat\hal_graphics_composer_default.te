# Date : WK17.25
# Stage: O Migration, SQC
# Purpose: Allow to use HAL PQ
hal_client_domain(hal_graphics_composer_default, hal_mtk_pq)

# Date : WK17.25
# Stage: O Migration, SQC
# Purpose: Allow to use shared memory for HAL PQ
hal_client_domain(hal_graphics_composer_default, hal_allocator)

# Date : WK19.45
# Operation : WFD
# Purpose : HWC verify secure WFD
get_prop(hal_graphics_composer_default, vendor_mtk_secure_venc_prop)

# Date : WK20.39
# Purpose: allow proess access tee node(tee_client_device/isee_tee0)
allow hal_graphics_composer_default teei_client_device:chr_file rw_file_perms;

# Date : WK20.40
# Purpose : wifidisplay notify
get_prop(hal_graphics_composer_default, vendor_mtk_wfd_enable_prop)

# Date : WK21.01
# Purpose : Allow to use MMAgent
hal_client_domain(hal_graphics_composer_default, hal_mtk_mmagent)
allow hal_graphics_composer_default ion_device:chr_file w_file_perms;

# Date : WK21.25
# Purpose : Allow to use DAM buffer
allow hal_graphics_composer_default dmabuf_system_heap_device:chr_file rw_file_perms;

# Data: WK21.26
# Purpose: HWC needs to check whether mtk_svp_on_mtee is supported or not
get_prop(hal_graphics_composer_default, vendor_mtk_svp_on_mtee_support_prop)

# Data: WK21.35
# Purpose: Call NpAgent
hal_client_domain(hal_graphics_composer_default,hal_neuralnetworks)
