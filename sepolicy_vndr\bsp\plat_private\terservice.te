# ==============================================
# Policy File of /system/bin/terservice Executable File

# ==============================================
# Common SEPolicy Rule
# ==============================================

type terservice_exec, system_file_type, exec_type, file_type;
typeattribute terservice coredomain;

init_daemon_domain(terservice)

# Date : 2014/09/18 (WK14.38)
# Operation : Migration
# Purpose : allow register terservice service in servicemanager.
allow terservice terservice_service:service_manager add;

# property service
set_prop(terservice, system_mtk_terservice_prop)

# ipc call
binder_use(terservice)
binder_service(terservice)


