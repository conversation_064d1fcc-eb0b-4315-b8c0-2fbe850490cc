# ==============================================
# Policy File of /vendor/bin/thermal Executable File

# ==============================================
# Common SEPolicy Rule
# ==============================================
type thermal_exec, exec_type, file_type, vendor_file_type;

init_daemon_domain(thermal)
net_domain(thermal)

allow thermal mtkrild:unix_stream_socket connectto;
allow thermal proc_thermal:dir search;
allow thermal proc_thermal:file rw_file_perms;
allow thermal rild_oem_socket:sock_file write;
allow thermal netd_socket:sock_file write;
allow thermal netd:unix_stream_socket connectto;
allow thermal self:udp_socket create;
allow thermal self:udp_socket ioctl;
allow thermal rpc_socket:sock_file write;
allow thermal viarild:unix_stream_socket connectto;
allow thermal statusd:unix_stream_socket connectto;
allow thermal rild:unix_stream_socket connectto;

# If thermal(which belongs to vendor partition) want to open binder dev node(e.g. Parcel) will be
# denied for no permission. Should use vndbinder dev node in vendor domain.
# Using the following sepolicy rule to allow thermal to use vendor binder.
vndbinder_use(thermal)

# Data: 2018/08/26
# Operation: Thermal
# Purpose : add permission for thermal daemon to access mtcloader
set_prop(thermal, vendor_mtk_thermal_config_prop)
allow thermal thermal_manager_data_file:file rw_file_perms;
allow thermal thermalloadalgod:unix_stream_socket connectto;
allow thermal proc_mtkcooler:dir search;

