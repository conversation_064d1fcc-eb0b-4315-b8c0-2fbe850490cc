# ==============================================
# Policy File of /system/bin/em_svr Executable File

# ==============================================
# Type Declaration
# ==============================================

type em_svr_exec, system_file_type, exec_type, file_type;
typeattribute em_svr coredomain;

# ==============================================
# Common SEPolicy Rule
# ==============================================

init_daemon_domain(em_svr)

# Date: WK1812
# Purpose: add for MD log filter
allow em_svr block_device:dir search;
allow em_svr sdcardfs:dir w_dir_perms;
allow em_svr sdcardfs:file create_file_perms;

allow em_svr media_rw_data_file:dir rw_dir_perms;
allow em_svr media_rw_data_file:file create_file_perms;

# Date: WK1812
# Purpose: add for controlling screen on/off
allow em_svr graphics_device:dir search;
allow em_svr graphics_device:chr_file rw_file_perms;
allow em_svr surfaceflinger_service:service_manager find;
binder_use(em_svr)
binder_call(em_svr, surfaceflinger)

# Date: WK1812
# Purpose: add for controlling backlight
allow em_svr sysfs_leds:dir search;

# Date: WK1812
# Purpose: add for sensor calibration
allow em_svr self:capability { chown fsetid };

# Date: WK1812
# Purpose: add for shell cmd
allow em_svr shell_exec:file rx_file_perms;

# Date: WK1812
# Purpose: sys file access
allow em_svr sysfs:dir r_dir_perms;
