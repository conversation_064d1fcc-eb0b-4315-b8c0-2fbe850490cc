# ==============================================
# Common SEPolicy Rule
# ==============================================
##########################
# Filesystem types
#
##########################
# Proc Filesystem types
#
type proc_ccci_lp_mem, fs_type, proc_type;
type proc_dynamic_debug_control, fs_type, proc_type;

##########################
# Sys Filesystem types
#
type sysfs_mcupm, fs_type, sysfs_type;

# For drmserver
# Date: WK1812
# Operation : Migration
# Purpose : For drmserver
type access_sys_file, fs_type, sysfs_type;

##########################
# File types
# Core domain data file types
#
# For modem db filter HIDL client
# Date: WK1924
# Operation : Save modem db and filter into data partition
# Purpose : For Modem db and filter file
type mddb_filter_data_file, file_type, data_file_type, core_data_file_type;

type debuglog_data_file, file_type, data_file_type, core_data_file_type, mlstrustedobject;

#support system server domain do ioctl
type system_mtk_pmb_file, system_file_type, file_type, mlstrustedobject;
