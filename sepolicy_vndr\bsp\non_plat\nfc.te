# ==============================================
# Common SEPolicy Rule
# ==============================================

# Date      : 2014/10/15
# Operation : Refine
# Purpose   : Set NFC permission to access nfc_socket_file.
allow nfc nfc_socket_file:dir w_dir_perms;

# Date      : 2014/10/15
# Operation : Refine
# Purpose   : Set NFC permission to access custom file.
allow nfc custom_file:dir getattr;

# Date      : 2014/10/15
# Operation : Refine
# Purpose   : Set NFC permission to access nfc data file.
allow nfc nfc_data_file:dir { write remove_name add_name search create setattr };
allow nfc nfc_data_file:file { read getattr open rename write ioctl setattr create unlink };

# Date      : 2014/10/15
# Operation : Refine
# Purpose   : Set NFC permission to access SD card for debug purpose.
allow nfc sdcard_type:dir { write remove_name search create add_name };
allow nfc sdcard_type:file { read write getattr open rename create };
allow nfc vfat:dir { write add_name search };
allow nfc vfat:file { read write getattr open create };

# Date      : 2014/10/15
# Operation : Refine
# Purpose   : Set NFC permission for WFD
allow nfc surfaceflinger:dir search;

# Date      : 2014/10/15
# Operation : Refine
# Purpose   : For Mdlogger
allow nfc node:tcp_socket node_bind;
allow nfc port:tcp_socket name_bind;
allow nfc self:tcp_socket { setopt read bind create accept write getattr connect getopt listen };

# Date      : 2014/10/15
# Operation : Refine
# Purpose   : For NFC-JNI
allow nfc zygote:unix_stream_socket { getopt getattr };

# Date : WK1546
# Operation : Migration
# Purpose: Allow nfc to read binder from surfaceflinger
allow nfc surfaceflinger:fifo_file {read write};

# Date      : 2016/06/30
# Operation : SQC
# Purpose   : Allow NFC to plays sound which uses DrmServer
allow nfc drmserver_service:service_manager find;

# Date      : 2016/07/04
# Operation : SQC
# Purpose   : Allow NFC to access media data file
allow nfc media_rw_data_file:dir { create read open write remove_name search add_name };
allow nfc media_rw_data_file:file { read write create unlink open rename };

# Date      : 2016/11/10
# Operation : SQC
# Purpose   : Allow NFC to use FileManager share file
allow nfc sw_sync_device:chr_file getattr;

# Date      : 2017/07/26
# Operation : Refine
# Purpose   : Set NFC permission to access st21nfc_device ( nfc device node ) .
allow nfc st21nfc_device:chr_file { read write getattr open ioctl };
