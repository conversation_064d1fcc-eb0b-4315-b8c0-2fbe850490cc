# ==============================================
# Common SEPolicy Rule
# ==============================================

# Date : WK14.32
# Operation : Migration
# Purpose : for playing boot tone
binder_call(bootanim, mediaserver)
allow bootanim mediaserver_service:service_manager find;

# Purpose : for playing bootanimation audio
allow bootanim audioserver:binder {call transfer};
allow bootanim audioserver_service:service_manager find;

# Date : WK14.37
# Operation : Migration
# Purpose : for opetator
set_prop(bootanim, debug_prop)

# Date : WK14.37
# Operation : Migration
# Purpose : for opetator
set_prop(bootanim, system_mtk_bootani_prop)

# Date : WK14.46
# Operation : Migration
# /data/resource-cache
allow bootanim resourcecache_data_file:dir search;
allow bootanim resourcecache_data_file:file r_file_perms;

# Data : WK16.42
# Operator: Whitney bring up
# Purpose: call surfaceflinger due to powervr
allow bootanim surfaceflinger:fifo_file rw_file_perms;

# Date : W16.42
# Operation : Integration
# Purpose : DRM / DRI GPU driver required
allow bootanim gpu_device:dir search;
