# ==============================================
# Policy File of /vendor/bin/stp_dump3 Executable File

# ==============================================
# Type Declaration
# ==============================================
type stp_dump3_exec, vendor_file_type, exec_type, file_type;
type stp_dump3, domain;

# ==============================================
# Common SEPolicy Rule
# ==============================================
init_daemon_domain(stp_dump3)
allow stp_dump3 self:capability { net_admin fowner chown fsetid };
allow stp_dump3 self:netlink_socket create_socket_perms_no_ioctl;
allow stp_dump3 self:netlink_generic_socket create_socket_perms_no_ioctl;
allow stp_dump3 wmtdetect_device:chr_file rw_file_perms;
allow stp_dump3 stpwmt_device:chr_file rw_file_perms;
allow stp_dump3 tmpfs:lnk_file r_file_perms;
allow stp_dump3 mnt_user_file:dir search;
allow stp_dump3 mnt_user_file:lnk_file r_file_perms;
allow stp_dump3 storage_file:lnk_file r_file_perms;
allow stp_dump3 storage_file:dir search;
allow stp_dump3 sdcard_type:dir create_dir_perms;
allow stp_dump3 sdcard_type:file create_file_perms;
allow stp_dump3 stp_dump_data_file:dir create_dir_perms;
allow stp_dump3 stp_dump_data_file:file create_file_perms;
allow stp_dump3 stp_dump_data_file:sock_file create_file_perms;
allow stp_dump3 connsyslog_data_vendor_file:dir create_dir_perms;
allow stp_dump3 connsyslog_data_vendor_file:file create_file_perms;
get_prop(stp_dump3, vendor_mtk_coredump_prop)
