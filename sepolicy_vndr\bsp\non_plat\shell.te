# ==============================================
# Common SEPolicy Rule
# ==============================================

userdebug_or_eng(`

# MICROTRUST SEPolicy Rule
# Date : 2016/06/01
# Operation: TEEI integration
# Purpose: Microtrust init_thh service
  allow shell init_thh_service_exec:file rx_file_perms;
  allow shell init_thh_service_exec:dir r_dir_perms;
  hal_client_domain(shell, hal_teei_thh)

# Purpose: Allow shell to read trustkernel log
allow shell tkcore_data_file:dir search;
allow shell tkcore_log_file:file r_file_perms;

# Date : WK19.07 2019/06/13
# Operation : mdi_redirector integration test with AT&T Linkmaster
# Purpose : Allow shell to read DMC property ro.vendor.mtk_mapi_support
get_prop(shell, vendor_mtk_dmc_prop)

')
