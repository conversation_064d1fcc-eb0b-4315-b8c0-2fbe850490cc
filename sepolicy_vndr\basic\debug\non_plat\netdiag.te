# ==============================================
# Common SEPolicy Rule
# ==============================================

# Purpose : for access storage file
allow netdiag sdcard_type:dir create_dir_perms;
allow netdiag sdcard_type:file create_file_perms;
allow netdiag net_data_file:file r_file_perms;
allow netdiag net_data_file:dir search;
allow netdiag storage_file:dir search;
allow netdiag storage_file:lnk_file r_file_perms;
allow netdiag mnt_user_file:dir search;
allow netdiag mnt_user_file:lnk_file r_file_perms;
allow netdiag platform_app:dir search;
allow netdiag untrusted_app:dir search;
allow netdiag mnt_media_rw_file:dir search;
allow netdiag vfat:dir create_dir_perms;
allow netdiag vfat:file create_file_perms;
allow netdiag tmpfs:lnk_file r_file_perms;

# purpose: allow netdiag to access storage in new version
allow netdiag media_rw_data_file:file create_file_perms;
allow netdiag media_rw_data_file:dir create_dir_perms;

# purpose: read ip address
allow netdiag self:netlink_route_socket nlmsg_readpriv;