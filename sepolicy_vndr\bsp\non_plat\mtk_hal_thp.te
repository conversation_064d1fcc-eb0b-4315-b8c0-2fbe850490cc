# ==============================================
# Common SEPolicy Rule
# ==============================================

type mtk_hal_thp_exec, exec_type, file_type, vendor_file_type;

init_daemon_domain(mtk_hal_thp)

hal_server_domain(mtk_hal_thp, hal_mtk_thp)

allow mtk_hal_thp mtk_thp_data_file:dir create_dir_perms;
allow mtk_hal_thp mtk_thp_data_file:file create_file_perms;
allow mtk_hal_thp mtk_thp_data_file:sock_file create_file_perms;
allow mtk_hal_thp mtk_thp_data_file:fifo_file create_file_perms;

# Allow accessto /dev/thp & /dev/input_mt_wrapper node
allow mtk_hal_thp gdix_thp_device:chr_file rw_file_perms;
allow mtk_hal_thp gdix_mt_wrapper_device:chr_file rw_file_perms;

# Allow sys_nice
allow mtk_hal_thp self:capability sys_nice;

