# ==============================================
# Policy File of /vendor/bin/meta_tst Executable File

# ==============================================
# Common SEPolicy Rule
# ==============================================

# Date : 2016/06/01
# Operation: TEEI integration
# Purpose: Microtrust service
allow meta_tst init_thh_service_exec:file rx_file_perms;
allow meta_tst teei_data_file:dir create_dir_perms;
allow meta_tst teei_data_file:file create_file_perms;
allow meta_tst teei_client_device:chr_file { create setattr unlink rw_file_perms };
set_prop(meta_tst, vendor_mtk_soter_teei_prop)
hal_client_domain(meta_tst, hal_teei_thh)
allow meta_tst tee_device:chr_file rw_file_perms;

allow meta_tst camera_fdvt_device:chr_file rw_file_perms;
allow meta_tst camera_owe_device:chr_file rw_file_perms;
allow meta_tst camera_wpe_device:chr_file rw_file_perms;
allow meta_tst camera_gepf_device:chr_file rw_file_perms;
allow meta_tst camera_rsc_device:chr_file rw_file_perms;
allow meta_tst camera_tsf_device:chr_file rw_file_perms;
allow meta_tst camera_isp_device:chr_file rw_file_perms;
allow meta_tst ccu_device:chr_file rw_file_perms;
allow meta_tst vpu_device:chr_file rw_file_perms;

# Data: W17.27
# DRM Key Installation HIDL
allow meta_tst mtk_hal_keyinstall:binder call;

# Date: W17.27
# Purpose : Allow meta_tst to call vendor.mediatek.hardware.keyinstall@1.0-service.
hal_client_domain(meta_tst, hal_keymaster)

# Date: W17.46
allow meta_tst dm_device:blk_file rw_file_perms;
allow meta_tst devpts:chr_file rw_file_perms;
allow meta_tst kmsg_device:chr_file w_file_perms;
allow meta_tst sysfs_fs_ext4_features:dir search;
allow meta_tst sysfs_fs_ext4_features:file read;
allow meta_tst vendor_block_device:blk_file getattr;
allow meta_tst protect1_block_device:blk_file getattr;
allow meta_tst protect2_block_device:blk_file getattr;

# Date: W17.48
# Purpose : meta connect with ATCI by socket.
set_prop(meta_tst, vendor_mtk_persist_service_atci_prop)
allow meta_tst atcid:unix_stream_socket connectto;

# Purpose: TrustKernel Service
allow meta_tst tkcore_admin_device:chr_file { read write open ioctl };
allow meta_tst sdcardfs:dir create_dir_perms;
allow meta_tst sdcardfs:file create_file_perms;

# Data: W18.01
#tablet DRM Key Manager HIDL
allow meta_tst mtk_hal_keymanage:binder call;

# lite version start
allow meta_tst init_thh_service_exec:file { execute_no_trans };
# lite version end

# Date: W18.32
# Purpose: DRM key install
allow meta_tst mobicore_user_device:chr_file rw_file_perms;

# Data: W19.18
# Operation: Android Q migration
# Purpose : meta set atci property
set_prop(meta_tst, vendor_mtk_atci_sys_prop)
allow meta_tst adb_atci_socket:sock_file write;

# Date: WK20.13
# Operation : Migration
# Purpose : HDCP
allow meta_tst persist_data_file:dir create_dir_perms;
allow meta_tst persist_data_file:file create_file_perms;
allow meta_tst mobicore_vendor_file:file lock;
allow meta_tst self:capability chown;

hal_client_domain(meta_tst, hal_teei_capi)
hal_client_domain(meta_tst, hal_allocator)

# Date : WK20.51
# Purpose: Allow meta connect to sysfs_pmu
allow meta_tst sysfs_pmu:dir search;
allow meta_tst sysfs_pmu:file rw_file_perms;
