# ==============================================
# Common SEPolicy Rule
# ==============================================

##########################
# Data files
#
# Trustonic data files
/data/vendor/mcRegistry(/.*)? u:object_r:mobicore_data_file:s0

# Microtrust data files
/data/vendor/thh(/.*)? u:object_r:teei_data_file:s0

/data/vendor/radio(/.*)?       u:object_r:mtk_radio_data_file:s0
/data/vendor/verizon(/.*)?     u:object_r:omadm_data_file:s0
/data/vendor/misc/msdata(/.*)? u:object_r:omadm_misc_file:s0

# TrustKernel add
/data/vendor/t6(/.*)?      u:object_r:tkcore_data_file:s0
/data/vendor/t6/app(/.*)?  u:object_r:tkcore_spta_file:s0
/data/vendor/t6/tkcore.log u:object_r:tkcore_log_file:s0

# For Google Trusty Secure Storage Proxy
/data/vendor/trusty(/.*)? u:object_r:tee_data_file:s0

# DOE
/data/vendor/doe(/.*)? u:object_r:doe_vendor_data_file:s0

# MTK thp hal
/data/vendor/thp(/.*)? u:object_r:mtk_thp_data_file:s0

# MTK MDM
/data/vendor/md_mon(/.*)? u:object_r:md_monitor_vendor_file:s0

# EmCamera
/data/vendor/camera_dump(/.*)? u:object_r:vendor_camera_dump_file:s0

/data/vendor/.img(/.*)? u:object_r:gpunn_data_file:s0

# DuraSpeed
/data/duraspeed(/.*)? u:object_r:duraspeed_data_file:s0

/data/misc/log(/.*)? u:object_r:logmuch_data_file:s0

# Date: 2021/06/30
# Purpose: mtk nn info file
/data/vendor/nn(/.*)?        u:object_r:data_vendor_nn_file:s0

# Date: 2021/07/01
# Purpose: mtk hmp info file
/data/vendor/hmp(/.*)?        u:object_r:data_vendor_hmp_file:s0

##########################
# Devices
#
# TrustKernel add
/dev/tkcoredrv u:object_r:tkcore_admin_device:s0

# For Google Trusty Secure Storage Proxy
/dev/block/mmcblk0rpmb u:object_r:rpmb_block_device:s0

# Trustonic TEE devices
/dev/mobicore      u:object_r:mobicore_admin_device:s0
/dev/mobicore-user u:object_r:mobicore_user_device:s0
/dev/t-base-tui    u:object_r:mobicore_tui_device:s0

# teeperf devices
/dev/teeperf u:object_r:teeperf_device:s0

# Microtrust TEEI devices
/dev/teei_config u:object_r:teei_config_device:s0
/dev/teei_client u:object_r:teei_client_device:s0
/dev/isee_tee0   u:object_r:teei_client_device:s0
/dev/tz_vfs      u:object_r:teei_vfs_device:s0

# rpmb char device
/dev/rpmb0       u:object_r:teei_rpmb_device:s0
/dev/mmcblk0rpmb u:object_r:rpmb_device:s0

# legacy char device for cross-platform compatibility
/dev/emmcrpmb0    u:object_r:teei_rpmb_device:s0
/dev/teei_fp      u:object_r:teei_fp_device:s0
/dev/ut_keymaster u:object_r:ut_keymaster_device:s0
/dev/utr_tui      u:object_r:utr_tui_device:s0

# microtrust lite version use start
/dev/teei_loader u:object_r:tee_device:s0

# microtrust lite version use end
/dev/dri/card0 u:object_r:dri_device:s0

/dev/ttyC5     u:object_r:nwkopt_device:s0
/dev/mix_event u:object_r:tx_device:s0

# MTK thp hal
/dev/thp              u:object_r:gdix_thp_device:s0
/dev/input_mt_wrapper u:object_r:gdix_mt_wrapper_device:s0

# tetheroffload
/dev/mddp u:object_r:mddp_device:s0

/dev/socket/rcs_ua_proxy(/.*)?    u:object_r:rcs_ua_proxy_socket:s0
/dev/socket/rcs_volte_stack(/.*)? u:object_r:rcs_volte_stack_socket:s0
/dev/socket/rcs_rild(/.*)?        u:object_r:rcs_rild_socket:s0
/dev/socket/statusd               u:object_r:statusd_socket:s0
/dev/socket/rilproxy-mal(/.*)?    u:object_r:rild_mal_socket:s0
/dev/socket/wo_epdg_ipsec(/.*)?   u:object_r:wo_epdg_ipsec_socket:s0

# MTK ATCI
/dev/socket/rild-atci(/.*)?       u:object_r:rild_atci_socket:s0
/dev/socket/rilproxy-atci(/.*)?   u:object_r:rilproxy_atci_socket:s0
/dev/socket/atci-service(/.*)?    u:object_r:atci_service_socket:s0
/dev/socket/adb_atci_socket(/.*)? u:object_r:adb_atci_socket:s0

# MTK VTService
/dev/socket/volte_imsvt1(/.*)? u:object_r:volte_imsvt1_socket:s0

/dev/goodix_fp     u:object_r:fingerprint_device:s0

#MTK widevine kernel driver
/dev/drm_wv u:object_r:widevine_drv_device:s0

##########################
# Vendor files
#
# TrustKernel add
/(vendor|system/vendor)/bin/hw/android\.hardware\.keymaster@4\.0-service\.trustkernel u:object_r:hal_keymaster_default_exec:s0
/(vendor|system/vendor)/bin/hw/android\.hardware\.keymaster@4\.1-service\.trustkernel u:object_r:hal_keymaster_default_exec:s0

# Trustonic TEE
/(vendor|system/vendor)/bin/hw/android\.hardware\.keymaster@3\.0-service\.trustonic u:object_r:hal_keymaster_default_exec:s0
/(vendor|system/vendor)/bin/hw/android\.hardware\.keymaster@4\.0-service\.trustonic u:object_r:hal_keymaster_default_exec:s0
/(vendor|system/vendor)/bin/hw/android\.hardware\.keymaster@4\.1-service\.trustonic u:object_r:hal_keymaster_default_exec:s0

# Trustonic TEE system files
/(vendor|system/vendor)/app/mcRegistry(/.*)?                       u:object_r:mobicore_vendor_file:s0
/(vendor|system/vendor)/bin/mcDriverDaemon                         u:object_r:mobicore_exec:s0
/(vendor|system/vendor)/bin/hw/vendor\.trustonic\.tee@1\.1-service u:object_r:hal_tee_default_exec:s0

/(vendor|system/vendor)/bin/thermal                                               u:object_r:thermal_exec:s0
/(vendor|system/vendor)/bin/volte_rcs_ua                                          u:object_r:volte_rcs_ua_exec:s0
/(vendor|system/vendor)/bin/rcs_volte_stack                                       u:object_r:rcs_volte_stack_exec:s0
/(vendor|system/vendor)/bin/volte_clientapi_ua                                    u:object_r:volte_clientapi_ua_exec:s0
/(vendor|system/vendor)/bin/viarild                                               u:object_r:viarild_exec:s0
/(vendor|system/vendor)/bin/statusd                                               u:object_r:statusd_exec:s0
/(vendor|system/vendor)/bin/flashlessd                                            u:object_r:flashlessd_exec:s0
/(vendor|system/vendor)/bin/ccci_fsd                                              u:object_r:ccci_fsd_exec:s0
/(vendor|system/vendor)/bin/ccci_rpcd                                             u:object_r:ccci_rpcd_exec:s0
/(vendor|system/vendor)/bin/ipsec_mon                                             u:object_r:ipsec_mon_exec:s0
/(vendor|system/vendor)/bin/getgameserver                                         u:object_r:getgameserver_exec:s0
/(vendor|system/vendor)/bin/hw/vendor\.mediatek\.hardware\.wfo@1\.0-service       u:object_r:mtk_hal_wfo_exec:s0
/(vendor|system/vendor)/bin/hw/vendor\.mediatek\.hardware\.clientapi@1\.0-service u:object_r:volte_clientapi_ua_exec:s0
/(vendor|system/vendor)/bin/hw/vtservice_hidl                                     u:object_r:vtservice_hidl_exec:s0
/(vendor|system/vendor)/bin/hw/vendor\.mediatek\.hardware\.rcs@1\.0-service       u:object_r:volte_rcs_ua_exec:s0
/(vendor|system/vendor)/bin/STFlashTool                                           u:object_r:stflashtool_exec:s0
/(vendor|system/vendor)/bin/hw/vendor\.mediatek\.hardware\.dfps@1\.0-service      u:object_r:mtk_hal_dfps_exec:s0
/(vendor|system/vendor)/bin/hw/vendor\.mediatek\.hardware\.omadm@1\.0-service     u:object_r:mtk_hal_omadm_exec:s0

# DOE
/(vendor|system/vendor)/bin/hw/vendor\.mediatek\.hardware\.dplanner@1\.0-service u:object_r:mtk_hal_dplanner_exec:s0
/(vendor|system/vendor)/bin/hw/vendor\.mediatek\.hardware\.dplanner@2\.0-service u:object_r:mtk_hal_dplanner_exec:s0
/(vendor|system/vendor)/bin/dconfig                                              u:object_r:mtk_dconfig_exec:s0
/(vendor|system/vendor)/bin/dtc_vendor                                           u:object_r:mtk_dconfig_exec:s0

# DRM Key Installation HIDL
/(vendor|system/vendor)/bin/hw/vendor\.mediatek\.hardware\.keyinstall@1\.0-service u:object_r:mtk_hal_keyinstall_exec:s0

# DRM Key Manage HIDL
/(vendor|system/vendor)/bin/hw/vendor\.mediatek\.hardware\.keymanage@1\.0-service u:object_r:mtk_hal_keymanage_exec:s0

/(vendor|system/vendor)/bin/wo_ipsec                                                    u:object_r:wo_ipsec_exec:s0
/(vendor|system/vendor)/bin/wo_charon                                                   u:object_r:wo_charon_exec:s0
/(vendor|system/vendor)/bin/wo_starter                                                  u:object_r:wo_starter_exec:s0
/(vendor|system/vendor)/bin/wo_stroke                                                   u:object_r:wo_stroke_exec:s0
/(vendor|system/vendor)/bin/wo_epdg_client                                              u:object_r:wo_epdg_client_exec:s0

# netdagent
/(vendor|system/vendor)/bin/netdagent u:object_r:netdagent_exec:s0

# MTK PPL
/(vendor|system/vendor)/bin/ppl_agent u:object_r:ppl_agent_exec:s0

# Microtrust TEEI system files
/(vendor|system/vendor)/bin/init_thh    u:object_r:init_thh_service_exec:s0
/(vendor|system/vendor)/bin/teei_daemon u:object_r:tee_exec:s0

# microtrust THH daemon
/(vendor|system/vendor)/bin/hw/vendor\.microtrust\.hardware\.thh@2\.0-service u:object_r:teei_hal_thh_exec:s0

# microtrust TUI daemon
/(vendor|system/vendor)/bin/hw/vendor\.microtrust\.hardware\.tui@2\.0-service u:object_r:teei_hal_tui_exec:s0

# microtrust IFAA hidl service
/(vendor|system/vendor)/bin/hw/vendor\.microtrust\.hardware\.ifaa@1\.0-service u:object_r:teei_hal_ifaa_exec:s0
/(vendor|system/vendor)/bin/hw/vendor\.microtrust\.hardware\.ifaa@2\.0-service u:object_r:teei_hal_ifaa_exec:s0

# microtrust WECHAT hidl service
/(vendor|system/vendor)/bin/hw/vendor\.microtrust\.hardware\.soter@1\.0-service          u:object_r:teei_hal_wechat_exec:s0
/(vendor|system/vendor)/bin/teei_loader                                                  u:object_r:tee_exec:s0
/(vendor|system/vendor)/bin/istorageproxyd                                               u:object_r:tee_exec:s0
/(vendor|system/vendor)/bin/hw/vendor\.microtrust\.hardware\.capi@2\.0-service           u:object_r:teei_hal_capi_exec:s0
/(vendor|system/vendor)/bin/hw/android\.hardware\.keymaster@4\.0-service\.beanpod        u:object_r:hal_keymaster_default_exec:s0
/(vendor|system/vendor)/bin/hw/android\.hardware\.keymaster@4\.0-service\.beanpod\.lite  u:object_r:hal_keymaster_default_exec:s0
/(vendor|system/vendor)/bin/hw/android\.hardware\.keymaster@4\.1-service\.beanpod        u:object_r:hal_keymaster_default_exec:s0
/(vendor|system/vendor)/bin/hw/android\.hardware\.keymaster@4\.1-service\.beanpod\.lite  u:object_r:hal_keymaster_default_exec:s0
/(vendor|system/vendor)/bin/hw/android\.hardware\.security\.keymint@1\.0-service\.beanpod u:object_r:hal_keymint_default_exec:s0
/(vendor|system/vendor)/bin/hw/android\.hardware\.gatekeeper@1\.0-service\.beanpod\.lite u:object_r:hal_gatekeeper_default_exec:s0
/(vendor|system/vendor)/bin/hw/android\.hardware\.gatekeeper@1\.0-service\.itrusty u:object_r:hal_gatekeeper_default_exec:s0
/(vendor|system/vendor)/bin/bp_kmsetkey_ca u:object_r:bp_kmsetkey_ca_exec:s0

/(vendor|system/vendor)/bin/hw/android\.hardware\.biometrics\.fingerrpint@1\.1-service              u:object_r:hal_fingerprint_default_exec:s0
/(vendor|system/vendor)/bin/hw/android\.hardware\.neuralnetworks@1\.3-service-gpunn                 u:object_r:mtk_hal_neuralnetworks_exec:s0
/(vendor|system/vendor)/bin/hw/android\.hardware\.neuralnetworks@1\.3-service-mtk-neuron            u:object_r:mtk_hal_neuralnetworks_exec:s0
/(vendor|system/vendor)/bin/hw/android\.hardware\.neuralnetworks@1\.3-service-mtk-neuron-lazy       u:object_r:mtk_hal_neuralnetworks_exec:s0
/(vendor|system/vendor)/bin/hw/android\.hardware\.neuralnetworks@1\.3-service-mtk-neuron-debug      u:object_r:mtk_hal_neuralnetworks_exec:s0
/(vendor|system/vendor)/bin/hw/android\.hardware\.neuralnetworks@1\.3-service-mtk-neuron-debug-lazy u:object_r:mtk_hal_neuralnetworks_exec:s0
/(vendor|system/vendor)/bin/hw/android\.hardware\.neuralnetworks-shim-service-mtk                   u:object_r:mtk_hal_neuralnetworks_exec:s0
/(vendor|system/vendor)/bin/hw/android\.hardware\.neuralnetworks-shell-service-mtk                  u:object_r:mtk_hal_neuralnetworks_exec:s0

# MTK nwk opt hal
/(vendor|system/vendor)/bin/hw/vendor\.mediatek\.hardware\.nwk_opt@1\.0-service u:object_r:mtk_hal_nwk_opt_exec:s0

# MTK touchll hal
/(vendor|system/vendor)/bin/hw/vendor\.mediatek\.hardware\.touchll@1\.0-service u:object_r:mtk_hal_touchll_exec:s0

# MTK thp hal
/(vendor|system/vendor)/bin/hw/vendor\.mediatek\.hardware\.thp@1\.0-service u:object_r:mtk_hal_thp_exec:s0

# tetheroffload
/(vendor|system/vendor)/bin/hw/tetheroffloadservice u:object_r:hal_tetheroffload_default_exec:s0

# MTK ATCI
/(vendor|system/vendor)/bin/atcid        u:object_r:atcid_exec:s0
/(vendor|system/vendor)/bin/atci_service u:object_r:atci_service_exec:s0

# MTK PMS ext
/(vendor|system/vendor)/operator/app(/.*)?            u:object_r:vendor_app_file:s0
/(vendor|system/vendor)/etc/rsc/[^/]+/app(/.*)?       u:object_r:vendor_app_file:s0
/(vendor|system/vendor)/etc/rsc/[^/]+/priv-app(/.*)?  u:object_r:vendor_app_file:s0
/(vendor|system/vendor)/etc/rsc/[^/]+/plugin(/.*)?    u:object_r:vendor_app_file:s0
/(vendor|system/vendor)/etc/rsc/[^/]+/overlay(/.*)?   u:object_r:vendor_overlay_file:s0
/(vendor|system/vendor)/etc/rsc/[^/]+/framework(/.*)? u:object_r:vendor_framework_file:s0

/(vendor|system/vendor)/bin/remosaic_daemon u:object_r:remosaic_daemon_exec:s0

# HDCP
/(vendor|system/vendor)/bin/hw/vendor\.tesiai\.hardware\.hdcpconnection@1\.0-service u:object_r:tesiai_hal_hdcp_exec:s0

# ST nfcstackp service
/vendor/bin/nfcstackp-vendor u:object_r:nfcstackp_vendor_exec:s0

# DMC (Diagnostic Monitoring Collector)
/vendor/bin/dmc_core u:object_r:dmc_core_exec:s0

# DMC Packet Monitor (PKM)
/vendor/bin/mtk_pkm_service u:object_r:mtk_pkm_service_exec:s0

# TrustKernel add
/vendor/bin/teed u:object_r:tee_exec:s0

# For Google Trusty Secure Storage Proxy
/vendor/bin/storageproxyd u:object_r:tee_exec:s0
/(vendor|system/vendor)/bin/rpmb_svc u:object_r:tee_exec:s0

# MTK MDM
/vendor/bin/md_monitor u:object_r:md_monitor_exec:s0

/vendor/app/t6(/.*)? u:object_r:tkcore_systa_file:s0

##########################
# Others
#
/mnt/vendor/persist/t6(/.*)?    u:object_r:tkcore_protect_data_file:s0
/mnt/vendor/protect_f/tee(/.*)? u:object_r:tkcore_protect_data_file:s0

# Logo Updater
/vendor/bin/logo_updater u:object_r:logo_updater_exec:s0
