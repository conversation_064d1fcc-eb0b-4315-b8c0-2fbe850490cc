# ==============================================
# Policy File of /system/bin/batterywarning Executable File

# ==============================================
# Common SEPolicy Rule
# ==============================================

type batterywarning_exec, system_file_type, exec_type, file_type;
typeattribute batterywarning coredomain;

init_daemon_domain(batterywarning)

# Date : 2014/10/15
# Operation : Migration
# Purpose : all Binder IPC for battery warning to call IActivityManager to send broadcast
binder_use(batterywarning)

# Date : 2014/10/16
# Operation : Migration
# Purpose : allow battery warning use AMS to send broadcast through binder call
binder_call(batterywarning, system_server)

# Date : 2015/07/27
# Operation : Migration
# Purpose : allow battery warning check AMS service status
allow batterywarning activity_service:service_manager find;

# Date : 2017/07/03
# Operation : Migration
# Purpose : allow battery warning read file
allow batterywarning sysfs_battery_warning:file r_file_perms;

# Date : 2019/07/31
# Operation : Migration
# Purpose : allow battery warning open socket connection
allow batterywarning self:netlink_kobject_uevent_socket create_socket_perms_no_ioctl;
