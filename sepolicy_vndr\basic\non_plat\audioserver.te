# ==============================================
# Common SEPolicy Rule
# ==============================================

# Date: WK14.44
# Operation : Migration
# Purpose : EVDO
allow audioserver rpc_socket:sock_file write;
allow audioserver ttySDIO_device:chr_file rw_file_perms;

# Data: WK14.44
# Operation : Migration
# Purpose : for low SD card latency issue
allow audioserver sysfs_lowmemorykiller:file r_file_perms;

# Data: WK14.45
# Operation : Migration
# Purpose : for change thermal policy when needed
allow audioserver proc_mtkcooler:dir search;
allow audioserver proc_mtktz:dir search;
allow audioserver proc_thermal:dir search;

# Date : WK15.03
# Operation : Migration
# Purpose : offloadservice
allow audioserver offloadservice_device:chr_file rw_file_perms;

# Date : WK16.17
# Operation : Migration
# Purpose: read/open sysfs node
allow audioserver sysfs_ccci:file r_file_perms;

# Date : WK16.18
# Operation : Migration
# Purpose: research root dir "/"
allow audioserver tmpfs:dir search;

# Date : WK16.18
# Operation : Migration
# Purpose: access sysfs node
allow audioserver sysfs_ccci:dir search;

# Purpose: Dump debug info
allow audioserver fuse:file w_file_perms;

# Date : WK16.33
# Purpose: Allow to access ged for gralloc_extra functions
allow audioserver proc_ged:file rw_file_perms;
