# ==============================================
# Policy File of /vendor/app/sensorhub Executable File

# ==============================================
# Common SEPolicy Rule
# ==============================================
app_domain(sensorhub_app)

allow sensorhub_app activity_service:service_manager find;
allow sensorhub_app surfaceflinger_service:service_manager find;
allow sensorhub_app activity_task_service:service_manager find;
allow sensorhub_app audio_service:service_manager find;
allow sensorhub_app drmserver_service:service_manager find;
allow sensorhub_app autofill_service:service_manager find;
allow sensorhub_app sensorservice_service:service_manager find;
allow sensorhub_app mediaextractor_service:service_manager find;
allow sensorhub_app mediaserver_service:service_manager find;
allow sensorhub_app mediametrics_service:service_manager find;

# Date : 2019/03/25
# Operation : IT
# Purpose : for engineermode sensor can work normal
allow sensorhub_app als_ps_device:chr_file r_file_perms;
allow sensorhub_app gsensor_device:chr_file r_file_perms;
allow sensorhub_app gyroscope_device:chr_file r_file_perms;
