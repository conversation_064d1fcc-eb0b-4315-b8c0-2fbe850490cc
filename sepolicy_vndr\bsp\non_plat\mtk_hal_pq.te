# ==============================================
# Policy File of /vendor/bin/hw/vendor.mediatek.hardware.pq@2.0-service Executable File

# ==============================================
# Common SEPolicy Rule
# ==============================================

# Date : WK17.01
# Operation : New feature: Factory Gamma
# Purpose : Allow nvram access
allow mtk_hal_pq nvdata_file:dir w_dir_perms;
allow mtk_hal_pq nvdata_file:file create_file_perms;
allow mtk_hal_pq nvram_data_file:dir w_dir_perms;
allow mtk_hal_pq nvram_data_file:file create_file_perms;
allow mtk_hal_pq nvram_data_file:lnk_file read;
allow mtk_hal_pq nvdata_file:lnk_file read;

# Operation : New feature: AppGamePQ 2.0
# Purpose : create hidl handle for buffer
allow mtk_hal_pq hal_graphics_allocator_default:fd use;
allow mtk_hal_pq proc_ged:file r_file_perms;
allowxperm mtk_hal_pq proc_ged:file ioctl proc_ged_ioctls;
