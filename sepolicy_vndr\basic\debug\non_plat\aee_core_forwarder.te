# ==============================================
# Policy File of /system/bin/aee_core_forwarder Executable File

# ==============================================
# Common SEPolicy Rule
# ==============================================

allow aee_core_forwarder aee_exp_data_file:dir rw_dir_perms;
allow aee_core_forwarder aee_exp_data_file:file create_file_perms;

# Date: 2019/06/14
# Operation : Migration
# Purpose : interface=android.system.suspend::ISystemSuspend for aee_core_forwarder
wakelock_use(aee_core_forwarder)
allow aee_core_forwarder crash_dump:unix_stream_socket connectto;
allow aee_core_forwarder aee_core_data_file:dir r_dir_perms;
allow aee_core_forwarder crash_dump:lnk_file r_file_perms;
allow aee_core_forwarder crash_dump:process {getattr};
allow aee_core_forwarder sysfs_aee_enable:file r_file_perms;
