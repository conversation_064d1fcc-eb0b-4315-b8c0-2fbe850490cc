# ==============================================
# Common SEPolicy Rule
# ==============================================

# Data : WK16.25
# Operation : Camera display client
# Purpose : for SVP secure memory allocation
allow mediacodec proc_secmem:file rw_file_perms;

# Date : WK16.25
# Operation : WVL1 IT
# Purpose : SVP module operates secmem driver
allow mediacodec mobicore_data_file:file { read open getattr};
allow mediacodec mobicore_user_device:chr_file rw_file_perms;
allow mediacodec mobicore:unix_stream_socket connectto;
allow mediacodec mobicore_data_file:dir search;
allow mediacodec persist_data_file:file { read getattr open };
allow mediacodec persist_data_file:dir search;

# Date : WK16.28
# Operation : video codec driver
# Purpose : for performance profiling and timing issue tracking during video playback
allow mediacodec debugfs_fb:dir search;

# Date : WK16.29
# Operation : Migration
# Purpose : Add permission for gpu access
allow mediacodec dri_device:chr_file rw_file_perms;

# Date : WK16.50
# Operation : video codec driver
# Purpose : Add permission for thermal function access
allow mediacodec proc_mtktz:dir search;
allow mediacodec proc_mtktz:file r_file_perms;

# Date : WK17.23
# Stage: O Migration, SQC
# Purpose: Allow to use HAL PQ
hal_client_domain(mediacodec, hal_mtk_pq)

# Date : WK17.23
# Stage: O Migration, SQC
# Purpose: Allow to use shared memory for HAL PQ
hal_client_domain(mediacodec, hal_allocator)

# Date : WK17.31
# Stage: O Migration, SQC
# Purpose: Allow to use ape decoder
hal_client_domain(mediacodec, hal_mtk_codecservice)

# Date : WK18.46
# Operation : WVL1 IT for TEEI
# Purpose : SVP module operates TEEI
hal_client_domain(mediacodec, hal_teei_capi)
allow mediacodec teei_client_device:chr_file rw_file_perms;

# Date : WK19.44
# Purpose: Android Migration for D2+ Encoder
allow mediacodec proc_chip:dir r_dir_perms;
allow mediacodec proc_chip:file r_file_perms;

# Date : WK19.45
# Operation : WFD
# Purpose : Allow set property to notify HWC secure venc enabled
set_prop(mediacodec, vendor_mtk_secure_venc_prop)

# Date : WK20.22
# Operation : VDEC debug
# Purpose : allow vdec can dump file to storage
allow mediacodec vcodec_file:dir create_dir_perms;
allow mediacodec vcodec_file:file create_file_perms;

# Date : WK20.40
# Operation : WFD
# Purpose : Allow set property to notify HWC wfd enabled
set_prop(mediacodec, vendor_mtk_wfd_enable_prop)

#allow get mtk_sec_video_path_support
get_prop(mediacodec, vendor_mtk_sec_video_path_support_prop)
