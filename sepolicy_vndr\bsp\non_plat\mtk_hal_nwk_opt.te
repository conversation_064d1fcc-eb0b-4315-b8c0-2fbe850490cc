hal_server_domain(mtk_hal_nwk_opt,hal_nwk_opt)
type mtk_hal_nwk_opt_exec,exec_type,file_type,vendor_file_type;
init_daemon_domain(mtk_hal_nwk_opt)
hwbinder_use(mtk_hal_nwk_opt)

add_hwservice(hal_nwk_opt_server,mtk_hal_nwk_opt_hwservice)

binder_call(hal_nwk_opt_client, hal_nwk_opt_server)
binder_call(hal_nwk_opt_server, hal_nwk_opt_client)

allow hal_nwk_opt_client mtk_hal_nwk_opt_hwservice :hwservice_manager find;
allow mtk_hal_nwk_opt nwkopt_device:chr_file rw_file_perms;
allow mtk_hal_nwk_opt sysfs_fpsgo:dir search;
allow mtk_hal_nwk_opt sysfs_fpsgo:file rw_file_perms;
allow mtk_hal_nwk_opt input_device:dir r_dir_perms;
allow mtk_hal_nwk_opt input_device:file rw_file_perms;
allow mtk_hal_nwk_opt input_device:chr_file rw_file_perms;
allow mtk_hal_nwk_opt tx_device:chr_file rw_file_perms;

set_prop(mtk_hal_nwk_opt, vendor_mtk_nwk_opt_prop)
