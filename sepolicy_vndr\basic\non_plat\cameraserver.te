# ==============================================================================
# Policy File of /system/bin/cameraserver Executable File

# ==============================================
# Common SEPolicy Rule
# ==============================================

# -----------------------------------
# Android O
# Purpose: Allow cameraserver to perform binder IPC to servers and callbacks.
# call camerahalserver
binder_call(cameraserver, mtk_hal_camera)

# call the graphics allocator hal
binder_call(cameraserver, hal_graphics_allocator)

# -----------------------------------
# Android O
# Purpose: adb shell dumpsys media.camera --unreachable
allow cameraserver self:process ptrace;

# Date : WK14.40
# Operation : Migration
# Purpose : HDMI driver access
allow cameraserver graphics_device:chr_file rw_file_perms;

# Date : WK16.20
# Operation : Migration
# Purpose: research root dir "/"
allow cameraserver tmpfs:dir search;

# Date : WK16.21
# Operation : Migration
# Purpose : EGL file access
allow cameraserver system_file:dir r_dir_perms;
allow cameraserver gpu_device:chr_file rw_file_perms;
allow cameraserver gpu_device:dir search;

# Date : WK16.33
# Purpose: Allow to access ged for gralloc_extra functions
allow cameraserver proc_ged:file rw_file_perms;
allowxperm cameraserver proc_ged:file ioctl proc_ged_ioctls;

# Date : WK17.25
# Operation : Migration
allow cameraserver debugfs_ion:dir search;

# Date : WK17.49
# Operation : MT6771 SQC
# Purpose: Allow permgr access
allow cameraserver proc_perfmgr:dir r_dir_perms;
allow cameraserver proc_perfmgr:file r_file_perms;
allowxperm cameraserver proc_perfmgr:file ioctl {
  PERFMGR_FPSGO_QUEUE
  PERFMGR_FPSGO_DEQUEUE
  PERFMGR_FPSGO_QUEUE_CONNECT
  PERFMGR_FPSGO_BQID
};

# Date : WK21.25
# Operation : Migration
# Purpose : PDA Driver
allow cameraserver camera_pda_device:chr_file rw_file_perms;
