# ==============================================
# Common SEPolicy Rule
# ==============================================

# for debug purpose
allow surfaceflinger self:capability { net_admin sys_nice };
allow surfaceflinger self:netlink_socket { read bind create };
allow surfaceflinger anr_data_file:dir { write search create add_name };
allow surfaceflinger anr_data_file:file { create write};
allow surfaceflinger aee_dumpsys_data_file:file write;
allow surfaceflinger RT_Monitor_device:chr_file { read ioctl open };

# watch dog use shell to move debug file
allow surfaceflinger shell_exec:file rx_file_perms;

# for using toolbox
allow surfaceflinger system_file:file x_file_perms;

# for sf_dump
userdebug_or_eng(`
allow surfaceflinger sf_bqdump_data_file:{dir file} {relabelto open create read write getattr };
allow surfaceflinger sf_bqdump_data_file:dir {search add_name};
')

# for driver access
allow surfaceflinger MTK_SMI_device:chr_file { read write open ioctl };

# for bootanimation
allow surfaceflinger bootanim:dir search;
allow surfaceflinger bootanim:file { read getattr open };

# for MTK Emulator HW GPU
allow surfaceflinger qemu_pipe_device:chr_file rw_file_perms;

# for SVP secure memory allocation
allow surfaceflinger proc_secmem:file { read write open ioctl };

# for watchdog
allow surfaceflinger anr_data_file:dir { relabelfrom read remove_name getattr };
allow surfaceflinger anr_data_file:file { rename getattr unlink open append};
allow surfaceflinger sf_rtt_file:dir { create search write add_name remove_name};
allow surfaceflinger sf_rtt_file:file { open read write create rename append getattr unlink};
allow surfaceflinger sf_rtt_file:dir {relabelto getattr};
allow surfaceflinger crash_dump:process sigchld;

# for BufferQueue check process name of em_svr
allow surfaceflinger em_svr:dir search;
allow surfaceflinger em_svr:file { read getattr open };

allow surfaceflinger mobicore_user_device:chr_file { read write ioctl open };

# take down the boot time for bootprof
allow surfaceflinger proc_bootprof:file write;

# Add permission for gpu access
allow surfaceflinger dri_device:chr_file { read write open ioctl };

# for rtt dump
allow surfaceflinger toolbox_exec:file rx_file_perms;

# Date : WK17.23
# Stage: O Migration, SQC
# Purpose: Allow to use HAL PQ
hal_client_domain(surfaceflinger, hal_mtk_pq)

# Date : WK17.23
# Stage: O Migration, SQC
# Purpose: Allow to use shared memory for HAL PQ
hal_client_domain(surfaceflinger, hal_allocator)

# Date : WK17.43
# Stage: O Migration, SQC
# purpose: Allow to SF communicate with HAL DFPS
hal_client_domain(surfaceflinger, hal_dfps)

allow surfaceflinger mtk_dfrc_device:chr_file rw_file_perms;

# Data: 2019/09/28
# Purpose: SurfaceFlinger need to call MMS to convert buffer format and PQ effect
hal_client_domain(surfaceflinger, hal_mtk_mms)

#allow get mtk_sec_video_path_support
get_prop(surfaceflinger, vendor_mtk_sec_video_path_support_prop)
get_prop(surfaceflinger, vendor_mtk_svp_on_mtee_support_prop)

# Date: 2021/07/02
# Operation: Allow 'getattr' for unlabeled:filesystem
allow surfaceflinger unlabeled:filesystem {getattr};

# Date: 2021/09/01
# Operation: Allow 'r_file_perms_no_map' for dmabuf_system_secure_heap_device:chr_file
allow surfaceflinger dmabuf_system_secure_heap_device:chr_file r_file_perms_no_map;

# Data: 2021/09/07
# Purpose: Call NpAgent
hal_client_domain(surfaceflinger, hal_neuralnetworks)

# Purpose: Netflix Widevine
allow surfaceflinger teei_client_device:chr_file rw_file_perms;
