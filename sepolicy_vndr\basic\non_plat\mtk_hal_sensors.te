# ==============================================================================
# Type Declaration
# ==============================================================================
type mtk_hal_sensors, domain;
type mtk_hal_sensors_exec, exec_type, file_type, vendor_file_type;

# ==============================================
# Common SEPolicy Rule
# ==============================================

init_daemon_domain(mtk_hal_sensors)

# call into system_server process (callbacks)
binder_call(mtk_hal_sensors, system_server)

#hwservicemanager
hal_server_domain(mtk_hal_sensors, hal_sensors)

# graphics allocator
allow mtk_hal_sensors hal_graphics_allocator_default:fd use;

# gpu device
allow mtk_hal_sensors gpu_device:dir create_dir_perms;
allow mtk_hal_sensors gpu_device:chr_file rw_file_perms;
allow mtk_hal_sensors dri_device:chr_file rw_file_perms;

# ion device
allow mtk_hal_sensors ion_device:dir create_dir_perms;
allow mtk_hal_sensors ion_device:chr_file rw_file_perms;

# system file
allow mtk_hal_sensors system_file:dir r_dir_perms;

# sensors input rw access
allow mtk_hal_sensors sysfs_sensor:dir r_dir_perms;
allow mtk_hal_sensors sysfs_sensor:file rw_file_perms;

# hal sensor for chr_file
allow mtk_hal_sensors hwmsensor_device:chr_file r_file_perms;

# Access sensor bio devices
allow mtk_hal_sensors sensorlist_device:chr_file rw_file_perms;
allow mtk_hal_sensors m_acc_misc_device:chr_file rw_file_perms;
allow mtk_hal_sensors m_als_misc_device:chr_file rw_file_perms;
allow mtk_hal_sensors m_ps_misc_device:chr_file rw_file_perms;
allow mtk_hal_sensors m_mag_misc_device:chr_file rw_file_perms;
allow mtk_hal_sensors m_gyro_misc_device:chr_file rw_file_perms;
allow mtk_hal_sensors m_baro_misc_device:chr_file rw_file_perms;
allow mtk_hal_sensors m_hmdy_misc_device:chr_file rw_file_perms;
allow mtk_hal_sensors m_act_misc_device:chr_file rw_file_perms;
allow mtk_hal_sensors m_pedo_misc_device:chr_file rw_file_perms;
allow mtk_hal_sensors m_situ_misc_device:chr_file rw_file_perms;
allow mtk_hal_sensors m_step_c_misc_device:chr_file rw_file_perms;
allow mtk_hal_sensors m_fusion_misc_device:chr_file rw_file_perms;
allow mtk_hal_sensors m_bio_misc_device:chr_file rw_file_perms;
allow mtk_hal_sensors hf_manager_device:chr_file rw_file_perms;

# Access mtk sensor setting and calibration node.
# for data
allow mtk_hal_sensors sensor_data_file:file create_file_perms;
allow mtk_hal_sensors sensor_data_file:dir create_dir_perms;

# for nvcfg
allow mtk_hal_sensors nvcfg_file:file create_file_perms;
allow mtk_hal_sensors nvcfg_file:dir create_dir_perms;

# Date : WK18.21
# Operation: P migration
# Purpose: Allow to search /mnt/vendor/nvdata for fstab when using NVM_Init()
allow mtk_hal_sensors mnt_vendor_file:dir search;

# Date : WK19.48
# Purpose: fix [vts_10.0_r2]VtsHalSensorsV2_0Target fail
allow mtk_hal_sensors merged_hal_service:fd use;

# Date : WK20.25
# Purpose: Allow to read /bus/platform/drivers/mtk_nanohub/state
allow mtk_hal_sensors sysfs_mtk_nanohub_state:file r_file_perms;
