# ==============================================
# MICROTRUST SEPolicy Rule
# ==============================================

# Set exec file type
type teei_hal_capi_exec, exec_type, vendor_file_type, file_type;

# Setup for domain transition
init_daemon_domain(teei_hal_capi)

# Set teei_hal_capi as server domain of hal_teei_capi
hal_server_domain(teei_hal_capi, hal_teei_capi)

# Access capi devices at all.
allow teei_hal_capi teei_client_device:chr_file rw_file_perms;

# Allow to use shared memory for HAL PQ
hal_client_domain(teei_hal_capi, hal_allocator)

# Allow to set soter prop
set_prop(teei_hal_capi, vendor_mtk_soter_teei_prop)
