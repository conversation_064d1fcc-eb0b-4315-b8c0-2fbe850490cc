# ==============================================
# Common SEPolicy Rule
# ==============================================

type mtk_hal_bluetooth_hwservice, hwservice_manager_type;

# Date: 2017/05/9
type mtk_hal_rild_hwservice, hwservice_manager_type;

# Date: 2017/06/07
# power hidl
type mtk_hal_power_hwservice, hwservice_manager_type;

# Date: 2017/06/12
# LBS HIDL
type mtk_hal_lbs_hwservice, hwservice_manager_type;

# Date: 2017/06/27
# IMSA HIDL
type mtk_hal_imsa_hwservice, hwservice_manager_type;

# Date: 2017/07/12
# NVRAM HIDL
type mtk_hal_nvramagent_hwservice, hwservice_manager_type;

# Date: 2017/07/19
# PQ HIDL
type mtk_hal_pq_hwservice, hwservice_manager_type;

# Date: 2017/07/20
# keymaster attestation hidl
type mtk_hal_keyattestation_hwservice, hwservice_manager_type;

# Date: 2018/05/25
# FM HIDL
type mtk_hal_fm_hwservice, hwservice_manager_type;

# Date: 2018/03/23
# log hidl
type mtk_hal_log_hwservice, hwservice_manager_type;

# Date: 2018/06/26
# em hidl
type mtk_hal_em_hwservice, hwservice_manager_type;

# Date: 2018/07/02
# MMS HIDL
type mtk_hal_mms_hwservice, hwservice_manager_type, mtk_safe_hwservice_manager_type;

type mtk_hal_atci_hwservice, hwservice_manager_type;

# Date: 2020/12/02
# MMAgent HIDL
type mtk_hal_mmagent_hwservice, hwservice_manager_type;

type mtk_hal_keymanage_hwservice, hwservice_manager_type;

# Date: 2019/06/12
# modem db filter hidl
type mtk_hal_md_dbfilter_hwservice, hwservice_manager_type;

# Date: 2019/07/16
# HDMI HIDL
type mtk_hal_hdmi_hwservice, hwservice_manager_type;

# Date: 2019/09/06
# BGService HIDL
type mtk_hal_bgs_hwservice, hwservice_manager_type;

# Date: 2019/07/04
# bluetooth audio hidl
type mtk_hal_bluetooth_audio_hwservice,hwservice_manager_type;

# Date: 2021/06/30
# composer extension HIDL
type mtk_hal_composer_ext_hwservice, hwservice_manager_type, protected_hwservice;

# GPU HIDL
type mtk_hal_gpu_hwservice, hwservice_manager_type;
type hal_mtkcodecservice_hwservice, hwservice_manager_type;
