# ==============================================
# Common SEPolicy Rule
# ==============================================

type mtk_dconfig_exec, exec_type, file_type, vendor_file_type;

init_daemon_domain(mtk_dconfig)
userdebug_or_eng(`
    allow mtk_dconfig mtk_hal_dplanner:fd use;
    allow mtk_dconfig mtk_hal_dplanner:fifo_file { w_file_perms getattr };
')
allow mtk_dconfig proc_chip:file r_file_perms;
allow mtk_dconfig sysfs_chipid:file r_file_perms;

#for setting
allow mtk_dconfig mtk_dconfig_exec:file execute_no_trans;
allow mtk_dconfig block_device:dir search;
allow mtk_dconfig boot_para_block_device:blk_file rw_file_perms;
