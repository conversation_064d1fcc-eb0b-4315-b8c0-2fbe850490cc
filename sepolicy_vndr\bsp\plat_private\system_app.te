# ==============================================
# Common SEPolicy Rule
# ==============================================

# Date: 2016/11/10
# Purpose: [MDM] Modem monitor config
# Package Name: com.mediatek.mdmconfig
set_prop(system_app, config_prop)

# Date : WK17.31
# Operation : Migration
# Purpose : Carrier express service on BSP
allow system_app mtk_carrierexpress_service:service_manager add;
set_prop(system_app, system_mtk_usp_srv_prop)
set_prop(system_app, system_mtk_persist_vendor_vzw_device_type_prop)
set_prop(system_app, system_mtk_uce_support_prop)
set_prop(system_app, system_mtk_rcs_support_prop)
set_prop(system_app, system_mtk_clientapi_support_prop)
set_prop(system_app, radio_prop)

# Date : W17.36
# Operation : SQC
# Purpose : allow MOTA app to write perist.update_finished, persist_update_started
set_prop(system_app, system_mtk_update_prop)

# Date : 2018/06/21
# Operation : P Migration
# Purpose : Allow AtciService to set ATCI property
set_prop(system_app, system_mtk_atci_sys_prop)

# Date : 2018/06/21
# Stage: Migration
# Purpose: allow system app to set RTT property
set_prop(system_app, system_mtk_rtt_prop)

#Date: 2018/07/18
# Operation: Migration
# Purpose: all System app to read property
get_prop(system_app, system_mtk_update_support_prop)

# Date : 2018/07/19
# Operation : P Migration
# Purpose : Allow to get AAL property
get_prop(system_app, system_mtk_aal_prop)

# Date: 2018/07/24
# Operation: Migration
# Purpose : Allow system-app to get system_mtk_wfc_opt_in_prop
# Package: com.mediatek.settings.ext
get_prop(system_app, system_mtk_wfc_opt_in_prop)

# Date : W18.28
# Operation : New feature for VSIM SQC
set_prop(system_app, system_mtk_vsim_sys_prop)

# Date : W18.28
# Operation : New feature for VSIM SQC
# Purpose : Allow OSI Permission control to tcp socket
allow system_app osi:tcp_socket create_socket_perms_no_ioctl;

# Date: 2018/10/26
# Purpose: Allow system app to get Subsidy Lock properties
get_prop(system_app, system_mtk_subsidylock_prop)

# Date: 2018/10/31
# Operation: Support SubsidyLock
set_prop(system_app, system_mtk_subsidylock_connect_prop)

# Date: 2019/02/14
# Purpose: Permission Control configure
# Package Name: com.mediatek.security
set_prop(system_app, system_mtk_permission_control_prop)

# Date:2019/05/21
# Purpose: Allow OP12settings to get opt_in_url_prop
# Package: com.mediatek.op12.settings;
get_prop(system_app, system_mtk_opt_in_url_prop)
get_prop(system_app, system_mtk_apptoken_required_prop)

# Date: 2019/06/06
# Operation: SimProcessor needs to read telephony log switch property
# Purpose: allow to get system_mtk_em_tel_log_prop
get_prop(system_app, system_mtk_em_tel_log_prop)

# Date : 2019/05/28
# Operation : Q Migration
# Purpose : allow to get mtk_cta_set and mtk_cta_support property
get_prop(system_app, system_mtk_cta_set_prop)

# Date : 2019/06/03
# Operation : Q Migration split build
# Purpose : allow to get system_mtk_rsc_sys_prop
get_prop(system_app, system_mtk_rsc_sys_prop)

# Date: 2019/07/22
# Purpose: Allow op07settings to get system_mtk_wfc_entitlement_prop
# Package: com.mediatek.op07.settings
get_prop(system_app, system_mtk_wfc_entitlement_prop)

# Date: 2019/07/23
# Purpose: allow to read init.svc.md_monitor property for calling SystemService.waitForState()
# Package Name: com.mediatek.mdmconfig
get_prop(system_app, system_mtk_init_svc_md_monitor_prop)

# Date : 2020/03/23
# Operation : Migration
# Purpose : Allow cta_networkdatacontroller_service to add AOSP service by ServiceManager
allow system_app cta_networkdatacontroller_service:service_manager add;

# MICROTRUST SEPolicy Rule
# Date : 2017/12/18
# Operation: ifaa integration
# Purpose: access for ifaa_service call
# Package: org.ifaa.android.service
add_service(system_app, teei_ifaa_service)

hal_client_domain(system_app, hal_teei_ifaa)
hal_client_domain(system_app, hal_teei_wechat)
