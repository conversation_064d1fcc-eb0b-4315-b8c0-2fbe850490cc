# ==============================================
# Common SEPolicy Rule
# ==============================================

# DMC HIDL
attribute hal_mtk_dmc;
attribute hal_mtk_dmc_client;
attribute hal_mtk_dmc_server;

# APM HIDL
attribute hal_mtk_apm;
attribute hal_mtk_apm_client;
attribute hal_mtk_apm_server;

# netdagent HIDL
attribute mtk_hal_netdagent;
attribute mtk_hal_netdagent_client;
attribute mtk_hal_netdagent_server;

attribute hal_mtk_wfo;
attribute hal_mtk_wfo_client;
attribute hal_mtk_wfo_server;

attribute hal_presence;
attribute hal_presence_client;
attribute hal_presence_server;

attribute hal_videotelephony;
attribute hal_videotelephony_client;
attribute hal_videotelephony_server;

attribute hal_rcs;
attribute hal_rcs_client;
attribute hal_rcs_server;

attribute hal_dfps;
attribute hal_dfps_client;
attribute hal_dfps_server;

attribute hal_dplanner;
attribute hal_dplanner_client;
attribute hal_dplanner_server;

attribute mtk_hal_pplagent;
attribute mtk_hal_pplagent_client;
attribute mtk_hal_pplagent_server;

attribute hal_clientapi;
attribute hal_clientapi_client;
attribute hal_clientapi_server;

# Trustonic Attribute declarations
attribute hal_tee_client;
attribute hal_tee_server;
attribute hal_tee;

attribute hal_teeregistry_client;
attribute hal_teeregistry_server;
attribute hal_teeregistry;

# MDM HIDL
attribute md_monitor_hal_client;
attribute md_monitor_hal_server;
attribute md_monitor_hal;

# OMADM HIDL
attribute hal_mtk_omadm;
attribute hal_mtk_omadm_client;
attribute hal_mtk_omadm_server;

# HDCP HIDL
attribute hal_tesiai_hdcp;
attribute hal_tesiai_hdcp_client;
attribute hal_tesiai_hdcp_server;

# touch HIDL
attribute hal_nwk_opt;
attribute hal_nwk_opt_client;
attribute hal_nwk_opt_server;

# touchll HIDL
attribute hal_mtk_touchll;
attribute hal_mtk_touchll_client;
attribute hal_mtk_touchll_server;

# thp HIDL
attribute hal_mtk_thp;
attribute hal_mtk_thp_client;
attribute hal_mtk_thp_server;

# ==============================================
# MICROTRUST Attribute declarations
# ==============================================
# THH ATTESTATION HIDL
attribute hal_teei_thh;
attribute hal_teei_thh_client;
attribute hal_teei_thh_server;

# TUI ATTESTATION HIDL
attribute hal_teei_tui;
attribute hal_teei_tui_client;
attribute hal_teei_tui_server;

# IFAA ATTESTATION HIDL
attribute hal_teei_ifaa;
attribute hal_teei_ifaa_client;
attribute hal_teei_ifaa_server;

# CLIENT API ATTESTATION HIDL
attribute hal_teei_capi;
attribute hal_teei_capi_client;
attribute hal_teei_capi_server;

# Wechat ATTESTATION HIDL
attribute hal_teei_wechat;
attribute hal_teei_wechat_client;
attribute hal_teei_wechat_server;
