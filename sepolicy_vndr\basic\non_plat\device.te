# ==============================================
# Common SEPolicy Rule
# ==============================================
# Device types
type devmap_device, dev_type;
type ttyMT_device, dev_type;
type ttyS_device, dev_type;
type ttySDIO_device, dev_type;
type vmodem_device, dev_type;
type stpwmt_device, dev_type;
type conninfra_device, dev_type;
type conn_pwr_device, dev_type;
type conn_scp_device, dev_type;
type wmtdetect_device, dev_type;
type wmtWifi_device, dev_type;
type stpbt_device, dev_type;
type fw_log_bt_device, dev_type;
type stpant_device, dev_type;
type fm_device, dev_type, mlstrustedobject;
type gps_emi_device, dev_type;
type stpgps_device, dev_type;
type gps2scp_device, dev_type;
type gps_pwr_device, dev_type;
type gpsdl_device, dev_type;
type connfem_device, dev_type;
type fw_log_gps_device, dev_type;
type fw_log_wmt_device, dev_type;
type fw_log_wifi_device, dev_type;
type fw_log_ics_device, dev_type;
type fw_log_wifimcu_device, dev_type;
type fw_log_btmcu_device, dev_type;
type pmem_multimedia_device, dev_type;
type mt6516_isp_device, dev_type;
type mt6516_IDP_device, dev_type;
type mt9p012_device, dev_type;
type mt6516_jpeg_device, dev_type;
type FM50AF_device, dev_type;
type DW9714AF_device, dev_type;
type DW9814AF_device, dev_type;
type AK7345AF_device, dev_type;
type DW9714A_device, dev_type;
type LC898122AF_device, dev_type;
type LC898212AF_device, dev_type;
type BU6429AF_device, dev_type;
type AD5820AF_device, dev_type;
type DW9718AF_device, dev_type;
type BU64745GWZAF_device, dev_type;
type MAINAF_device, dev_type;
type MAIN2AF_device, dev_type;
type MAIN3AF_device, dev_type;
type MAIN4AF_device, dev_type;
type SUBAF_device, dev_type;
type SUB2AF_device, dev_type;
type M4U_device_device, dev_type;
type Vcodec_device, dev_type;
type MJC_device, dev_type;
type smartpa_device, dev_type;
type smartpa1_device, dev_type;
type tahiti_device, dev_type;
type uio0_device, dev_type;
type xt_qtaguid_device, dev_type;
type rfkill_device, dev_type;
type sw_sync_device, dev_type, mlstrustedobject;
type sec_device, dev_type;
type hid_keyboard_device, dev_type;
type btn_device, dev_type;
type uinput_device, dev_type;
type TV_out_device, dev_type;
type gz_device, dev_type;
type camera_sysram_device, dev_type;
type camera_mem_device, dev_type;
type camera_isp_device, dev_type;
type camera_dip_device, dev_type;
type camera_dpe_device, dev_type;
type camera_tsf_device, dev_type;
type camera_fdvt_device, dev_type;
type camera_rsc_device, dev_type;
type camera_gepf_device, dev_type;
type camera_wpe_device, dev_type;
type camera_owe_device, dev_type;
type camera_mfb_device, dev_type;
type camera_pda_device, dev_type;
type camera_pipemgr_device, dev_type;
type mtk_hcp_device, dev_type;
type mtk_ccd_device, dev_type;
type mtk_v4l2_media_device, dev_type;
type ccu_device, dev_type;
type gpueb_device, dev_type;
type vcp_device, dev_type;
type mvpu_algo_device, dev_type;
type vpu_device, dev_type, mlstrustedobject;
type mdla_device, dev_type, mlstrustedobject;
type apusys_device, dev_type;
type mtk_jpeg_device, dev_type;
type kd_camera_hw_device, dev_type;
type seninf_device, dev_type;
type kd_camera_flashlight_device, dev_type;
type flashlight_device, dev_type;
type kd_camera_hw_bus2_device, dev_type;
type MATV_device, dev_type;
type mt_otg_test_device, dev_type;
type mt_mdp_device, dev_type;
type mtkg2d_device, dev_type;
type misc_sd_device, dev_type;
type mtk_sched_device, dev_type;
type ampc0_device, dev_type;
type mmp_device, dev_type;
type ttyGS_device, dev_type;
type CAM_CAL_DRV_device, dev_type;
type CAM_CAL_DRV1_device, dev_type;
type CAM_CAL_DRV2_device, dev_type;
type camera_eeprom_device, dev_type;
type seninf_n3d_device, dev_type;
type MTK_SMI_device, dev_type;
type mtk_cmdq_device, dev_type;
type mtk_mdp_device, dev_type;
type mtk_mdp_sync_device, dev_type;
type mtk_fmt_sync_device, dev_type;
type mtk_fmt_device, dev_type;
type mtk_rrc_device, dev_type;
type ebc_device, dev_type;
type vow_device, dev_type;
type MT6516_H264_DEC_device, dev_type;
type MT6516_Int_SRAM_device, dev_type;
type MT6516_MM_QUEUE_device, dev_type;
type MT6516_MP4_DEC_device, dev_type;
type MT6516_MP4_ENC_device, dev_type;
type sensor_device, dev_type;
type ccci_device, dev_type;
type ccci_monitor_device, dev_type;
type gsm0710muxd_device, dev_type;
type eemcs_device, dev_type;
type emd_device, dev_type;
type st21nfc_device, dev_type;
type st54spi_device, dev_type;
type mmcblk_device, dev_type;
type BOOT_device, dev_type;
type MT_pmic_device, dev_type;
type aal_als_device, dev_type;
type accdet_device, dev_type;
type android_device, dev_type;
type bmtpool_device, dev_type;
type bootimg_device, dev_type;
type btif_device, dev_type;
type cache_device, dev_type;
type cpu_dma_latency_device, dev_type;
type dummy_cam_cal_device, dev_type;
type ebr_device, dev_type;
type expdb_device, dev_type;
type fat_device, dev_type;
type logo_device, dev_type;
type loop-control_device, dev_type;
type mbr_device, dev_type;
type met_device, dev_type;
type misc_device, dev_type;
type misc2_device, dev_type;
type mtfreqhopping_device, dev_type;
type mtgpio_device, dev_type;
type mtk_kpd_device, dev_type;
type network_device, dev_type;
type nvram_device, dev_type;
type pmt_device, dev_type;
type preloader_device, dev_type;
type pro_info_device, dev_type;
type protect_f_device, dev_type;
type protect_s_device, dev_type;
type psaux_device, dev_type;
type ptyp_device, dev_type;
type recovery_device, dev_type;
type sec_ro_device, dev_type;
type seccfg_device, dev_type;
type tee_part_device, dev_type;
type snapshot_device, dev_type;
type tgt_device, dev_type;
type touch_device, dev_type;
type tpd_em_log_device, dev_type;
type ttyp_device, dev_type;
type uboot_device, dev_type;
type uibc_device, dev_type;
type usrdata_device, dev_type;
type zram0_device, dev_type;
type hwzram0_device, dev_type;
type RT_Monitor_device, dev_type;
type kick_powerkey_device, dev_type;
type agps_device, dev_type;
type mnld_device, dev_type;
type geo_device, dev_type;
type mdlog_device, dev_type;
type md32_device, dev_type;
type scp_device, dev_type;
type adsp_device, dev_type;
type audio_scp_device, dev_type;
type sspm_device, dev_type;
type etb_device, dev_type;
type MT_pmic_adc_cali_device, dev_type;
type mtk-adc-cali_device, dev_type;
type MT_pmic_cali_device,dev_type;
type otp_device, dev_type;
type otp_part_block_device, dev_type;
type qemu_pipe_device, dev_type;
type icusb_device, dev_type;
type nlop_device, dev_type;
type irtx_device, dev_type;
type pmic_ftm_device, dev_type;
type charger_ftm_device, dev_type;
type shf_device, dev_type;
type keyblock_device, dev_type;
type offloadservice_device, dev_type;
type ttyACM_device, dev_type;
type hrm_device, dev_type;
type lens_device, dev_type;
type nvdata_device, dev_type;
type mcf_ota_block_device,dev_type;
type nvcfg_device, dev_type;
type expdb_block_device, dev_type;
type misc2_block_device, dev_type;
type logo_block_device, dev_type;
type para_block_device, dev_type;
type tee_block_device, dev_type;
type seccfg_block_device, dev_type;
type secro_block_device, dev_type;
type preloader_block_device, dev_type;
type lk_block_device, dev_type;
type protect1_block_device, dev_type;
type protect2_block_device, dev_type;
type keystore_block_device, dev_type;
type oemkeystore_block_device, dev_type;
type sec1_block_device, dev_type;
type md1img_block_device, dev_type;
type md1dsp_block_device, dev_type;
type md1arm7_block_device, dev_type;
type md3img_block_device, dev_type;
type mmcblk1_block_device, dev_type;
type mmcblk1p1_block_device, dev_type;
type bootdevice_block_device, dev_type;
type odm_block_device, dev_type;
type oem_block_device, dev_type;
type vendor_block_device, dev_type;
type loader_ext_block_device, dev_type;
type spm_device, dev_type;
type persist_block_device, dev_type;
type md_block_device, dev_type;
type spmfw_block_device, dev_type;
type mcupmfw_block_device, dev_type;
type scp_block_device, dev_type;
type sspm_block_device, dev_type;
type dsp_block_device, dev_type;
type ppl_block_device, dev_type;
type nvcfg_block_device, dev_type;
type ancservice_device, dev_type;
type mbim_device, dev_type;
type audio_ipi_device, dev_type;
type cam_vpu_block_device,dev_type;
type boot_para_block_device,dev_type;
type mtk_dfrc_device, dev_type;
type vbmeta_block_device, dev_type;
type alarm_device, dev_type;
type mdp_device, dev_type;
type mrdump_device, dev_type;
type kb_block_device,dev_type;
type dkb_block_device,dev_type;
type mtk_radio_device, dev_type;
type dpm_block_device, dev_type;
type audio_dsp_block_device, dev_type;
type gz_block_device, dev_type;
type pi_img_device, dev_type;
type vpud_device, dev_type;
type vcu_device, dev_type;
type mml_pq_device, dev_type;

##########################
# Sensor common Devices Start
#
type hwmsensor_device, dev_type;
type msensor_device, dev_type;
type gsensor_device, dev_type;
type als_ps_device, dev_type;
type gyroscope_device, dev_type;
type barometer_device,dev_type;
type humidity_device,dev_type;
type biometric_device,dev_type;
type sensorlist_device,dev_type;
type hf_manager_device,dev_type;

##########################
# Sensor Devices Start
#
type m_batch_misc_device, dev_type;

##########################
# Sensor bio Devices Start
#
type m_als_misc_device, dev_type;
type m_ps_misc_device, dev_type;
type m_baro_misc_device, dev_type;
type m_hmdy_misc_device, dev_type;
type m_acc_misc_device, dev_type;
type m_mag_misc_device, dev_type;
type m_gyro_misc_device, dev_type;
type m_act_misc_device, dev_type;
type m_pedo_misc_device, dev_type;
type m_situ_misc_device, dev_type;
type m_step_c_misc_device, dev_type;
type m_fusion_misc_device, dev_type;
type m_bio_misc_device, dev_type;

# Date : 2016/07/11
# Operation : Migration
# Purpose : Add permission for gpu access
type dri_device, dev_type, mlstrustedobject;

# Date : 2021/07/09
# Operation : S Migration
# Purpose : Add permission for ABOTA
type postinstall_block_device, dev_type;

# Date : 2021/08/27
# Operation : S Migration
# Purpose : Add permission for wifi proxy
type ccci_wifi_proxy_device, dev_type;

# Date : 2016/06/01
# Operation: TEEI integration
# Purpose: access for fp device and client device of TEEI
type teei_fp_device, dev_type;
type teei_client_device, dev_type, mlstrustedobject;
type teei_config_device, dev_type;
type utr_tui_device, dev_type;
type teei_vfs_device, dev_type;
type teei_rpmb_device, dev_type;
type ut_keymaster_device, dev_type;

# Date : 2019/07/19
# Operation : Add newwork optimization feature
# Purpose : Add permission for nwk
type nwkopt_device, dev_type;
type tx_device, dev_type;

# Date : 2019/11/07
# Operation : Add thp feature
# Purpose : Add permission for thp
type gdix_mt_wrapper_device, dev_type, fs_type;
type gdix_thp_device, dev_type, fs_type;

type mddp_device, dev_type;

type tkcore_admin_device, dev_type, mlstrustedobject;
type tkcore_block_device, dev_type;

# mobicore device type
type mobicore_admin_device, dev_type;
type mobicore_user_device, dev_type, mlstrustedobject;
type mobicore_tui_device, dev_type;

# teeperf device type
type teeperf_device, dev_type, mlstrustedobject;

type rpmb_block_device, dev_type;
type rpmb_device, dev_type;

type fingerprint_device, dev_type;

# widevine device type
type widevine_drv_device, dev_type;

# Date:2021/08/05
# Purpose: permission for audioserver to use ccci node
type ccci_aud_device, dev_type;

# Date:2021/07/27
# Purpose: permission for CCB user
type ccci_ccb_device, dev_type;
# Purpose: permission for md_monitor
type ccci_mdmonitor_device, dev_type;

# Date: 2021/09/26
# Operator: S migration
# Purpose: Add permission for vilte
type ccci_vts_device, dev_type;
