# ==============================================
# Common SEPolicy Rule
# ==============================================

# Date : 2016/07/12
# Purpose : Issue submitter need creat folder on SD card
allow system_app vfat:dir create_dir_perms;

# Date: 2017/07/01
# Change to simple policy
allow system_app media_rw_data_file:dir rw_dir_perms;
allow system_app media_rw_data_file:file rw_file_perms;

# Purpose: receive dropbox message
allow system_app system_server:unix_stream_socket connectto;
allow system_app crash_dump:unix_stream_socket connectto;

# Date : 2021/01/19
# Purpose : access power hal
hal_client_domain(system_app, hal_power)

# Date: 2021/09/06
# Purpose: To enable Google UCE Flow
set_prop(system_app, system_mtk_rcs_single_reg_support_prop)