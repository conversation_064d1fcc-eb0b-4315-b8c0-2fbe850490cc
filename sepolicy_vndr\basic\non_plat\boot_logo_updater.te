# ==============================================
# Policy File of /system/bin/boot_logo_updater Executable File

# ==============================================
# Common SEPolicy Rule
# ==============================================

# Date : WK14.43
# Operation : Migration
# Purpose : To access file directories and files like logo.bin
allow boot_logo_updater logo_block_device:blk_file r_file_perms;

# To access block files at  /dev/block/mmcblk0 ir /dev/block/sdc
allow boot_logo_updater bootdevice_block_device:blk_file r_file_perms;

#To access file at  /dev/logo
allow boot_logo_updater logo_device:chr_file r_file_perms;

# To access file at   /proc/lk_env
allow boot_logo_updater proc_lk_env:file rw_file_perms;

# Date : WK16.25
# Operation : Global_Device/Uniservice Feature
# Purpose : for it to read-write SysEnv data
allow boot_logo_updater para_block_device:blk_file rw_file_perms;
# Allow ReadDefaultFstab().
read_fstab(boot_logo_updater)
