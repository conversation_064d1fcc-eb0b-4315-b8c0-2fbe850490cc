# ==============================================
# Common SEPolicy Rule
# ==============================================

# system_internal_prop      -- Properties used only in /system
# system_restricted_prop    -- Properties which can't be written outside system
# system_public_prop        -- Properties with no restrictions
# system_vendor_config_prop -- Properties which can be written only by vendor_init
# vendor_internal_prop      -- Properties used only in /vendor
# vendor_restricted_prop    -- Properties which can't be written outside vendor
# vendor_public_prop        -- Properties with no restrictions

# Properties used only in /system
system_internal_prop(system_mtk_aal_prop)
system_internal_prop(system_mtk_acs_support_prop)
system_internal_prop(system_mtk_acs_url_prop)
system_internal_prop(system_mtk_acs_version_prop)
system_internal_prop(system_mtk_amsaal_prop)
system_internal_prop(system_mtk_apptoken_required_prop)
system_internal_prop(system_mtk_atci_sys_prop)
system_internal_prop(system_mtk_capctrl_sys_prop)
system_internal_prop(system_mtk_bluetooth_prop)
system_internal_prop(system_mtk_capability_switch_prop)
system_internal_prop(system_mtk_cdma_ecm_prop)
system_internal_prop(system_mtk_cdma_prop)
system_internal_prop(system_mtk_clientapi_support_prop)
system_internal_prop(system_mtk_common_data_prop)
system_internal_prop(system_mtk_cta_set_prop)
system_internal_prop(system_mtk_ctl_atcid_daemon_u_prop)
system_internal_prop(system_mtk_ctl_campostalgo_prop)
system_internal_prop(system_mtk_ctmslot_prop)
system_internal_prop(system_mtk_debug_sf_prop)
system_internal_prop(system_mtk_debug_bq_prop)
system_internal_prop(system_mtk_duraspeed_drop_caches_prop)
system_internal_prop(system_mtk_em_tel_log_prop)
system_internal_prop(system_mtk_imsconfig_prop)
system_internal_prop(system_mtk_logmuch_prop)
system_internal_prop(system_mtk_media_wfd_prop)
system_internal_prop(system_mtk_opt_in_url_prop)
system_internal_prop(system_mtk_permission_control_prop)
system_internal_prop(system_mtk_persist_vendor_vzw_device_type_prop)
system_internal_prop(system_mtk_rcs_support_prop)
system_internal_prop(system_mtk_rsc_sys_prop)
system_internal_prop(system_mtk_rtt_prop)
system_internal_prop(system_mtk_selfreg_prop)
system_internal_prop(system_mtk_subsidylock_connect_prop)
system_internal_prop(system_mtk_supp_serv_prop)
system_internal_prop(system_mtk_telecom_vibrate_prop)
system_internal_prop(system_mtk_terservice_prop)
system_internal_prop(system_mtk_uce_support_prop)
system_internal_prop(system_mtk_update_prop)
system_internal_prop(system_mtk_usb_tethering_prop)
system_internal_prop(system_mtk_usp_srv_prop)
system_internal_prop(system_mtk_vsim_sys_prop)
system_internal_prop(system_mtk_wfc_entitlement_prop)
system_internal_prop(system_mtk_wfc_opt_in_prop)
system_internal_prop(system_mtk_world_phone_prop)
system_internal_prop(system_mtk_ctm_prop)
system_internal_prop(system_mtk_graphics_sf_gll_prop)
system_internal_prop(system_mtk_subsidylock_prop)
system_internal_prop(system_mtk_gwsd_prop)
system_internal_prop(system_mtk_vodata_prop)
system_internal_prop(system_mtk_fd_prop)
system_internal_prop(system_mtk_dbg_ims_prop)

# Properties which can be written only by vendor_init
system_vendor_config_prop(system_mtk_graphics_sf_gll_ro_prop)
system_vendor_config_prop(system_mtk_update_support_prop)

# Properties which can't be written outside vendor
#=============allow netflix read property==============
system_public_prop(netflix_bsp_rev_prop)

# Properties with can't be accessed by device-sepcific domains
typeattribute system_mtk_aal_prop                            extended_core_property_type;
typeattribute system_mtk_acs_support_prop                    extended_core_property_type;
typeattribute system_mtk_acs_url_prop                        extended_core_property_type;
typeattribute system_mtk_acs_version_prop                    extended_core_property_type;
typeattribute system_mtk_amsaal_prop                         extended_core_property_type;
typeattribute system_mtk_apptoken_required_prop              extended_core_property_type;
typeattribute system_mtk_atci_sys_prop                       extended_core_property_type;
typeattribute system_mtk_capctrl_sys_prop                    extended_core_property_type;
typeattribute system_mtk_bluetooth_prop                      extended_core_property_type;
typeattribute system_mtk_capability_switch_prop              extended_core_property_type;
typeattribute system_mtk_cdma_ecm_prop                       extended_core_property_type;
typeattribute system_mtk_cdma_prop                           extended_core_property_type;
typeattribute system_mtk_clientapi_support_prop              extended_core_property_type;
typeattribute system_mtk_common_data_prop                    extended_core_property_type;
typeattribute system_mtk_cta_set_prop                        extended_core_property_type;
typeattribute system_mtk_ctl_atcid_daemon_u_prop             extended_core_property_type;
typeattribute system_mtk_ctl_campostalgo_prop                extended_core_property_type;
typeattribute system_mtk_ctmslot_prop                        extended_core_property_type;
typeattribute system_mtk_debug_sf_prop                       extended_core_property_type;
typeattribute system_mtk_debug_bq_prop                       extended_core_property_type;
typeattribute system_mtk_duraspeed_drop_caches_prop          extended_core_property_type;
typeattribute system_mtk_em_tel_log_prop                     extended_core_property_type;
typeattribute system_mtk_heavy_loading_prop                  extended_core_property_type;
typeattribute system_mtk_imsconfig_prop                      extended_core_property_type;
typeattribute system_mtk_logmuch_prop                        extended_core_property_type;
typeattribute system_mtk_media_wfd_prop                      extended_core_property_type;
typeattribute system_mtk_opt_in_url_prop                     extended_core_property_type;
typeattribute system_mtk_permission_control_prop             extended_core_property_type;
typeattribute system_mtk_persist_vendor_vzw_device_type_prop extended_core_property_type;
typeattribute system_mtk_rcs_support_prop                    extended_core_property_type;
typeattribute system_mtk_rsc_sys_prop                        extended_core_property_type;
typeattribute system_mtk_rtt_prop                            extended_core_property_type;
typeattribute system_mtk_selfreg_prop                        extended_core_property_type;
typeattribute system_mtk_subsidylock_connect_prop            extended_core_property_type;
typeattribute system_mtk_subsidylock_prop                    extended_core_property_type;
typeattribute system_mtk_supp_serv_prop                      extended_core_property_type;
typeattribute system_mtk_telecom_vibrate_prop                extended_core_property_type;
typeattribute system_mtk_terservice_prop                     extended_core_property_type;
typeattribute system_mtk_uce_support_prop                    extended_core_property_type;
typeattribute system_mtk_update_prop                         extended_core_property_type;
typeattribute system_mtk_update_support_prop                 extended_core_property_type;
typeattribute system_mtk_usb_tethering_prop                  extended_core_property_type;
typeattribute system_mtk_usp_srv_prop                        extended_core_property_type;
typeattribute system_mtk_vsim_sys_prop                       extended_core_property_type;
typeattribute system_mtk_wfc_entitlement_prop                extended_core_property_type;
typeattribute system_mtk_wfc_opt_in_prop                     extended_core_property_type;
typeattribute system_mtk_world_phone_prop                    extended_core_property_type;
typeattribute system_mtk_ctm_prop                            extended_core_property_type;
typeattribute system_mtk_gwsd_prop                           extended_core_property_type;
typeattribute system_mtk_vodata_prop                         extended_core_property_type;
typeattribute system_mtk_fd_prop                             extended_core_property_type;
typeattribute system_mtk_dbg_ims_prop                        extended_core_property_type;
