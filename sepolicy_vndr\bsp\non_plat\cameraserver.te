# ==============================================
# Common SEPolicy Rule
# ==============================================

# Date : WK16.29
# Operation : Migration
# Purpose : Add permission for gpu access
allow cameraserver dri_device:chr_file rw_file_perms;

# Date : WK16.30
# Operation : Migration
# Purpose : allow camera to save raw data on sdcard
allow cameraserver fuse:dir create_dir_perms;
allow cameraserver fuse:file create_file_perms;

# Date : WK16.33
# Operation : Migration
# Purpose : Dump camera buffer to sdcard for debug
allow cameraserver sdcardfs:dir create_dir_perms;
allow cameraserver sdcardfs:file create_file_perms;
allow cameraserver media_rw_data_file:dir create_dir_perms;
allow cameraserver media_rw_data_file:file create_file_perms;

# Date : WK17.23
# Stage: O Migration, SQC
# Purpose: Allow to use shared memory for HAL PQ
hal_client_domain(cameraserver, hal_allocator)

# Date : WK17.31
# Operation : ViLTE
# Purpose : for ViLTE - set VTservice has permission to access me
binder_call(cameraserver, vtservice)
