# ==============================================
# Policy File of /vendor/bin/rcs_volte_stack Executable File

# ==============================================
# Common SEPolicy Rule
# ==============================================
type rcs_volte_stack_exec, exec_type, file_type, vendor_file_type;

init_daemon_domain(rcs_volte_stack)

# Date : WK14.42
# Operation : Migration
# Purpose : for VoLTE L early bring up and first call
allow rcs_volte_stack vendor_shell_exec:file rx_file_perms;
allow rcs_volte_stack self:key_socket { write read create setopt };
allow rcs_volte_stack self:capability { net_admin setuid setgid };
allow rcs_volte_stack self:tcp_socket create_stream_socket_perms;
allow rcs_volte_stack self:udp_socket create_stream_socket_perms;
allow rcs_volte_stack node:udp_socket node_bind;
allow rcs_volte_stack node:tcp_socket node_bind;
allow rcs_volte_stack port:tcp_socket { name_bind name_connect };
allow rcs_volte_stack port:udp_socket name_bind;
allow rcs_volte_stack fwmarkd_socket:sock_file write;

allow rcs_volte_stack rcs_volte_stack_socket:sock_file write;
allow rcs_volte_stack self:netlink_xfrm_socket { write bind create read nlmsg_write nlmsg_read };

# Date : W1849
# Operation : Migration
# Purpose : for TMO ROI support Ipsec tunnel
set_prop(rcs_volte_stack, vendor_mtk_network_prop)
