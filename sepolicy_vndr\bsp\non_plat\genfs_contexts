# ==============================================
# Common SEPolicy Rule
# ==============================================

##########################
# proc files
#
genfscon proc /secmem0 u:object_r:proc_secmem:s0

# TrustKernel TEE proc
genfscon proc /tkcore u:object_r:proc_tkcore:s0

# For DuraSpeed
genfscon proc /cpu_loading/onoff                        u:object_r:proc_cpu_loading:s0
genfscon proc /cpu_loading/uevent_enable                u:object_r:proc_cpu_loading:s0
genfscon proc /cpu_loading/poltime_secs                 u:object_r:proc_cpu_loading:s0
genfscon proc /cpu_loading/poltime_nsecs                u:object_r:proc_cpu_loading:s0
genfscon proc /cpu_loading/overThrhld                   u:object_r:proc_cpu_loading:s0
genfscon proc /cpu_loading/specify_cpus                 u:object_r:proc_cpu_loading:s0
genfscon proc /cpu_loading/specify_overThrhld           u:object_r:proc_cpu_loading:s0

# For CachedAppOptimizer
genfscon proc /freqhopping u:object_r:proc_freqhopping:s0

##########################
# sysfs files
#
# Microtrust TEEI sysfs files
genfscon sysfs /devices/platform/utos u:object_r:teei_control_file:s0
genfscon sysfs /devices/utos          u:object_r:teei_control_file:s0

# for 7663 VTS NetdSELinuxTest.CheckProperMTULabels requirement
genfscon sysfs /devices/platform/soc/11250000.mmc/mmc_host/mmc2/mmc2:0001/mmc2:0001:1/net/wlan0/mtu u:object_r:sysfs_net:s0
genfscon sysfs /devices/platform/soc/11250000.mmc/mmc_host/mmc2/mmc2:0001/mmc2:0001:1/net/ap0/mtu u:object_r:sysfs_net:s0
genfscon sysfs /devices/platform/soc/11250000.mmc/mmc_host/mmc2/mmc2:0001/mmc2:0001:1/net/p2p0/mtu u:object_r:sysfs_net:s0
genfscon sysfs /devices/platform/11240000.msdc/mmc_host/mmc1/mmc1:0001/mmc1:0001:1/net/wlan0/mtu u:object_r:sysfs_net:s0
genfscon sysfs /devices/platform/11240000.msdc/mmc_host/mmc1/mmc1:0001/mmc1:0001:1/net/ap0/mtu u:object_r:sysfs_net:s0
genfscon sysfs /devices/platform/11240000.msdc/mmc_host/mmc1/mmc1:0001/mmc1:0001:1/net/p2p0/mtu u:object_r:sysfs_net:s0

# for 7668 VTS NetdSELinuxTest.CheckProperMTULabels requirement
genfscon sysfs /devices/platform/soc/11170000.sdio/mmc_host/mmc1/mmc1:0001/mmc1:0001:1/net/wlan0/mtu u:object_r:sysfs_net:s0
genfscon sysfs /devices/platform/soc/11170000.sdio/mmc_host/mmc1/mmc1:0001/mmc1:0001:1/net/ap0/mtu   u:object_r:sysfs_net:s0
genfscon sysfs /devices/platform/soc/11170000.sdio/mmc_host/mmc1/mmc1:0001/mmc1:0001:1/net/p2p0/mtu  u:object_r:sysfs_net:s0

# Date : 2020/09/09
# Purpose: for otg access
genfscon sysfs /devices/platform/bus/mt_usb/musb-hdrc/usb1 u:object_r:sysfs_usb_nonplat:s0

# For chager enable fast charging algorithm
genfscon sysfs /devices/platform/charger/fast_chg_indicator   u:object_r:sysfs_fs_chg_file:s0

# Date: 2021/08/10
# Operation: S migration
# Purpose: Add permission for inputReader search every input device path including "power_supply"
genfscon sysfs /devices/virtual/power_supply u:object_r:sysfs_power_supply:s0
