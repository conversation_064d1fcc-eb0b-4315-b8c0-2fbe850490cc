# ==============================================
# Common SEPolicy Rule
# ==============================================

# ccci device for internal modem
allow emdlogger ccci_mdl_device:chr_file rw_file_perms;
allow emdlogger ccci_ccb_device:chr_file rw_file_perms;
#add for read /dev/ccci_md1_sta
allow emdlogger ccci_device:chr_file rw_file_perms;

# eemcs device for external modem
allow emdlogger eemcs_device:chr_file rw_file_perms;

# C2K project SDIO device for external modem ttySDIO2 control port, ttySDIO8 log port
allow emdlogger ttySDIO_device:chr_file rw_file_perms;

# C2K project modem device for external modem vmodem start/stop/ioctl modem
allow emdlogger vmodem_device:chr_file rw_file_perms;

# usb device ttyGSx for modem logger usb logging
allow emdlogger ttyGS_device:chr_file rw_file_perms;

# for modem logging sdcard access
allow emdlogger sdcard_type:dir create_dir_perms;
allow emdlogger sdcard_type:file create_file_perms;

# modem logger access on /data/mdlog
allow emdlogger mdlog_data_file:dir { create_dir_perms relabelto };
allow emdlogger mdlog_data_file:fifo_file create_file_perms;
allow emdlogger mdlog_data_file:file create_file_perms;

# modem logger control port access /dev/ttyC1
allow emdlogger mdlog_device:chr_file rw_file_perms;

# modem logger SD logging in factory mode
allow emdlogger vfat:dir create_dir_perms;
allow emdlogger vfat:file create_file_perms;

# modem logger permission in storage in android M version
allow emdlogger mnt_user_file:dir search;
allow emdlogger mnt_user_file:lnk_file r_file_perms;
allow emdlogger storage_file:lnk_file r_file_perms;

# permission for storage link access in vzw Project
allow emdlogger mnt_media_rw_file:dir search;

# permission for use SELinux API
# avc: denied { read } for pid=576 comm="emdlogger1" name="selinux_version" dev="rootfs"
allow emdlogger rootfs:file r_file_perms;

# permission for storage access storage
allow emdlogger storage_file:dir create_dir_perms;
allow emdlogger tmpfs:lnk_file r_file_perms;
allow emdlogger storage_file:file create_file_perms;

# permission for read boot mode
# avc: denied { open }  path="/sys/devices/virtual/BOOT/BOOT/boot/boot_mode" dev="sysfs"
allow emdlogger sysfs_boot_mode:file r_file_perms;

# Allow read to sys/kernel/ccci/* files
allow emdlogger sysfs_ccci:dir search;
allow emdlogger sysfs_ccci:file r_file_perms;

allow emdlogger sysfs_mdinfo:file r_file_perms;
allow emdlogger sysfs_mdinfo:dir search;

# Allow read avc: denied { read } for name="mddb" dev="mmcblk0p25" ino=681
# scontext=u:r:emdlogger:s0 tcontext=u:object_r:system_file:s0 tclass=dir permissive=0
allow emdlogger system_file:dir r_dir_perms;

# purpose: allow emdlogger to access storage in N version
allow emdlogger media_rw_data_file:file create_file_perms;
allow emdlogger media_rw_data_file:dir create_dir_perms;

# For dynamic CCB buffer feature
# avc: denied { read write } for name="lk_env" dev="proc" ino=4026532192
# scontext=u:r:emdlogger:s0 tcontext=u:object_r:proc_lk_env:s0 tclass=file permissive=0
# avc: denied { read } for name="mmcblk0p3" dev="tmpfs" ino=8493 scontext=u:r:emdlogger:s0
# tcontext=u:object_r:para_block_device:s0 tclass=blk_file permissive=0
allow emdlogger para_block_device:blk_file rw_file_perms;
allow emdlogger proc_lk_env:file rw_file_perms;

allow emdlogger block_device:dir search;
allow emdlogger md_block_device:blk_file r_file_perms;
allow emdlogger self:capability chown;

# purpose: allow emdlogger to access persist.meta.connecttype
get_prop(emdlogger, vendor_mtk_meta_connecttype_prop)

# purpose: allow emdlogger to create socket
allow emdlogger port:tcp_socket { name_connect name_bind };
allow emdlogger emdlogger:tcp_socket {create_stream_socket_perms};
allow emdlogger node:tcp_socket node_bind;
allow emdlogger fwmarkd_socket:sock_file {write};
allow emdlogger netd:unix_stream_socket {connectto};
allow emdlogger self:tcp_socket {ioctl};


# Android P migration
get_prop(emdlogger, vendor_mtk_usb_prop)

# Date : WK19.12
# Operation: add permission to catch logs
# Purpose : get kernel and radio logs when modem exception
allow emdlogger kernel:system syslog_read;
allow emdlogger logcat_exec:file rx_file_perms;
allow emdlogger logdr_socket:sock_file w_file_perms;

# Add permission to access new bootmode file
allow emdlogger sysfs_boot_info:file r_file_perms;

# avc: denied { connectto } for path=006165653A72747464 scontext=u:r:emdlogger:s0
# tcontext=u:object_r:aee_aed_socket:s0 tclass=unix_stream_socket permissive=0
# security issue control
allow emdlogger crash_dump:unix_stream_socket connectto;
# Allow ReadDefaultFstab().
read_fstab(emdlogger)

# Date : 2021/07/06
# Purpose: add permission to access devie tree to get ccb gear info
allow emdlogger sysfs_soc_ccb_gear:file r_file_perms;
allow emdlogger sysfs_ccb_gear:file r_file_perms;

get_prop(emdlogger, vendor_mtk_atm_ipaddr_prop)