# ==============================================
# Common SEPolicy Rule
# ==============================================

# Date : WK16.33
# Purpose: Allow to access ged for gralloc_extra functions
allow appdomain proc_ged:file rw_file_perms;
allowxperm appdomain proc_ged:file ioctl { proc_ged_ioctls };

# Data : WK16.42
# Operator: Whitney bring up
# Purpose: call surfaceflinger due to powervr
allow appdomain surfaceflinger:fifo_file rw_file_perms;

# Date : W16.42
# Operation : Integration
# Purpose : DRM / DRI GPU driver required
allow appdomain gpu_device:dir search;

# Date : W17.41
# Operation: SQC
# Purpose : Allow HWUI to access perfmgr
allow appdomain proc_perfmgr:dir search;
allow appdomain proc_perfmgr:file r_file_perms;
allowxperm appdomain proc_perfmgr:file ioctl {
  PERFMGR_FPSGO_QUEUE
  PERFMGR_FPSGO_DEQUEUE
  PERFMGR_FPSGO_QUEUE_CONNECT
  PERFMGR_FPSGO_BQID
  PERFMGR_FPSGO_SWAP_BUFFER
  PERFMGR_FPSGO_SBE_RESCUE
};

# Date : W19.23
# Operation : Migration
# Purpose : For platform app com.android.gallery3d
allow { appdomain -isolated_app_all } radio_data_file:file rw_file_perms;

# Date : W19.23
# Operation : Migration
# Purpose : For app com.tencent.qqpimsecure
allowxperm appdomain appdomain:fifo_file ioctl SNDCTL_TMR_START;

# Date : W20.26
# Operation : Migration
# Purpose : For apps other than isolated_app_all call hidl
hwbinder_use({ appdomain -isolated_app_all })
get_prop({ appdomain -isolated_app_all }, hwservicemanager_prop)
allow { appdomain -isolated_app_all } hidl_manager_hwservice:hwservice_manager find;
binder_call({ appdomain -isolated_app_all }, mtk_safe_halserverdomain_type)
allow { appdomain -isolated_app_all } mtk_safe_hwservice_manager_type:hwservice_manager find;

# Date : 2021/04/24
# Operation: addwindow
# Purpose: Get the variable value of touch report rate
get_prop(appdomain, vendor_mtk_input_report_rate_prop)
