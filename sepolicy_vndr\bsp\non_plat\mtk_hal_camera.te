# ==============================================
# Common SEPolicy Rule
# ==============================================

# Date : WK17.27
# Stage: O Migration, SQC
# Purpose: Allow to use HAL PQ
hal_client_domain(mtk_hal_camera, hal_mtk_pq)

# Date : WK17.27
# Stage: O Migration, SQC
# Purpose: Allow to use shared memory for HAL PQ
hal_client_domain(mtk_hal_camera, hal_allocator)

# WK17.33 camera binder_call permission
binder_call(mtk_hal_camera, system_server)

# Date : WK17.33
# Stage: O Migration, SQC
# Purpose: Allow to set log too much property
set_prop(mtk_hal_camera, vendor_mtk_logmuch_prop)

# Date : WK17.35
# Stage: O Migration, SQC
# Purpose: camera notifies its status to thermal module
allow mtk_hal_camera proc_thermal:file rw_file_perms;
allow mtk_hal_camera proc_mtktz:file rw_file_perms;
get_prop(mtk_hal_camera, vendor_mtk_thermal_config_prop)
allow mtk_hal_camera proc_mtkcooler:file rw_file_perms;

# W17.36 callback to mtk_advcamserver
binder_call(mtk_hal_camera, mtk_advcamserver)

# Date : WK17.39
# Stage: O1 Migration, SQC
# Purpose : Update camera Vcodec device file
allow mtk_hal_camera Vcodec_device:chr_file rw_file_perms;

# Date : WK17.42
# Operation : Migration
# Purpose : Dump camera buffer to sdcard for debug
allow mtk_hal_camera sdcardfs:dir create_dir_perms;
allow mtk_hal_camera sdcardfs:file create_file_perms;

# Date : WK17.48
# Stage: O Migration
# Purpose: CCT
allow mtk_hal_camera cct_data_file:dir create_dir_perms;
allow mtk_hal_camera cct_data_file:file create_file_perms;
allow mtk_hal_camera cct_data_file:fifo_file create_file_perms;

# Date : WK18.22
# Stage: p Migration
# Purpose: NVRAM
allow mtk_hal_camera nvram_data_file:dir search;
allow mtk_hal_camera nvram_data_file:file rw_file_perms;
allow mtk_hal_camera nvram_data_file:lnk_file r_file_perms;
allow mtk_hal_camera nvdata_file:lnk_file r_file_perms;
allow mtk_hal_camera nvdata_file:dir create_dir_perms;
allow mtk_hal_camera nvdata_file:file create_file_perms;
allow mtk_hal_camera nvcfg_file:lnk_file r_file_perms;
allow mtk_hal_camera nvcfg_file:dir create_dir_perms;
allow mtk_hal_camera nvcfg_file:file create_file_perms;
allow mtk_hal_camera mnt_vendor_file:dir search;
allow mtk_hal_camera mnt_vendor_file:file create_file_perms;

# Date : WK18.29
# Stage: P Migration
# Purpose: Trustonic TEE access
allow mtk_hal_camera mobicore_user_device:chr_file rw_file_perms;

# Date : WK18.29
# Stage: P Migration
# Purpose: secure memory driver access
allow mtk_hal_camera proc_secmem:file rw_file_perms;

# Date : WK18.30
# Stage: P migration
# Purpose: sysfs boot mode access for HalSensor
allow mtk_hal_camera sysfs_boot_mode:file r_file_perms;

# Date : WK18.40
# Stage: P migration
# Purpose: Allow setprop for CCT
set_prop(mtk_hal_camera, vendor_mtk_camera_prop)

# Date : WK19.39
# Stage: Q Migration
# Purpose: Microtrust TEE access
allow mtk_hal_camera teei_client_device:chr_file rw_file_perms;

allow mtk_hal_camera mdla_device:chr_file rw_file_perms;

# Date: 2019/11/11
# Operation: For NDD
allow mtk_hal_camera vendor_camera_dump_file:dir create_dir_perms;
allow mtk_hal_camera vendor_camera_dump_file:file create_file_perms;

binder_call(mtk_hal_camera, remosaic_daemon)
allow mtk_hal_camera remosaic_daemon_service:service_manager find;

# Date : WK21.14
# Stage: R Migration
# Purpose: Allow memfd access by MPEG4Writer
allow mtk_hal_camera tmpfs:file rw_file_perms;
