# ==============================================
# Policy File of /vendor/bin/mtk_wmt_launcher Executable File

# ==============================================
# Type Declaration
# ==============================================
type mtk_wmt_launcher, domain;
type mtk_wmt_launcher_exec, exec_type, file_type, vendor_file_type;

# ==============================================
# Common SEPolicy Rule
# ==============================================
init_daemon_domain(mtk_wmt_launcher)

# set the property
set_prop(mtk_wmt_launcher, vendor_mtk_wmt_prop)

# add ioctl/open/read/write permission for mtk_wmt_launcher with /dev/stpwmt
allow mtk_wmt_launcher stpwmt_device:chr_file rw_file_perms;
allow mtk_wmt_launcher devpts:chr_file rw_file_perms;
allow mtk_wmt_launcher system_file:dir r_dir_perms;

# Date : W18.01
# Add for turn on SElinux in enforcing mode
allow mtk_wmt_launcher vendor_file:dir r_dir_perms;