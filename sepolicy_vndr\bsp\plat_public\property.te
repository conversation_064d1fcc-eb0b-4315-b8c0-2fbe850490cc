# ==============================================
# Common SEPolicy Rule
# ==============================================

# system_internal_prop      -- Properties used only in /system
# system_restricted_prop    -- Properties which can't be written outside system
# system_public_prop        -- Properties with no restrictions
# system_vendor_config_prop -- Properties which can be written only by vendor_init
# vendor_internal_prop      -- Properties used only in /vendor
# vendor_restricted_prop    -- Properties which can't be written outside vendor
# vendor_public_prop        -- Properties with no restrictions

# Properties which can't be written outside system
system_restricted_prop(system_mtk_init_svc_md_monitor_prop)

# Properties with no restrictions
system_public_prop(system_mtk_heavy_loading_prop)
system_public_prop(system_mtk_pkm_init_prop)
