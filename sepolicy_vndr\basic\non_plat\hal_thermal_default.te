# ==============================================
# Common SEPolicy Rule
# ==============================================

r_dir_file(hal_thermal_default, sysfs_therm)
allow hal_thermal_default sysfs_therm:file w_file_perms;

allow hal_thermal_default thermal_link_device:dir r_dir_perms;

allow hal_thermal_default proc_mtktz:dir search;
allow hal_thermal_default proc_mtktz:file r_file_perms;
allow hal_thermal_default proc_stat:file r_file_perms;

#for uevent handle
allow hal_thermal_default self:netlink_kobject_uevent_socket create_socket_perms_no_ioctl;
allow hal_thermal_default self:netlink_generic_socket create_socket_perms_no_ioctl;

#for thermal sysfs
allow hal_thermal_default sysfs_therm:file rw_file_perms;
allow hal_thermal_default sysfs_therm:dir search;

#for thermal hal socket
allow hal_thermal_default thermal_hal_socket:dir { rw_dir_perms setattr};
allow hal_thermal_default thermal_hal_socket:sock_file create_file_perms;

hal_client_domain(hal_thermal_default, hal_power);

# read thermal_config
get_prop(hal_thermal_default, vendor_thermal_prop)
