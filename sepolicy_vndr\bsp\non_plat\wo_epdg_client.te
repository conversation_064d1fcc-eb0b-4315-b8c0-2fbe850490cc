# ==============================================
# Policy File of /vendor/bin/wo_epdg_client Executable File

# ==============================================
# Common SEPolicy Rule
# ==============================================

type wo_epdg_client_exec, exec_type, file_type, vendor_file_type;

init_daemon_domain(wo_epdg_client)
net_domain(wo_epdg_client)

domain_auto_trans(wo_epdg_client, wo_starter_exec, wo_ipsec)
domain_auto_trans(wo_epdg_client, wo_charon_exec, wo_ipsec)
domain_auto_trans(wo_epdg_client, wo_stroke_exec, wo_ipsec)
domain_auto_trans(wo_epdg_client, netutils_wrapper_exec, netutils_wrapper)

# Date: WK14.52
# Operation : Feature for ePDG
# Purpose :  handle tunnel interface
allow wo_epdg_client self:tun_socket { relabelfrom relabelto create };
allow wo_epdg_client tun_device:chr_file rw_file_perms;
allow wo_epdg_client self:netlink_route_socket { setopt nlmsg_write read bind create nlmsg_read write getattr };
allow wo_epdg_client self:capability { net_admin net_raw kill setuid setgid sys_module };

# Purpose :  update ipsec deamon
allow wo_epdg_client wo_ipsec_exec:file rx_file_perms;

# Purpose : send signal to process (ipsec/charon)
allow wo_epdg_client wo_ipsec:process { signal sigkill signull };

# Purpose :  set property for debug messages
set_prop(wo_epdg_client, vendor_mtk_persist_epdg_prop)

# Purpose :  Query ePDG IP address
allow wo_epdg_client dnsproxyd_socket:sock_file write;
allow wo_epdg_client netd:unix_stream_socket connectto;
allow wo_epdg_client netd_socket:sock_file write;

# tear_xfrm_policy
allow wo_epdg_client self:netlink_xfrm_socket { write getattr setopt nlmsg_write read bind create };

# Purpose : check tun device is ready
allow wo_epdg_client self:udp_socket { create ioctl };

# Purpose : create symbolic link for /dev/tun
allow wo_epdg_client vendor_shell_exec:file rx_file_perms;

# Purpose: Kill Process
allow wo_epdg_client system_server:process { signal signull };
allow wo_epdg_client kernel:process signal;

# Purpose: access iptables for mss
allow wo_epdg_client self:rawip_socket { getopt create setopt };

allow wo_epdg_client devpts:chr_file rw_file_perms;
allow wo_epdg_client kernel:system module_request;
allowxperm wo_epdg_client self:udp_socket ioctl { SIOCSIFFLAGS SIOCSIFMTU SIOCSIFADDR };
