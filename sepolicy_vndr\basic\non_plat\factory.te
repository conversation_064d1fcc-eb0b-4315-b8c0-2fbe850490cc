# ==============================================
# Policy File of /vendor/bin/factory Executable File

# ==============================================
# Common SEPolicy Rule
# ==============================================
type factory, domain;
type factory_exec, exec_type, file_type, vendor_file_type;

init_daemon_domain(factory)

allow factory MTK_SMI_device:chr_file r_file_perms;
allow factory ashmem_device:chr_file x_file_perms;
allow factory ebc_device:chr_file rw_file_perms;
allow factory stpbt_device:chr_file rw_file_perms;

# Date: WK14.47
# Operation : Migration
# Purpose : CCCI
allow factory eemcs_device:chr_file rw_file_perms;
allow factory ccci_device:chr_file rw_file_perms;
allow factory gsm0710muxd_device:chr_file rw_file_perms;

# Purpose: file system requirement
allow factory devpts:chr_file rw_file_perms;
allow factory vfat:dir { w_dir_perms mounton };
allow factory vfat:filesystem { mount unmount };
allow factory labeledfs:filesystem unmount;
allow factory rootfs:dir mounton;

# Purpose : SDIO
allow factory ttySDIO_device:chr_file rw_file_perms;

# Purpose: USB
allow factory ttyMT_device:chr_file rw_file_perms;
allow factory ttyS_device:chr_file rw_file_perms;
allow factory ttyGS_device:chr_file rw_file_perms;

# Purpose: OTG
allow factory usb_device:chr_file rw_file_perms;
allow factory usb_device:dir r_dir_perms;
allow factory sysfs_usb_nonplat:file r_file_perms;
allow factory sysfs_usb_nonplat:dir r_dir_perms;

# Date: WK15.01
# Purpose : OTG Mount
allow factory sdcard_type:dir { mounton create_dir_perms };

# Date: WK15.07
# Purpose : use c2k flight mode;
allow factory vmodem_device:chr_file rw_file_perms;

# Date: WK15.13
# Purpose: for nand project
allow factory mtd_device:dir search;
allow factory mtd_device:chr_file rw_file_perms;
allow factory self:capability {
 chown
 fsetid
 ipc_lock
 net_admin
 net_raw
 sys_time
 sys_admin
 sys_boot
 sys_module
 sys_nice
 sys_resource
 };
allow factory pro_info_device:chr_file rw_file_perms;

# Data: WK15.28
# Purpose: for mt-ramdump reset
allow factory proc_mrdump_rst:file w_file_perms;

# Date: WK15.31
# Purpose: define factory_data_file instead of system_data_file
# because system_data_file is sensitive partition from M
wakelock_use(factory);
allow factory storage_file:dir { create_dir_perms mounton };

# Date: WK15.44
# Purpose: factory idle current status
set_prop(factory, vendor_mtk_factory_idle_state_prop)

# Date: WK15.46
# Purpose: gps factory mode
allow factory agpsd_data_file:dir search;
allow factory gps_data_file:dir { w_dir_perms unlink};
allow factory gps_data_file:file create_file_perms;
allow factory gps_data_file:lnk_file r_file_perms;
allow factory storage_file:lnk_file r_file_perms;
allow factory mnld:unix_dgram_socket sendto;

# Date: WK15.48
# Purpose: capture for factory mode
allow factory devmap_device:chr_file r_file_perms;
allow factory sdcard_type:file create_file_perms;
allow factory mnt_user_file:dir search;
allow factory mnt_user_file:lnk_file r_file_perms;

# Date: WK16.05
# Purpose: For access NVRAM
allow factory nvram_data_file:dir create_dir_perms;
allow factory nvram_data_file:file create_file_perms;
allow factory nvram_data_file:lnk_file r_file_perms;
allow factory nvdata_file:lnk_file r_file_perms;
allow factory nvram_device:chr_file rw_file_perms;
allow factory nvram_device:blk_file rw_file_perms;
allow factory nvdata_device:blk_file rw_file_perms;

# Date: WK16.12
# Purpose: For sensor test
allow factory hf_manager_device:chr_file rw_file_perms;
allow factory als_ps_device:chr_file r_file_perms;
allow factory barometer_device:chr_file r_file_perms;
allow factory gsensor_device:chr_file r_file_perms;
allow factory gyroscope_device:chr_file r_file_perms;
allow factory msensor_device:chr_file r_file_perms;
allow factory biometric_device:chr_file r_file_perms;

# Purpose: For camera Test
allow factory kd_camera_flashlight_device:chr_file rw_file_perms;
allow factory kd_camera_hw_device:chr_file rw_file_perms;
allow factory seninf_device:chr_file rw_file_perms;
allow factory CAM_CAL_DRV_device:chr_file rw_file_perms;
allow factory camera_eeprom_device:chr_file rw_file_perms;
allow factory ion_device:chr_file rw_file_perms;

# Purpose: For reboot the target
set_prop(factory, powerctl_prop)

# Purpose: For memory card test
allow factory misc_sd_device:chr_file r_file_perms;
allow factory mmcblk1_block_device:blk_file rw_file_perms;
allow factory bootdevice_block_device:blk_file rw_file_perms;
allow factory mmcblk1p1_block_device:blk_file rw_file_perms;
allow factory block_device:dir search;
allowxperm factory mmcblk1_block_device:blk_file ioctl BLKGETSIZE;
allowxperm factory bootdevice_block_device:blk_file ioctl BLKGETSIZE;

# Purpose: For EMMC test
allow factory nvdata_file:dir create_dir_perms;
allow factory nvdata_file:file create_file_perms;

# Purpose: For HRM test
allow factory hrm_device:chr_file r_file_perms;

# Purpose: For IrTx LED test
allow factory irtx_device:chr_file rw_file_perms;

# Purpose: For battery test, ext_buck test and ext_vbat_boost test
allow factory pmic_ftm_device:chr_file rw_file_perms;
allow factory MT_pmic_adc_cali_device:chr_file rw_file_perms;
allow factory MT_pmic_cali_device:chr_file r_file_perms;
allow factory charger_ftm_device:chr_file r_file_perms;

# Purpose: For HDMI test
allow factory graphics_device:dir w_dir_perms;
allow factory graphics_device:chr_file rw_file_perms;

# Purpose: For WIFI test
allow factory wmtWifi_device:chr_file rw_file_perms;

# Purpose: For rtc test
allow factory rtc_device:chr_file rw_file_perms;

# Purpose: For gps test
allow factory mnld_device:chr_file rw_file_perms;
allow factory stpgps_device:chr_file rw_file_perms;
allow factory mnld_exec:file rx_file_perms;

# Purpose: For keypad test
allow factory mtk_kpd_device:chr_file r_file_perms;

# Purpose: For Humidity test
allow factory humidity_device:chr_file r_file_perms;

# Purpose: For camera test
allow factory camera_isp_device:chr_file rw_file_perms;
allow factory camera_dip_device:chr_file rw_file_perms;
allow factory camera_pipemgr_device:chr_file r_file_perms;
allow factory camera_sysram_device:chr_file r_file_perms;
allow factory ccu_device:chr_file rw_file_perms;
allow factory vpu_device:chr_file rw_file_perms;
allow factory mdla_device:chr_file rw_file_perms;
allow factory apusys_device:chr_file rw_file_perms;
allow factory sysfs_apusys_queue:dir r_dir_perms;
allow factory sysfs_apusys_queue:file r_file_perms;
allow factory MAINAF_device:chr_file rw_file_perms;
allow factory MAIN2AF_device:chr_file rw_file_perms;
allow factory MAIN3AF_device:chr_file rw_file_perms;
allow factory MAIN4AF_device:chr_file rw_file_perms;
allow factory SUBAF_device:chr_file rw_file_perms;
allow factory SUB2AF_device:chr_file rw_file_perms;
allow factory FM50AF_device:chr_file rw_file_perms;
allow factory AD5820AF_device:chr_file rw_file_perms;
allow factory DW9714AF_device:chr_file rw_file_perms;
allow factory DW9714A_device:chr_file rw_file_perms;
allow factory LC898122AF_device:chr_file rw_file_perms;
allow factory LC898212AF_device:chr_file rw_file_perms;
allow factory BU6429AF_device:chr_file rw_file_perms;
allow factory DW9718AF_device:chr_file rw_file_perms;
allow factory BU64745GWZAF_device:chr_file rw_file_perms;
allow factory cct_data_file:dir create_dir_perms;
allow factory cct_data_file:file create_file_perms;
allow factory camera_tsf_device:chr_file rw_file_perms;
allow factory camera_rsc_device:chr_file rw_file_perms;
allow factory camera_gepf_device:chr_file rw_file_perms;
allow factory camera_fdvt_device:chr_file rw_file_perms;
allow factory camera_wpe_device:chr_file rw_file_perms;
allow factory camera_owe_device:chr_file rw_file_perms;
allow factory camera_mfb_device:chr_file rw_file_perms;
allow factory camera_pda_device:chr_file rw_file_perms;
hal_client_domain(factory, hal_power)
get_prop(factory, vendor_mtk_mediatek_prop)

# Date: 2021/12/10
# Operation : allow camera test to read dla network file
allow factory vendor_etc_nn_file:dir r_dir_perms;
allow factory vendor_etc_nn_file:file r_file_perms;
allowxperm factory vendor_etc_nn_file:file ioctl VT_SENDSIG;

# Date: 2020/07/20
# Operation : For M4U security
allow factory proc_m4u:file r_file_perms;
allowxperm factory proc_m4u:file ioctl {
     MTK_M4U_T_SEC_INIT
     MTK_M4U_T_CONFIG_PORT
};

# Purpose: For FM test and headset test
allow factory accdet_device:chr_file r_file_perms;
allow factory fm_device:chr_file rw_file_perms;

# Purpose: For audio test
allow factory audio_device:chr_file rw_file_perms;
allow factory audio_device:dir w_dir_perms;
set_prop(factory, vendor_mtk_audiohal_prop)
allow factory audio_ipi_device:chr_file rw_file_perms;
allow factory audio_scp_device:chr_file r_file_perms;

# Purpose: For key and touch event
allow factory input_device:chr_file r_file_perms;
allow factory input_device:dir rw_dir_perms;

# Date: WK16.17
# Purpose:  N Migration For ccci sysfs node
# Allow read to sys/kernel/ccci/* files
allow factory sysfs_ccci:dir search;
allow factory sysfs_ccci:file r_file_perms;

# Date: WK16.18
# Purpose: N Migration For boot_mode
# Allow to read boot mode
# avc: denied { read } for name="boot_mode" dev="sysfs" ino=117
# scontext=u:r:factory:s0 tcontext=u:object_r:sysfs:s0
# tclass=file permissive=0
allow factory sysfs_boot_mode:file r_file_perms;
allow factory sysfs_boot_type:file r_file_perms;

# Date: WK16.31
# Purpose: For gps test
set_prop(factory, vendor_mtk_mnld_prop)

# Date: WK16.33
# Purpose: for unmount sdcardfs and stop services which are using data partition
allow factory sdcard_type:filesystem unmount;
set_prop(factory, ctl_default_prop)

# Date : WK16.35
# Operation : Migration
# Purpose : Update camera flashlight driver device file
allow factory flashlight_device:chr_file rw_file_perms;

# Date: WK15.25
# Purpose: for unmount sdcardfs and stop services which are using data partition
set_prop(factory, system_mtk_ctl_emdlogger1_prop)

# Date: WK17.07
# Purpose: Clear bootdevice (eMMC/UFS) may need to unmount tmpfs
allow factory tmpfs:filesystem unmount;
allow factory sysfs:dir r_dir_perms;
allow factory sysfs_leds:lnk_file r_file_perms;
allow factory sysfs_leds:file rw_file_perms;
allow factory sysfs_leds:dir r_dir_perms;
allow factory sysfs_power:file rw_file_perms;
allow factory sysfs_power:dir r_dir_perms;
allow factory self:capability2 block_suspend;
allow factory sysfs_vibrator:file rw_file_perms;
allow factory debugfs_ion:dir search;
allow factory selinuxfs:file r_file_perms;
allow factory sysfs_devices_block:dir r_dir_perms;
allow factory vendor_mtk_factory_start_prop:file read;
allow factory vendor_mtk_factory_start_prop:file open;
allow factory vendor_mtk_factory_start_prop:file getattr;
allow factory vendor_mtk_factory_start_prop:file map;

# Date: WK17.27
# Purpose: STMicro NFC solution integration
allow factory st21nfc_device:chr_file rw_file_perms;
hal_client_domain(factory, hal_nfc)

# Date : WK17.32
# Operation : O Migration
# Purpose: Allow to access cmdq driver
allow factory mtk_cmdq_device:chr_file r_file_perms;
allow factory mtk_mdp_device:chr_file r_file_perms;
allow factory mtk_mdp_sync_device:chr_file r_file_perms;
allow factory sw_sync_device:chr_file r_file_perms;

# Date: WK1733
# Purpose: add selinux policy to stop 'ccci_fsd' for clear emmc in factory mode
set_prop(factory, vendor_mtk_ctl_ccci_fsd_prop)

# Date : WK17.38
# Operation : O Migration
# Purpose: Allow to access sysfs
allow factory sysfs_therm:dir search;
allow factory sysfs_therm:file rw_file_perms;

# Date: W18.22
# Purpose:  P Migration for factory get com port type and uart port info
# detail avc log: [   11.751803] <1>.(1)[227:logd.auditd]type=1400 audit(1262304016.560:10):
# avc: denied { read } for pid=203 comm="factory" name="meta_com_type_info" dev=
# "sysfs" ino=11073 scontext=u:r:factory:s0 tcontext=u:object_r:sysfs:s0 tclass=file permissive=0
allow factory sysfs_comport_type:file rw_file_perms;
allow factory sysfs_uart_info:file rw_file_perms;

# from private
allow factory kernel:system module_request;
allow factory node:tcp_socket node_bind;
allow factory userdata_block_device:blk_file rw_file_perms;
allow factory port:tcp_socket { name_bind name_connect };
allow factory self:netlink_route_socket { create_socket_perms_no_ioctl nlmsg_read nlmsg_write };
allow factory proc_net:file r_file_perms;
allowxperm factory self:udp_socket ioctl { priv_sock_ioctls SIOCGIFFLAGS SIOCGIWNWID};

allow factory self:process execmem;
allow factory self:tcp_socket create_stream_socket_perms;
allow factory self:udp_socket create_socket_perms;

allow factory sysfs_wake_lock:file rw_file_perms;

# For Light HIDL permission
hal_client_domain(factory, hal_light)
allow factory mtk_hal_light:binder call;
allow factory merged_hal_service:binder call;

# For vibrator test permission
allow factory sysfs_vibrator:dir search;

# For Audio device permission
allow factory proc_asound:dir r_dir_perms;
allow factory proc_asound:file rw_file_perms;

# For Accdet data permission
allow factory sysfs_headset:file r_file_perms;

# For touch auto test
allow factory sysfs_tpd_setting:dir search;
allow factory sysfs_tpd_setting:file r_file_perms;

# For fingerprinto test
allow factory sysfs_gf_spi_tee:dir search;
allow factory sysfs_gf_spi_tee:file r_file_perms;

# Date : WK18.23
# Operation: P migration
# Purpose : Allow factory to unmount partition, stop service, and then erase partition
allow factory vendor_shell_exec:file rx_file_perms;
allow factory vendor_toolbox_exec:file x_file_perms;
allow factory proc_cmdline:file r_file_perms;
allow factory sysfs_dt_firmware_android:file r_file_perms;
allow factory sysfs_dt_firmware_android:dir r_dir_perms;

# For power_supply and switch permission
r_dir_file(factory, sysfs_batteryinfo)
r_dir_file(factory, sysfs_switch)

# Date : WK18.31
# Operation: P migration
# Purpose : Refine policy
allow factory sysfs_devices_block:dir search;
allow factory sysfs_devices_block:file r_file_perms;

# Date : WK18.37
# Operation: P migration
# Purpose : ADSP SmartPA calibration
allow factory vendor_file:file x_file_perms;
allow factory mtk_audiohal_data_file:dir create_dir_perms;
allow factory mtk_audiohal_data_file:file create_file_perms;

# Date : WK18.37
# Operation: P migration
# Purpose : Allow factory to open /proc/version
allow factory proc_version:file r_file_perms;

# Purpose : adsp
allow factory adsp_device:chr_file rw_file_perms;

# Purpose : NFC
allow factory vendor_nfc_socket_file:dir w_dir_perms;

# Allow to get AOSP property persist.radio.multisim.config
get_prop(factory, radio_control_prop)

# Date : WK19.38
# Operation : Q Migration
# Purpose: Allow clear eMMC
set_prop(factory, system_mtk_ctl_mdlogger_prop)

# Date : WK19.41
# Operation : Q Migration
# Purpose: allow system_server to access rt5509 param and calib node
allow factory sysfs_rt_param:file rw_file_perms;
allow factory sysfs_rt_calib:file rw_file_perms;
allow factory sysfs_rt_param:dir r_dir_perms;
allow factory sysfs_rt_calib:dir r_dir_perms;

# Date : WK20.13
# Operation: R migration
# Contains lib to visit file permission
allow factory ashmem_libcutils_device:chr_file x_file_perms;

# Date : WK20.13
# Operation: R migration
# Purpose : Add permission for new device node.
allow factory sysfs_boot_info:file r_file_perms;
allow factory proc_bootprof:file getattr;
allow factory sysfs_meta_info:file r_file_perms;

# Date : WK20.17
# Operation: R migration
# Purpose : Add permission for acess vendor_de.
allow factory factory_vendor_file:file create_file_perms;
allow factory factory_vendor_file:dir w_dir_perms;

# Date : WK20.20
# Operation: R migration
# Purpose : Add permission for health HAL and vbus
hal_client_domain(factory, hal_health)
allow factory sysfs_vbus:file r_file_perms;
allow factory sysfs_chg2_present:file r_file_perms;

# Date : WK20.31
# Operation: R migration
# Purpose : Add permission for /proc/bus/input/devices
allow factory proc_bus_input:file r_file_perms;

# Date : WK20.33
# Operation: R migration
# Purpose : Add permission for access aux_adc
allow factory sys_mt6577_auxadc:dir r_dir_perms;
allow factory sys_mt6577_auxadc:file r_file_perms;

# Date : WK21.14
# Operation: Layer decoupling 2.0
# Purpose: ro.vendor.factory.GB2312
set_prop(factory, vendor_mtk_factory_prop)

# Date : WK21.18
# Operation: GKI
# Purpose : Factory access drm permission
allow factory gpu_device:dir search;
allow factory dri_device:chr_file rw_file_perms;

# Date : WK21.19
# Operation: GKI
# Purpose : Add permission for access camera_mem
allow factory camera_mem_device:chr_file rw_file_perms;

# Allow ReadDefaultFstab().
read_fstab(factory)
allow factory proc_bootconfig:file r_file_perms;

# Date : WK21.22
# Operation: GKI 2.0
# Purpose : Add permission for access dmabuf
allow factory dmabuf_system_heap_device:chr_file rw_file_perms;

# Date: 2021/05/26
# Purpose : add permission for access extdev_io (rtk device)
allow factory sysfs_extdev:dir r_dir_perms;
allow factory sysfs_extdev:file rw_file_perms;

# Date: 2021/06/23
# Purpose : add permission for access camera dev
hal_client_domain(factory, hal_camera)

# Date : 2021/06/30
# Operation: New IMGSYS Driver
# Purpose : Add permission for imgsys daemon driver
allow factory mtk_hcp_device:chr_file rw_file_perms;

# Date: 2021/07/02
# Purpose : add permission for charger configuration
allow factory sysfs_chg_cfg:file r_file_perms;

# Date: 2021/07/30
# Operation :  To access V4L2 devices (media, video and sub devices)
allow factory mtk_v4l2_media_device:dir r_dir_perms;
allow factory mtk_v4l2_media_device:chr_file rw_file_perms;
allow factory video_device:dir r_dir_perms;
allow factory video_device:chr_file rw_file_perms;

# Date: 2021/07/30
# Purpose : Add permission for access /dev directory
allow factory device:dir r_dir_perms;

# Date: 2021/07/30
# Operation : To access camera control daemon driver
allow factory mtk_ccd_device:chr_file rw_file_perms;

# Date: 2022/01/24
allow factory mnt_vendor_file:dir search;
allow factory mnt_vendor_file:file r_file_perms;
