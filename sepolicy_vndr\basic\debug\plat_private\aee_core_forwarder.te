# ==============================================
# Policy File of /system/bin/aee_core_forwarder Executable File

# ==============================================
# Type Declaration
# ==============================================
type aee_core_forwarder_exec, system_file_type, exec_type, file_type;
typeattribute aee_core_forwarder coredomain;

# ==============================================
# Common SEPolicy Rule
# ==============================================
init_daemon_domain(aee_core_forwarder)

#mkdir /sdcard/mtklog/aee_exp and write /sdcard/mtklog/aee_exp/zcorexxx.zip
allow aee_core_forwarder sdcard_type:dir create_dir_perms;
allow aee_core_forwarder sdcard_type:file create_file_perms;
allow aee_core_forwarder self:capability { fsetid setgid sys_nice sys_admin };

#read STDIN_FILENO
allow aee_core_forwarder kernel:fifo_file r_file_perms;

#read /proc/<pid>/cmdline
allow aee_core_forwarder domain:dir r_dir_perms;
allow aee_core_forwarder domain:file r_file_perms;

#get wake_lock to avoid system suspend when coredump is generating
allow aee_core_forwarder sysfs_wake_lock:file rw_file_perms;

# Date : 2015/07/11
# Operation : Migration
# Purpose : for mtk debug mechanism
allow aee_core_forwarder self:capability2 block_suspend;

# Date : 2015/07/21
# Operation : Migration
# Purpose : for generating core dump on sdcard
allow aee_core_forwarder mnt_user_file:dir search;
allow aee_core_forwarder mnt_user_file:lnk_file r_file_perms;
allow aee_core_forwarder storage_file:dir search;
allow aee_core_forwarder storage_file:lnk_file r_file_perms;

# Date : 2016/03/05
# Operation : selinux waring fix
# Purpose : avc:  denied  { search } for  pid=15909 comm="aee_core_forwar"
#                 name="15493" dev="proc" ino=112310 scontext=u:r:aee_core_forwarder:s0
#                 tcontext=u:r:untrusted_app:s0:c512,c768 tclass=dir permissive=0
dontaudit aee_core_forwarder untrusted_app:dir search;

# Date : 2016/04/18
# Operation : N0 Migration
# Purpose : access for pipefs
allow aee_core_forwarder kernel:fd use;

# Purpose: search root dir "/"
allow aee_core_forwarder tmpfs:dir search;

# Purpose : read /selinux_version
allow aee_core_forwarder rootfs:file r_file_perms;

# Data : 2016/06/13
# Operation : fix sys_ptrace selinux warning
# Purpose : type=1400 audit(1420070409.080:177): avc: denied { sys_ptrace } for pid=3136
#           comm="aee_core_forwar" capability=19 scontext=u:r:aee_core_forwarder:s0
#           tcontext=u:r:aee_core_forwarder:s0  tclass=capability permissive=0
dontaudit aee_core_forwarder self:capability sys_ptrace;

# Data : 2016/06/24
# Operation : fix media_rw_data_file access selinux warning
# Purpose :
# type=1400 audit(0.0:6511): avc: denied { search } for name="db.p08JgF"
# dev="dm-0" ino=540948 scontext=u:r:aee_core_forwarder:s0
# tcontext=u:object_r:media_rw_data_file:s0 tclass=dir permissive=1
# type=1400 audit(0.0:6512): avc: denied { write } for name="db.p08JgF"
# dev="dm-0" ino=540948 scontext=u:r:aee_core_forwarder:s0
# tcontext=u:object_r:media_rw_data_file:s0 tclass=dir permissive=1
# type=1400 audit(0.0:6513): avc: denied { add_name } for name="CURRENT.dbg"
# scontext=u:r:aee_core_forwarder:s0 tcontext=u:object_r:media_rw_data_file:s0
# tclass=dir permissive=1
# type=1400 audit(0.0:6514): avc: denied { create } for name="CURRENT.dbg"
# scontext=u:r:aee_core_forwarder:s0 tcontext=u:object_r:media_rw_data_file:s0
# tclass=file permissive=1
# type=1400 audit(0.0:6515): avc: denied { write open } for
# path="/data/media/0/mtklog/aee_exp/temp/db.p08JgF/CURRENT.dbg" dev="dm-0"
# ino=540952 scontext=u:r:aee_core_forwarder:s0 tcontext=u:object_r:media_rw_data_file:s0
# tclass=file permissive=1
allow aee_core_forwarder media_rw_data_file:dir w_dir_perms;
allow aee_core_forwarder media_rw_data_file:file create_file_perms;

# Purpose : allow aee_core_forwarder to connect aee_aed socket
allow aee_core_forwarder crash_dump:unix_stream_socket connectto;
