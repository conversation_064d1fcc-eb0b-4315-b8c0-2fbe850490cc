# ==============================================
# Policy File of /vendor/bin/aee_aedv Executable File

# ==============================================
# Common SEPolicy Rule
# ==============================================

type aee_aedv, domain;

type aee_aedv_exec, exec_type, file_type, vendor_file_type;
typeattribute aee_aedv mlstrustedsubject;

init_daemon_domain(aee_aedv)

# Date : WK14.32
# Operation : AEE UT
# Purpose : for AEE module
allow aee_aedv aed_device:chr_file rw_file_perms;
allow aee_aedv expdb_device:chr_file rw_file_perms;
allow aee_aedv expdb_block_device:blk_file rw_file_perms;
allow aee_aedv bootdevice_block_device:blk_file rw_file_perms;
allow aee_aedv etb_device:chr_file rw_file_perms;

# AED start: /dev/block/expdb
allow aee_aedv block_device:dir search;

# NE flow: /dev/RT_Monitor
allow aee_aedv RT_Monitor_device:chr_file r_file_perms;

#data/aee_exp
allow aee_aedv aee_exp_vendor_file:dir create_dir_perms;
allow aee_aedv aee_exp_vendor_file:file create_file_perms;

#data/dumpsys
allow aee_aedv aee_dumpsys_vendor_file:dir create_dir_perms;
allow aee_aedv aee_dumpsys_vendor_file:file create_file_perms;

#/data/core
allow aee_aedv aee_core_vendor_file:dir create_dir_perms;
allow aee_aedv aee_core_vendor_file:file create_file_perms;

# /data/data_tmpfs_log
allow aee_aedv vendor_tmpfs_log_file:dir create_dir_perms;
allow aee_aedv vendor_tmpfs_log_file:file create_file_perms;

allow aee_aedv domain:process { sigkill getattr getsched};

#core-pattern
allow aee_aedv usermodehelper:file r_file_perms;

# Date: W15.34
# Operation: Migration
# Purpose: For pagemap & pageflags information in NE DB
# /proc/pid/
# pre-allocation
allow aee_aedv self:capability {
 chown
 fowner
 fsetid
 kill
 linux_immutable
 net_admin
 sys_admin
 sys_nice
 sys_resource
 sys_module
};

# Purpose: aee_aedv set property
set_prop(aee_aedv, vendor_mtk_persist_mtk_aeev_prop)
set_prop(aee_aedv, vendor_mtk_persist_aeev_prop)
set_prop(aee_aedv, vendor_mtk_debug_mtk_aeev_prop)
set_prop(aee_aedv, vendor_mtk_aeev_dynamic_switch_prop)

# Purpose: mnt/user/*
allow aee_aedv mnt_user_file:dir search;
allow aee_aedv mnt_user_file:lnk_file r_file_perms;

allow aee_aedv storage_file:dir search;
allow aee_aedv storage_file:lnk_file r_file_perms;

userdebug_or_eng(`
  allow aee_aedv su:dir r_dir_perms;
  allow aee_aedv su:file r_file_perms;
')

# PROCESS_FILE_STATE
allow aee_aedv dumpstate:unix_stream_socket { read write ioctl };
allow aee_aedv dumpstate:dir search;
allow aee_aedv dumpstate:file r_file_perms;

allow aee_aedv logdr_socket:sock_file write;
allow aee_aedv logd:unix_stream_socket connectto;

# vibrator
allow aee_aedv sysfs_vibrator:file w_file_perms;

# /proc/lk_env
allow aee_aedv proc_lk_env:file rw_file_perms;

# Data : 2017/03/22
# Operation : add NE flow rule for Android O
# Purpose : make aee_aedv can get specific process NE info
allow aee_aedv domain:dir r_dir_perms;
allow aee_aedv domain:{ file lnk_file } r_file_perms;

# Data : 2017/04/06
# Operation : add selinux rule for crash_dump notify aee_aedv
# Purpose : make aee_aedv can get notify from crash_dump
allow aee_aedv crash_dump:dir search;
allow aee_aedv crash_dump:file r_file_perms;

# Date : 20170512
# Operation : fix aee_archive can't execute issue
# Purpose : type=1400 audit(0.0:97916): avc: denied { execute_no_trans } for
#           path="/system/vendor/bin/aee_archive" dev="mmcblk0p26" ino=2355
#           scontext=u:r:aee_aedv:s0 tcontext=u:object_r:vendor_file:s0
#           tclass=file permissive=0
allow aee_aedv vendor_file:file x_file_perms;

# Purpose: debugfs files
allow aee_aedv procfs_blockio:file r_file_perms;
no_debugfs_restriction(`
  userdebug_or_eng(`
    allow aee_aedv debugfs_cam_dbg:file r_file_perms;
    allow aee_aedv debugfs_cam_exception:file r_file_perms;
   ')
')

# Purpose:
# 01-01 17:59:14.440  7664  7664 I aee_dumpstate: type=1400 audit(0.0:63497):
# avc: denied { open } for path="/sys/kernel/debug/tracing/tracing_on" dev=
# "debugfs" ino=2087 scontext=u:r:dumpstate:s0 tcontext=u:object_r:
# tracing_shell_writable:s0 tclass=file permissive=1
allow aee_aedv debugfs_tracing:file rw_file_perms;

# Purpose:
# 01-01 00:05:17.720  3567  3567 W ps      : type=1400 audit(0.0:5192): avc:
# denied { getattr } for path="/proc/3421" dev="proc" ino=78975 scontext=u:r:
# aee_aedv:s0 tcontext=u:r:platform_app:s0:c512,c768 tclass=dir permissive=0
allow aee_aedv platform_app:dir r_dir_perms;
allow aee_aedv platform_app:file r_file_perms;

# Purpose:
# 01-01 00:05:17.750  3567  3567 W ps      : type=1400 audit(0.0:5193): avc:
# denied { getattr } for path="/proc/3461" dev="proc" ino=11013 scontext=u:r:
# aee_aedv:s0 tcontext=u:r:untrusted_app_25:s0:c512,c768 tclass=dir permissive=0
allow aee_aedv untrusted_app_25:dir getattr;

# Purpose:
# 01-01 00:05:17.650  3567  3567 W ps      : type=1400 audit(0.0:5179): avc:
# denied { getattr } for path="/proc/2712" dev="proc" ino=65757 scontext=u:r:
# aee_aedv:s0 tcontext=u:r:untrusted_app:s0:c512,c768 tclass=dir permissive=0
allow aee_aedv untrusted_app:dir getattr;

# Purpose:
# 01-01 00:05:17.650  3567  3567 W ps      : type=1400 audit(0.0:5180): avc:
# denied { getattr } for path="/proc/2747" dev="proc" ino=66659 scontext=u:r:
# aee_aedv:s0 tcontext=u:r:priv_app:s0:c512,c768 tclass=dir permissive=0
allow aee_aedv priv_app:dir getattr;

# Purpose:
# 01-01 00:05:16.270  3554  3554 W aee_dumpstatev: type=1400 audit(0.0:5153):
# avc: denied { open } for path="/proc/interrupts" dev="proc" ino=4026533608
# scontext=u:r:aee_aedv:s0 tcontext=u:object_r:proc_interrupts:s0 tclass=file
# permissive=0
allow aee_aedv proc_interrupts:file r_file_perms;

# Purpose:
# 01-01 00:05:17.840  3554  3554 W aee_dumpstatev: type=1400 audit(0.0:5200):
# avc: denied { search } for name="leds" dev="sysfs" ino=6217 scontext=u:r:
# aee_aedv:s0 tcontext=u:object_r:sysfs_leds:s0 tclass=dir permissive=0
allow aee_aedv sysfs_leds:dir search;
allow aee_aedv sysfs_leds:file r_file_perms;

# Purpose:
# 01-01 00:03:45.790  3651  3651 I aee_dumpstatev: type=1400 audit(0.0:5592): avc: denied
# { search } for name="ccci" dev="sysfs" ino=6026 scontext=u:r:aee_aedv:s0 tcontext=u:object_r:
# sysfs_ccci:s0 tclass=dir permissive=1
# 01-01 00:03:45.790  3651  3651 I aee_dumpstatev: type=1400 audit(0.0:5593): avc: denied { read }
# for name="md_chn" dev="sysfs" ino=6035 scontext=u:r:aee_aedv:s0 tcontext=u:object_r:sysfs_ccci:s0
# tclass=file permissive=1
# 01-01 00:03:45.790  3651  3651 I aee_dumpstatev: type=1400 audit(0.0:5594): avc: denied { open }
# for path="/sys/kernel/ccci/md_chn" dev="sysfs" ino=6035 scontext=u:r:aee_aedv:s0 tcontext=u:
# object_r:sysfs_ccci:s0 tclass=file permissive=1
allow aee_aedv sysfs_ccci:dir search;
allow aee_aedv sysfs_ccci:file r_file_perms;

# Purpose:
# 01-01 00:03:44.330  3658  3658 I aee_dumpstatev: type=1400 audit(0.0:5411): avc: denied
# { execute_no_trans } for path="/vendor/bin/toybox_vendor" dev="mmcblk0p26" ino=250 scontext=u:r:
# aee_aedv:s0 tcontext=u:object_r:vendor_toolbox_exec:s0 tclass=file permissive=1
allow aee_aedv vendor_toolbox_exec:file rx_file_perms;

# Purpose:
# 01-01 00:12:06.320000  4145  4145 W dmesg   : type=1400 audit(0.0:826): avc: denied { open } for
# path="/dev/kmsg" dev="tmpfs" ino=10875 scontext=u:r:aee_aedv:s0 tcontext=u:object_r:kmsg_device:
# s0 tclass=chr_file permissive=0
# 01-01 00:42:33.070000  4171  4171 W dmesg   : type=1400 audit(0.0:1343): avc: denied
# { syslog_read } for scontext=u:r:aee_aedv:s0 tcontext=u:r:kernel:s0 tclass=system permissive=0
allow aee_aedv kmsg_device:chr_file r_file_perms;
allow aee_aedv kernel:system syslog_read;

# Purpose:
# 01-01 00:12:37.890000  4162  4162 W aee_dumpstatev: type=1400 audit(0.0:914): avc: denied
# { read } for name="meminfo" dev="proc" ino=4026533612 scontext=u:r:aee_aedv:s0 tcontext=u:
# object_r:proc_meminfo:s0 tclass=file permissive=0
allow aee_aedv proc_meminfo:file r_file_perms;

# Purpose:
# 01-01 00:08:39.900000  3833  3833 W aee_dumpstatev: type=1400 audit(0.0:371): avc: denied
# { open } for path="/proc/3833/net/route" dev="proc" ino=4026533632 scontext=u:r:aee_aedv:s0
# tcontext=u:object_r:proc_net:s0 tclass=file permissive=0
allow aee_aedv proc_net:file r_file_perms;

# Purpose:
# 01-01 00:08:39.880000  3833  3833 W aee_dumpstatev: type=1400 audit(0.0:370): avc: denied
# { open } for path="/proc/zoneinfo" dev="proc" ino=4026533663 scontext=u:r:aee_aedv:s0 tcontext=
# u:object_r:proc_zoneinfo:s0 tclass=file permissive=0
allow aee_aedv proc_zoneinfo:file r_file_perms;

# Purpose:
# 01-01 00:33:27.750000   338   338 W aee_aedv: type=1400 audit(0.0:98): avc: denied { read }
# for name="fstab.mt6755" dev="rootfs" ino=1082 scontext=u:r:aee_aedv:s0 tcontext=u:object_r:
# rootfs:s0 tclass=file permissive=0
allow aee_aedv rootfs:file r_file_perms;

# Purpose:
# [ 241.001976] <1>.(1)[209:logd.auditd]type=1400 audit(**********.172:515): avc: denied { read }
# for pid=1978 comm="aee_aedv64" name="atag,devinfo" dev="sysfs" ino=2349 scontext=u:r:aee_aedv:s0
# tcontext=u:object_r:sysfs:s0 tclass=file permissive=0
allow aee_aedv sysfs_mrdump:file rw_file_perms;
allow aee_aedv sysfs_memory:file r_file_perms;

# Purpose: Allow aee_aedv access to vendor/bin/mtkcam-debug, which in turn invokes ICameraProvider
# - avc: denied { find } for interface=android.hardware.camera.provider::ICameraProvider pid=2956
#   scontext=u:r:aee_aedv:s0 tcontext=u:object_r:hal_camera_hwservice:s0 tclass=hwservice_manager
# - Transaction error in ICameraProvider::debug: Status(EX_TRANSACTION_FAILED)
hal_client_domain(aee_aedv, hal_camera)
allow aee_aedv hal_camera_hwservice:hwservice_manager { find };
binder_call(aee_aedv, mtk_hal_camera)

# Purpose: allow aee to read /sys/fs/selinux/enforce to get selinux status
allow aee_aedv selinuxfs:file r_file_perms;

# Purpose: mrdump db flow and pre-allocation
# mrdump db flow
allow aee_aedv sysfs_dt_firmware_android:dir search;
allow aee_aedv sysfs_dt_firmware_android:file r_file_perms;
allow aee_aedv kernel:system module_request;
allow aee_aedv metadata_file:dir search;

allow aee_aedv userdata_block_device:blk_file rw_file_perms;
allow aee_aedv para_block_device:blk_file rw_file_perms;
allow aee_aedv mrdump_device:blk_file rw_file_perms;
allowxperm aee_aedv aee_dumpsys_vendor_file:file ioctl {
  FS_IOC_GETFLAGS
  FS_IOC_SETFLAGS
  F2FS_IOC_GET_PIN_FILE
  F2FS_IOC_SET_PIN_FILE
  FS_IOC_FIEMAP
};

# Purpose: allow vendor aee read lowmemorykiller logs
# file path: /sys/module/lowmemorykiller/parameters/
allow aee_aedv sysfs_lowmemorykiller:dir search;
allow aee_aedv sysfs_lowmemorykiller:file r_file_perms;

# Purpose: Allow aee read /sys/class/misc/scp/scp_dump
allow aee_aedv sysfs_scp:dir r_dir_perms;
allow aee_aedv sysfs_scp:file r_file_perms;

# Purpose: Allow aee read /sys/class/misc/adsp/adsp_dump
allow aee_aedv sysfs_adsp:dir r_dir_perms;
allow aee_aedv sysfs_adsp:file r_file_perms;

# Purpose: allow aee_aedv to read /proc/buddyinfo
allow aee_aedv proc_buddyinfo:file r_file_perms;

# Purpose: allow aee_aedv to read /proc/cmdline
allow aee_aedv proc_cmdline:file r_file_perms;

# Purpose: allow aee_aedv to read /proc/bootconfig
allow aee_aedv proc_bootconfig:file r_file_perms;

# Purpose: allow aee_aedv to read /proc/slabinfo
allow aee_aedv proc_slabinfo:file r_file_perms;

# Purpose: allow aee_aedv to read /proc/stat
allow aee_aedv proc_stat:file r_file_perms;

# Purpose: allow aee_aedv to read /proc/version
allow aee_aedv proc_version:file r_file_perms;

# Purpose: allow aee_aedv to read /proc/vmallocinfo
allow aee_aedv proc_vmallocinfo:file r_file_perms;

# Purpose: allow aee_aedv to read /proc/vmstat
allow aee_aedv proc_vmstat:file r_file_perms;

# Purpose: Allow aee_aedv to read /proc/cpu/alignment
allow aee_aedv proc_cpu_alignment:file w_file_perms;

# Purpose: Allow aee_aedv to read /proc/gpulog
allow aee_aedv proc_gpulog:file r_file_perms;

# Purpose: Allow aee_aedv to read /proc/chip/hw_ver
allow aee_aedv proc_chip:file r_file_perms;
allow aee_aedv proc_chip:dir r_dir_perms;

# Purpose: Allow aee_aedv to read /proc/sched_debug
allow aee_aedv proc_sched_debug:file r_file_perms;

# Purpose: Allow aee_aedv to read /proc/atf_log
allow aee_aedv proc_atf_log:dir r_dir_perms;
allow aee_aedv proc_atf_log:file r_file_perms;

# Purpose: Allow aee_aedv to read /proc/last_kmsg
allow aee_aedv proc_last_kmsg:file r_file_perms;

# Purpose: Allow aee_aedv to access /sys/devices/virtual/timed_output/vibrator/enable
allow aee_aedv sysfs_vibrator_setting:dir search;
allow aee_aedv sysfs_vibrator_setting:file w_file_perms;
allow aee_aedv sysfs_vibrator:dir search;

# Purpose: Allow aee_aedv to read /proc/ufs_debug
allow aee_aedv proc_ufs_debug:file rw_file_perms;

# Purpose: Allow aee_aedv to read /proc/msdc_debug
allow aee_aedv proc_msdc_debug:file r_file_perms;

# Purpose: Allow aee_aedv to read /proc/pidmap
allow aee_aedv proc_pidmap:file r_file_perms;

# Purpose: Allow aee_aedv to read /sys/power/vcorefs/vcore_debug
allow aee_aedv sysfs_vcore_debug:file r_file_perms;

# Purpose: Allow aee_aedv to read /sys/devices/virtual/BOOT/BOOT/boot/boot_mode
allow aee_aedv sysfs_boot_mode:file r_file_perms;

#Purpose: Allow aee_aedv to read/write /sys/kernel/debug/tracing/buffer_total_size_kb
userdebug_or_eng(`
allow aee_aedv debugfs_tracing_debug:file { rw_file_perms };
')

#Purpose: Allow aee_aedv to read /sys/mtk_memcfg/slabtrace
allow aee_aedv proc_slabtrace:file r_file_perms;

#Purpose: Allow aee_aedv to read /proc/mtk_cmdq_debug/status
allow aee_aedv proc_cmdq_debug:file r_file_perms;

#data/dipdebug
allow aee_aedv aee_dipdebug_vendor_file:dir r_dir_perms;
allow aee_aedv aee_dipdebug_vendor_file:file r_file_perms;
allow aee_aedv proc_isp_p2:dir r_dir_perms;
allow aee_aedv proc_isp_p2:file r_file_perms;

allow aee_aedv connsyslog_data_vendor_file:file r_file_perms;
allow aee_aedv connsyslog_data_vendor_file:dir r_dir_perms;

# Purpose: Allow aee_aedv to read the /proc/*/exe of vendor process
allow aee_aedv vendor_file_type:file r_file_perms;

# Purpose: Allow aee_aedv to read /proc/isp_p2/isp_p2_kedump
allow aee_aedv proc_isp_p2_kedump:file r_file_perms;

# Purpose: Allow aee_aedv to read /proc/cpuhvfs/dbg_repo
allow aee_aedv proc_dbg_repo:file r_file_perms;

# Purpose: Allow aee_aedv to read /proc/pl_lk
allow aee_aedv proc_pl_lk:file r_file_perms;

allow aee_aedv proc_aed_reboot_reason:file r_file_perms;

# Purpose: Allow aee_aedv to write /proc/sys/vm/drop_caches
allow aee_aedv proc_drop_caches:file rw_file_perms;

allow aee_aedv proc_wmt_aee:file r_file_perms;

allow aee_aedv proc_aed:file rw_file_perms;
allow aee_aedv proc_aed:dir r_dir_perms;
allow aee_aedv proc_ppm:dir r_dir_perms;

allow aee_aedv dpm_block_device:blk_file r_file_perms;
allow aee_aedv sspm_block_device:blk_file r_file_perms;
allow aee_aedv boot_para_block_device:blk_file rw_file_perms;

allow aee_aedv proc_modules:file r_file_perms;

set_prop(aee_aedv, powerctl_prop)


allow aee_aedv proc_ccci_dump:file r_file_perms;
allow aee_aedv proc_log_much:file r_file_perms;

# Purpose: Allow aee_aedv to read /sys/kernel/tracing/instances/mmstat/trace
allow aee_aedv debugfs_tracing_instances:dir r_dir_perms;
allow aee_aedv debugfs_tracing_instances:file r_file_perms;

allow aee_aedv binderfs_logs:dir r_dir_perms;
allow aee_aedv binderfs_logs:file r_file_perms;

allow aee_aedv proc_ion:dir r_dir_perms;
allow aee_aedv proc_ion:file r_file_perms;
allow aee_aedv proc_m4u_dbg:dir r_dir_perms;
allow aee_aedv proc_m4u_dbg:file r_file_perms;
allow aee_aedv proc_mtkfb:file r_file_perms;

allow aee_aedv proc_dmaheap:dir r_dir_perms;
allow aee_aedv proc_dmaheap:file r_file_perms;

allow aee_aedv proc_iommu_debug:dir r_dir_perms;
allow aee_aedv proc_iommu_debug:file r_file_perms;

allow aee_aedv sysfs_dvfsrc_dbg:dir r_dir_perms;
allow aee_aedv sysfs_dvfsrc_dbg:file r_file_perms;

allow aee_aedv sysfs_systracker:dir r_dir_perms;
allow aee_aedv sysfs_systracker:file r_file_perms;

allow aee_aedv sysfs_aee_enable:file r_file_perms;

#Purpose: Allow aee_aedv to read /data/vendor/gpu_dump
allow aee_aedv gpu_dump_vendor_file:dir r_dir_perms;
allow aee_aedv gpu_dump_vendor_file:file r_file_perms;

# Date : 2020/12/14
# Purpose: allow aee_aedv to read /sys/kernel/mm/mlog/dump
allow aee_aedv sysfs_mm:file r_file_perms;

#Purpose: Allow aee_aedv to read /sys/bus/scsi/devices/0:0:0:0/vpd_pg80
allow aee_aedv sysfs_vpd:dir r_dir_perms;
allow aee_aedv sysfs_vpd:file r_file_perms;

# Date: 2021/05/21
# Purpose: allow aee_aedv to read /sys/kernel/notes
allow aee_aedv sysfs_kernel_notes:file r_file_perms;

# Date: 2021/08/09
# Purpose: Add apusys debug info into db
allow aee_aedv proc_apusys_rv_coredump_debug:file r_file_perms;
allow aee_aedv proc_apusys_rv_xfile_debug:file r_file_perms;
allow aee_aedv proc_apusys_rv_regdump_debug:file r_file_perms;
allow aee_aedv proc_apusys_logger_seq_log_debug:file r_file_perms;

# Date: 2021/08/10
# Purpose: Add apusys mdw debug info into db
allow aee_aedv proc_aputag_mdw_debug:file r_file_perms;

no_debugfs_restriction(`
  userdebug_or_eng(`
    allow aee_aedv debugfs_blockio:file r_file_perms;
    allow aee_aedv debugfs_fb:dir search;
    allow aee_aedv debugfs_fb:file r_file_perms;
    allow aee_aedv debugfs_fuseio:dir search;
    allow aee_aedv debugfs_fuseio:file r_file_perms;
    allow aee_aedv debugfs_rcu:dir search;
    allow aee_aedv debugfs_shrinker_debug:file r_file_perms;
    allow aee_aedv debugfs_dmlog_debug:file r_file_perms;
    allow aee_aedv debugfs_page_owner_slim_debug:file r_file_perms;
    allow aee_aedv debugfs_ion_mm_heap:dir search;
    allow aee_aedv debugfs_ion_mm_heap:file r_file_perms;
    allow aee_aedv debugfs_ion_mm_heap:lnk_file r_file_perms;
    allow aee_aedv debugfs_cpuhvfs:dir search;
    allow aee_aedv debugfs_cpuhvfs:file r_file_perms;
    allow aee_aedv debugfs_emi_mbw_buf:file r_file_perms;

    # Purpose:
    # 01-01 00:33:28.340000   338   338 W aee_aedv: type=1400 audit(0.0:104): avc: denied { search }
    # for name="dynamic_debug" dev="debugfs" ino=8182 scontext=u:r:aee_aedv:s0 tcontext=u:object_r:
    # debugfs_dynamic_debug:s0 tclass=dir permissive=0
    allow aee_aedv debugfs_dynamic_debug:dir search;
    allow aee_aedv debugfs_dynamic_debug:file r_file_perms;

    # Purpose: Allow aee_aedv to read /sys/kernel/debug/rcu/rcu_callback_log
    allow aee_aedv debugfs_rcu:file r_file_perms;

    # Purpose: Allow aee_aedv to read /sys/kernel/debug/smi_mon
    allow aee_aedv debugfs_smi_mon:file r_file_perms;

    allow aee_aedv debugfs_cmdq:file r_file_perms;
    allow aee_aedv debugfs_mml:file r_file_perms;
    allow aee_aedv debugfs_wakeup_sources:file r_file_perms;
  ')
')

allow aee_aedv sysfs_cache_status:file r_file_perms;

allow aee_aedv sysfs_emiisu:file r_file_perms;

allow aee_aedv mnt_vendor_file:dir search;
allow aee_aedv nvdata_file:dir r_dir_perms;
allow aee_aedv nvdata_file:file r_file_perms;
allow aee_aedv protect_f_data_file:dir r_dir_perms;
allow aee_aedv protect_f_data_file:file r_file_perms;
allow aee_aedv protect_s_data_file:dir r_dir_perms;
allow aee_aedv protect_s_data_file:file r_file_perms;
allow aee_aedv proc_vpu_memory:file r_file_perms;

allow aee_aedv proc_lockdep:file r_file_perms;
