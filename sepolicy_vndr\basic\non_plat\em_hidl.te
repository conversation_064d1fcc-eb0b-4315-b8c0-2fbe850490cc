# ==============================================
# Policy File of /vendor/bin/em_hidl Executable File

# ==============================================
# Common SEPolicy Rule
# ==============================================
type em_hidl, domain;
type em_hidl_exec, exec_type, file_type, vendor_file_type;

# Date :  2018/06/28
init_daemon_domain(em_hidl)

# Date :  2018/06/28
# Purpose: EM_HILD
hal_server_domain(em_hidl, hal_mtk_em)

# Date :  2018/06/28
# Operation : EM DEBUG
# Purpose: EM should set ims operator
set_prop(em_hidl, vendor_mtk_operator_id_prop)

# Date :  2018/06/28
# Operation : EM DEBUG
# Purpose: EM should set vendor_mtk_simswitch_emmode_prop
set_prop(em_hidl, vendor_mtk_simswitch_emmode_prop)

# Date :  2018/06/28
# Operation : EM DEBUG
# Purpose: EM should set vendor_mtk_dsbp_support_prop
set_prop(em_hidl, vendor_mtk_dsbp_support_prop)

# Date :  2018/06/28
# Operation : EM DEBUG
# Purpose: EM should set vendor_mtk_imstestmode_prop
set_prop(em_hidl, vendor_mtk_imstestmode_prop)

# Date :  2018/06/28
# Operation : EM DEBUG
# Purpose: EM should set vendor_mtk_smsformat_prop
set_prop(em_hidl, vendor_mtk_smsformat_prop)

# Date :  2018/06/28
# Operation : EM DEBUG
# Purpose: EM should set vendor_mtk_gprs_prefer_prop
set_prop(em_hidl, vendor_mtk_gprs_prefer_prop)

# Date :  2018/06/28
# Operation : EM DEBUG
# Purpose: EM should set vendor_mtk_testsim_cardtype_prop
set_prop(em_hidl, vendor_mtk_testsim_cardtype_prop)

# Date :  2018/06/28
# Operation : EM DEBUG
# Purpose: EM should set vendor_mtk_ct_ir_engmode_prop
set_prop(em_hidl, vendor_mtk_ct_ir_engmode_prop)

# Date :  2018/06/28
# Operation : EM DEBUG
# Purpose: EM should vendor_mtk_disable_c2k_cap_prop
set_prop(em_hidl, vendor_mtk_disable_c2k_cap_prop)

# Date :  2018/06/29
# Operation : EM DEBUG
# Purpose: EM should vendor_mtk_debug_md_reset_prop
set_prop(em_hidl, vendor_mtk_debug_md_reset_prop)

# Date :  2018/06/29
# Operation : EM DEBUG
# Purpose: EM should video log vendor_mtk_omx_log_prop
set_prop(em_hidl, vendor_mtk_omx_log_prop)

# Date :  2018/06/29
# Operation : EM DEBUG
# Purpose: EM should video log vendor_mtk_vdec_log_prop
set_prop(em_hidl, vendor_mtk_vdec_log_prop)

# Date :  2018/06/29
# Operation : EM DEBUG
# Purpose: EM should video log vendor_mtk_vdectlc_log_prop
set_prop(em_hidl, vendor_mtk_vdectlc_log_prop)

# Date :  2018/06/29
# Operation : EM DEBUG
# Purpose: EM should video log vendor_mtk_venc_h264_showlog_prop
set_prop(em_hidl, vendor_mtk_venc_h264_showlog_prop)

# Date :  2018/06/29
# Operation : EM DEBUG
# Purpose: EM should video log vendor_mtk_modem_warning_prop
set_prop(em_hidl, vendor_mtk_modem_warning_prop)

# Date :  2018/07/06
# Operation : EM DEBUG
# Purpose: EM allow usb vendor_mtk_em_usb_prop
set_prop(em_hidl, vendor_mtk_em_usb_prop)

# Date :  2018/07/06
# Operation : EM DEBUG
# Purpose: for setting usb otg enable property
set_prop(em_hidl, vendor_mtk_usb_otg_switch_prop)

# Data : 2018/07/06
# Purpose : EM MCF read nvdata dir and file
allow em_hidl nvcfg_file:dir ra_dir_perms;
allow em_hidl nvcfg_file:file r_file_perms;

# Data : 2018/07/06
# Purpose : EM MCF search vendor dir
allow em_hidl mnt_vendor_file:dir search;

# Data : 2018/08/10
# Purpose : EM BT usage
allow em_hidl stpbt_device:chr_file rw_file_perms;
allow em_hidl sysfs_boot_mode:file r_file_perms;
allow em_hidl ttyGS_device:chr_file rw_file_perms;
set_prop(em_hidl, vendor_mtk_usb_prop)
allow em_hidl nvdata_file:file r_file_perms;
allow em_hidl nvdata_file:dir search;

# Date :  2018/08/28
# Operation : EM DEBUG
# Purpose: for em set hidl configure
set_prop(em_hidl, vendor_mtk_em_hidl_prop)

# Date :  2019/08/22
# Operation : EM AAL
# Purpose: for em set aal property
set_prop(em_hidl, vendor_mtk_pq_prop)

# Date :  2019/09/10
# Operation : EM wcn coredump
# Purpose: for em set wcn coredump property
set_prop(em_hidl, vendor_mtk_coredump_prop)

# Date :  2021/04/15
# Operation : mdota read
# Purpose: read mdota files
allow em_hidl mcf_ota_file:dir r_dir_perms;
