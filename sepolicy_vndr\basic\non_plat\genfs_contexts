# ==============================================
# Common SEPolicy Rule
# ==============================================

##########################
# proc files
#
genfscon proc /blocktag/blockio              u:object_r:procfs_blockio:s0
genfscon proc /blocktag/eara_io              u:object_r:proc_earaio:s0
genfscon proc /driver/thermal                u:object_r:proc_thermal:s0
genfscon proc /thermlmt                      u:object_r:proc_thermal:s0
genfscon proc /fps_tm                        u:object_r:proc_thermal:s0
genfscon proc /wmt_tm                        u:object_r:proc_thermal:s0
genfscon proc /mobile_tm                     u:object_r:proc_thermal:s0
genfscon proc /bcctlmt                       u:object_r:proc_thermal:s0
genfscon proc /battery_status                u:object_r:proc_thermal:s0
genfscon proc /mtkcooler                     u:object_r:proc_mtkcooler:s0
genfscon proc /mtktz                         u:object_r:proc_mtktz:s0
genfscon proc /lk_env                        u:object_r:proc_lk_env:s0
genfscon proc /driver/storage_logger         u:object_r:proc_slogger:s0
genfscon proc /driver/icusb                  u:object_r:proc_icusb:s0
genfscon proc /mrdump_rst                    u:object_r:proc_mrdump_rst:s0
genfscon proc /mtd                           u:object_r:proc_mtd:s0
genfscon proc /ged                           u:object_r:proc_ged:s0
genfscon proc /mtk_jpeg                      u:object_r:proc_mtk_jpeg:s0
genfscon proc /perfmgr                       u:object_r:proc_perfmgr:s0
genfscon proc /driver/wmt_dbg                u:object_r:proc_wmtdbg:s0
genfscon proc /zraminfo                      u:object_r:proc_zraminfo:s0
genfscon proc /gpulog                        u:object_r:proc_gpulog:s0
genfscon proc /gpufreqv2                     u:object_r:proc_gpufreqv2:s0
genfscon proc /sched_debug                   u:object_r:proc_sched_debug:s0
genfscon proc /chip                          u:object_r:proc_chip:s0
genfscon proc /atf_log                       u:object_r:proc_atf_log:s0
genfscon proc /gz_log                        u:object_r:proc_gz_log:s0
genfscon proc /last_kmsg                     u:object_r:proc_last_kmsg:s0
genfscon proc /bootprof                      u:object_r:proc_bootprof:s0
genfscon proc /mtprintk                      u:object_r:proc_mtprintk:s0
genfscon proc /pl_lk                         u:object_r:proc_pl_lk:s0
genfscon proc /msdc_debug                    u:object_r:proc_msdc_debug:s0
genfscon proc /ufs_debug                     u:object_r:proc_ufs_debug:s0
genfscon proc /pidmap                        u:object_r:proc_pidmap:s0
genfscon proc /mtk_memcfg/slabtrace          u:object_r:proc_slabtrace:s0
genfscon proc /mtk_cmdq_debug/status         u:object_r:proc_cmdq_debug:s0
genfscon proc /mtk_cmdq_debug/record         u:object_r:proc_cmdq_debug:s0
genfscon proc /cpuhvfs/dbg_repo              u:object_r:proc_dbg_repo:s0
genfscon proc /sys/kernel/panic_on_rcu_stall u:object_r:proc_panic_on_rcu_stall:s0

# Purpose dump not exit file
genfscon proc /isp_p2/isp_p2_dump     u:object_r:proc_isp_p2_dump:s0
genfscon proc /isp_p2/isp_p2_kedump   u:object_r:proc_isp_p2_kedump:s0
genfscon proc /mali/memory_usage      u:object_r:proc_memory_usage:s0
genfscon proc /mtk_mali/gpu_memory    u:object_r:proc_gpu_memory:s0
genfscon proc /mtk_es_reg_dump        u:object_r:proc_mtk_es_reg_dump:s0

# Date : 2018/11/01
# Purpose : mtk EM c2k bypass read usb file
genfscon proc /isp_p2 u:object_r:proc_isp_p2:s0

# Date : WK19.27
# Purpose: Android Migration for SVP
genfscon proc /m4u u:object_r:proc_m4u:s0

genfscon proc /driver/wmt_aee u:object_r:proc_wmt_aee:s0

genfscon proc /ccci_dump u:object_r:proc_ccci_dump:s0
genfscon proc /log_much  u:object_r:proc_log_much:s0
genfscon proc /ccci_sib  u:object_r:proc_ccci_sib:s0

# Purpose: get input devices
genfscon proc /bus/input/devices u:object_r:proc_bus_input:s0

# 2019/09/05
# Purpose: Allow powerhal to control kernel resources
genfscon proc /ppm      u:object_r:proc_ppm:s0
genfscon proc /cpufreq  u:object_r:proc_cpufreq:s0
genfscon proc /hps      u:object_r:proc_hps:s0
genfscon proc /cm_mgr   u:object_r:proc_cm_mgr:s0
genfscon proc /fliperfs u:object_r:proc_fliperfs:s0

# Date : 2019/10/11
# Purpose : allow system_server to access /proc/wlan/status for Q Migration
genfscon proc /wlan/status u:object_r:proc_wlan_status:s0

genfscon sysfs /devices/platform/11015000.i2c9/i2c-9/9-0041/reset_dsp u:object_r:sysfs_reset_dsp:s0
genfscon sysfs /bus/platform/drivers/smartpainfo/chip_vendor u:object_r:sysfs_chip_vendor:s0
genfscon sysfs /bus/platform/drivers/smartpainfo/pa_num u:object_r:sysfs_pa_num:s0

# 2019/11/14
# Purpose: Allow powerhal to control MCDI
genfscon proc /cpuidle u:object_r:proc_cpuidle:s0

# Date : 2019/12/10
# Purpose: Allow bt process or tool to control bt_dbg
genfscon proc /driver/bt_dbg u:object_r:proc_btdbg:s0

# Date : WK20.03
# Purpose: Allow mtk_hal_neuralnetworks to read chip id and segment code
# /proc/device-tree/chosen/atag,chipid is linked to
genfscon proc /device-tree/chosen/atag,devinfo u:object_r:proc_devinfo:s0

# 2020/06/12
# Operation: R migration
# Purpose: Allow powerhal to control displowpower
genfscon proc /displowpower u:object_r:proc_displowpower:s0

# 2020/06/29
# Operation: R migration
# Purpose: Add permission for access /proc/ion/*
genfscon proc /ion u:object_r:proc_ion:s0

# 2020/07/01
# Operation: R migration
# Purpose: Add permission for access /proc/m4u_dbg/*
genfscon proc /m4u_dbg u:object_r:proc_m4u_dbg:s0

genfscon proc /mtkfb u:object_r:proc_mtkfb:s0

# 2020/07/07
# Operation: R migration
# Purpose: Add permission for access /proc/pvr/*
genfscon proc /pvr u:object_r:procfs_gpu_img:s0

# Date : 2020/07/08
# Purpose: add permission for /proc/sys/vm/swappiness
genfscon proc /sys/vm/swappiness u:object_r:proc_swappiness:s0

# Date : 2020/08/05
# Purpose: add permission for /proc/driver/wmt_user_proc
genfscon proc /driver/wmt_user_proc u:object_r:proc_wmtuserproc:s0

# Date : 2020/09/18
# Purpose: add permission for /proc/mcdi/
genfscon proc /mcdi/ u:object_r:proc_mcdi:s0

# Date : 2020/12/23
# Purpose: Add permission for /proc/driver/conninfra_dbg
genfscon proc /driver/conninfra_dbg u:object_r:proc_conninfradbg:s0

# Data : 2021/4/21
# Purpose : read/write /proc/mtk_usb, /proc/mtk_typec
genfscon proc /mtk_usb u:object_r:proc_usb_plat:s0
genfscon proc /mtk_typec u:object_r:proc_usb_plat:s0

# Date 2021/05/10
# Purpose : init the default value before bootup
genfscon proc /sys/kernel/sched_migration_cost_ns u:object_r:proc_sched_migration_cost_ns:s0

# 2021/8/25
# allow powerhal to access /proc/cpuhvfs/cpufreq_cci_mode
genfscon proc /cpuhvfs/cpufreq_cci_mode u:object_r:proc_cpuhvfs:s0

##########################
# sysfs files
#
genfscon sysfs /bus/platform/drivers/mtk-kpd u:object_r:sysfs_keypad_file:s0
genfscon sysfs /power/vcorefs/pwr_ctrl       u:object_r:sysfs_vcorefs_pwrctrl:s0
genfscon sysfs /power/dcm_state              u:object_r:sysfs_dcm:s0
genfscon sysfs /power/mtkdcs/mode            u:object_r:sysfs_dcs:s0
genfscon sysfs /power/mtkpasr/execstate      u:object_r:sysfs_execstate:s0
genfscon sysfs /mtk_ssw                      u:object_r:sysfs_ssw:s0
genfscon sysfs /devices/soc0                 u:object_r:sysfs_soc:s0

genfscon sysfs /kernel/mm/mlog/dump u:object_r:sysfs_mm:s0

genfscon sysfs /bus/platform/drivers/dev_info/dev_info                       u:object_r:sysfs_devinfo:s0
genfscon sysfs /bus/platform/drivers/meta_com_type_info/meta_com_type_info   u:object_r:sysfs_comport_type:s0
genfscon sysfs /bus/platform/drivers/meta_uart_port_info/meta_uart_port_info u:object_r:sysfs_uart_info:s0

genfscon sysfs /devices/platform/battery                                                                            u:object_r:sysfs_batteryinfo:s0
genfscon sysfs /devices/platform/charger/Pump_Express                                                               u:object_r:sysfs_pump_express:s0
genfscon sysfs /devices/platform/charger/Charger_Config                                                             u:object_r:sysfs_chg_cfg:s0
genfscon sysfs /devices/platform/battery/Pump_Express                                                               u:object_r:sysfs_pump_express:s0
genfscon sysfs /devices/platform/charger/power_supply                                                               u:object_r:sysfs_batteryinfo:s0
genfscon sysfs /devices/platform/11d04000.i2c8/i2c-8/8-0055/power_supply/battery                                    u:object_r:sysfs_batteryinfo:s0
genfscon sysfs /devices/platform/11d04000.i2c8/i2c-8/8-0055/power_supply                                            u:object_r:sysfs_batteryinfo:s0
genfscon sysfs /devices/platform/charger/power_supply/mtk-slave-charger/present                                     u:object_r:sysfs_chg2_present:s0
genfscon sysfs /devices/platform/mt_charger/power_supply                                                            u:object_r:sysfs_batteryinfo:s0
genfscon sysfs /devices/platform/1000d000.pwrap/1000d000.pwrap:main_pmic/mt6357-gauge/power_supply                  u:object_r:sysfs_batteryinfo:s0
genfscon sysfs /devices/platform/soc/10026000.pwrap/10026000.pwrap:mt6366/mt6358-gauge/power_supply                 u:object_r:sysfs_batteryinfo:s0
genfscon sysfs /devices/platform/soc/1000d000.pwrap/1000d000.pwrap:main_pmic/mt6359-gauge/power_supply              u:object_r:sysfs_batteryinfo:s0
genfscon sysfs /devices/platform/10026000.pwrap/10026000.pwrap:mt6359p/mt6359p-gauge/power_supply                   u:object_r:sysfs_batteryinfo:s0
genfscon sysfs /devices/platform/soc/11cb1000.i2c/i2c-9/9-0034/11cb1000.i2c:mt6375@34:mt6375_gauge/power_supply/    u:object_r:sysfs_batteryinfo:s0
genfscon sysfs /devices/platform/11016000.i2c5/i2c-5/5-0034/mt6370_pmu_charger/power_supply                         u:object_r:sysfs_batteryinfo:s0
genfscon sysfs /devices/platform/soc/11016000.i2c5/i2c-5/5-0034/mt6360_pmu_chg.2.auto/power_supply                  u:object_r:sysfs_batteryinfo:s0
genfscon sysfs /devices/platform/soc/11e00000.i2c/i2c-7/7-0034/mt6360_chg.1.auto/power_supply                       u:object_r:sysfs_batteryinfo:s0
genfscon sysfs /devices/platform/soc/11e00000.i2c/i2c-5/5-0034/mt6360_chg.1.auto/power_supply                       u:object_r:sysfs_batteryinfo:s0
genfscon sysfs /devices/platform/soc/11f00000.i2c/i2c-5/5-0034/mt6360_chg.2.auto/power_supply                       u:object_r:sysfs_batteryinfo:s0
genfscon sysfs /devices/platform/soc/11cb1000.i2c/i2c-9/9-0034/11cb1000.i2c:mt6375@34:chg/power_supply/             u:object_r:sysfs_batteryinfo:s0
genfscon sysfs /devices/platform/11e01000.i2c/i2c-5/5-0034/11e01000.i2c:mt6375@34:mtk_gauge/power_supply/           u:object_r:sysfs_batteryinfo:s0
genfscon sysfs /devices/platform/11e01000.i2c/i2c-5/5-0034/11e01000.i2c:mt6375@34:chg/power_supply/                 u:object_r:sysfs_batteryinfo:s0
genfscon sysfs /devices/platform/11ed1000.i2c/i2c-5/5-0034/11ed1000.i2c:mt6375@34:mtk_gauge/power_supply/           u:object_r:sysfs_batteryinfo:s0
genfscon sysfs /devices/platform/11ed1000.i2c/i2c-5/5-0034/11ed1000.i2c:mt6375@34:chg/power_supply/                 u:object_r:sysfs_batteryinfo:s0
genfscon sysfs /devices/platform/soc/11280000.i2c/i2c-5/5-0034/11280000.i2c:mt6375@34:mtk_gauge/power_supply/       u:object_r:sysfs_batteryinfo:s0
genfscon sysfs /devices/platform/soc/11280000.i2c/i2c-5/5-0034/11280000.i2c:mt6375@34:chg/power_supply/             u:object_r:sysfs_batteryinfo:s0
genfscon sysfs /devices/platform/11b20000.i2c/i2c-5/5-0034/11b20000.i2c:mt6375@34:mtk_gauge/power_supply/           u:object_r:sysfs_batteryinfo:s0
genfscon sysfs /devices/platform/11b20000.i2c/i2c-5/5-0034/11b20000.i2c:mt6375@34:chg/power_supply/                 u:object_r:sysfs_batteryinfo:s0
genfscon sysfs /devices/platform/soc/11017000.i2c/i2c-5/5-0034/11017000.i2c:mt6375@34:chg/power_supply/             u:object_r:sysfs_batteryinfo:s0
genfscon sysfs /devices/platform/11e01000.i2c/i2c-5/5-0034/mt6360_chg.3.auto/power_supply                           u:object_r:sysfs_batteryinfo:s0
genfscon sysfs /devices/platform/1000d000.pwrap/1000d000.pwrap:main_pmic/mt6357-charger-type-detection/power_supply u:object_r:sysfs_batteryinfo:s0
genfscon sysfs /devices/platform/mt-rtc/rtc                                                                         u:object_r:sysfs_rtc:s0
genfscon sysfs /devices/platform/1000d000.pwrap/1000d000.pwrap:mt6359-pmic/mt6359-rtc/rtc                           u:object_r:sysfs_rtc:s0
genfscon sysfs /devices/platform/1000d000.pwrap/1000d000.pwrap:mt6358-pmic/mt6358-rtc/rtc                           u:object_r:sysfs_rtc:s0
genfscon sysfs /devices/platform/1000d000.pwrap/1000d000.pwrap:main_pmic/mt6397-rtc/rtc                             u:object_r:sysfs_rtc:s0
genfscon sysfs /devices/platform/soc/1000d000.pwrap/1000d000.pwrap:main_pmic/mt6397-rtc/rtc                         u:object_r:sysfs_rtc:s0
genfscon sysfs /devices/platform/10026000.pwrap/10026000.pwrap:mt6359-pmic/mt6359-rtc/rtc                           u:object_r:sysfs_rtc:s0
genfscon sysfs /devices/platform/10026000.pwrap/10026000.pwrap:mt6359p/mt6359p-rtc/rtc                              u:object_r:sysfs_rtc:s0
genfscon sysfs /class/typec                                                                                         u:object_r:sysfs_usb_nonplat:s0
genfscon sysfs /devices/platform/mt_usb/musb-hdrc/dual_role_usb                                                     u:object_r:sysfs_usb_nonplat:s0
genfscon sysfs /devices/platform/mt_usb/musb-hdrc/cmode                                                             u:object_r:sysfs_usb_nonplat:s0
genfscon sysfs /devices/platform/11270000.usb3/musb-hdrc/cmode                                                      u:object_r:sysfs_usb_nonplat:s0
genfscon sysfs /devices/platform/soc/usb0/cmode                                                                     u:object_r:sysfs_usb_nonplat:s0
genfscon sysfs /devices/platform/mt_usb/musb-hdrc/usb1                                                              u:object_r:sysfs_usb_nonplat:s0
genfscon sysfs /devices/platform/mt_usb/musb-hdrc/usb2                                                              u:object_r:sysfs_usb_nonplat:s0
genfscon sysfs /devices/platform/soc/mt_usb/musb-hdrc/usb1                                                          u:object_r:sysfs_usb_nonplat:s0
genfscon sysfs /devices/platform/soc/mt_usb/musb-hdrc/usb2                                                          u:object_r:sysfs_usb_nonplat:s0
genfscon sysfs /devices/platform/soc/usb0/11200000.xhci0/usb1                                                       u:object_r:sysfs_usb_nonplat:s0
genfscon sysfs /devices/platform/soc/usb0/11200000.xhci0/usb2                                                       u:object_r:sysfs_usb_nonplat:s0
genfscon sysfs /devices/platform/soc/usb0/11200000.xhci0/usb3                                                       u:object_r:sysfs_usb_nonplat:s0
genfscon sysfs /devices/platform/soc/11201000.usb0/11200000.xhci0/usb1                                              u:object_r:sysfs_usb_nonplat:s0
genfscon sysfs /devices/platform/soc/11201000.usb0/11200000.xhci0/usb2                                              u:object_r:sysfs_usb_nonplat:s0
genfscon sysfs /devices/platform/soc/11201000.usb0/11200000.xhci0/usb3                                              u:object_r:sysfs_usb_nonplat:s0
genfscon sysfs /devices/platform/usb_xhci/usb1                                                                      u:object_r:sysfs_usb_nonplat:s0
genfscon sysfs /devices/platform/usb3_xhci/usb1                                                                     u:object_r:sysfs_usb_nonplat:s0
genfscon sysfs /devices/platform/mt_usb/musb-hdrc/udc/musb-hdrc                                                     u:object_r:sysfs_usb_nonplat:s0
genfscon sysfs /devices/platform/11201000.mtu3_0/udc/musb-hdrc                                                      u:object_r:sysfs_usb_nonplat:s0
genfscon sysfs /devices/platform/11201000.usb3/udc/musb-hdrc                                                        u:object_r:sysfs_usb_nonplat:s0
genfscon sysfs /devices/platform/11270000.usb3/musb-hdrc/udc/musb-hdrc                                              u:object_r:sysfs_usb_nonplat:s0
genfscon sysfs /devices/platform/soc/11201000.usb0/udc/11201000.usb0                                                u:object_r:sysfs_usb_nonplat:s0
genfscon sysfs /devices/platform/usb0/udc/usb0                                                                      u:object_r:sysfs_usb_nonplat:s0
genfscon sysfs /devices/platform/usb0/11200000.xhci0/usb1                                                           u:object_r:sysfs_usb_nonplat:s0
genfscon sysfs /devices/platform/usb0/11200000.xhci0/usb2                                                           u:object_r:sysfs_usb_nonplat:s0

genfscon sysfs /devices/virtual/BOOT/BOOT/boot/boot_mode u:object_r:sysfs_boot_mode:s0
genfscon sysfs /devices/virtual/BOOT/BOOT/boot/boot_type u:object_r:sysfs_boot_type:s0

genfscon sysfs /devices/virtual/misc/md32   u:object_r:sysfs_md32:s0
genfscon sysfs /devices/virtual/misc/scp    u:object_r:sysfs_scp:s0
genfscon sysfs /devices/virtual/misc/scp_B  u:object_r:sysfs_scp:s0
genfscon sysfs /devices/virtual/misc/sspm   u:object_r:sysfs_sspm:s0
genfscon sysfs /devices/virtual/misc/adsp   u:object_r:sysfs_adsp:s0
genfscon sysfs /devices/virtual/misc/adsp_0 u:object_r:sysfs_adsp:s0
genfscon sysfs /devices/virtual/misc/adsp_1 u:object_r:sysfs_adsp:s0
genfscon sysfs /devices/virtual/misc/vcp    u:object_r:sysfs_vcp:s0

# Date : 2019/09/12
genfscon sysfs /devices/virtual/thermal u:object_r:sysfs_therm:s0
genfscon sysfs /devices/class/thermal   u:object_r:sysfs_therm:s0
genfscon sysfs /kernel/thermal u:object_r:sysfs_thermal_sram:s0
genfscon sysfs /kernel/charger_cooler u:object_r:sysfs_charger_cooler:s0

genfscon sysfs /devices/virtual/switch/fps u:object_r:sysfs_fps:s0

genfscon sysfs /firmware/devicetree/base/chosen/atag,devinfo u:object_r:sysfs_devinfo:s0

genfscon sysfs /firmware/devicetree/base/chosen/aee,enable u:object_r:sysfs_aee_enable:s0

genfscon sysfs /kernel/ccci u:object_r:sysfs_ccci:s0

# Date : 2018/06/15
# Purpose : mtk EM touchscreen settings
genfscon sysfs /module/tpd_setting        u:object_r:sysfs_tpd_setting:s0
genfscon sysfs /power/vcorefs/vcore_debug u:object_r:sysfs_vcore_debug:s0
genfscon sysfs /power/vcorefs/opp_table   u:object_r:sysfs_vcore_debug:s0

# Date: 2018/08/09
# Purpose : MTK Vibrator
genfscon sysfs /devices/platform/odm/odm:vibrator@0/leds/vibrator              u:object_r:sysfs_vibrator:s0
genfscon sysfs /devices/platform/soc/soc:regulator_vibrator/leds/vibrator      u:object_r:sysfs_vibrator:s0
genfscon sysfs /devices/platform/soc/soc:pwm_leds/leds/lcd-backlight           u:object_r:sysfs_leds:s0
genfscon sysfs /devices/platform/soc/soc:mtk_leds/leds/lcd-backlight           u:object_r:sysfs_leds:s0
genfscon sysfs /devices/platform/mtk_leds/leds/lcd-backlight                   u:object_r:sysfs_leds:s0
genfscon sysfs /devices/platform/i2c_leds/leds/lcd-backlight                   u:object_r:sysfs_leds:s0
genfscon sysfs /devices/platform/regulator_vibrator/leds/vibrator              u:object_r:sysfs_vibrator:s0
genfscon sysfs /devices/platform/leds-mt65xx/leds                              u:object_r:sysfs_leds:s0
genfscon sysfs /devices/platform/pwmleds/leds                                  u:object_r:sysfs_leds:s0
genfscon sysfs /devices/platform/disp_leds/leds                                u:object_r:sysfs_leds:s0
genfscon sysfs /devices/platform/11f00000.i2c6/i2c-6/6-0011/leds/lcd-backlight u:object_r:sysfs_leds:s0

# Date : 2018/11/22
# Purpose: allow mdlogger to read mdinfo file
genfscon sysfs /kernel/md/mdee u:object_r:sysfs_mdinfo:s0

# Date : 2019/07/03
# Purpose: SIU update sysfs_devices_block access for emmc and ufs
genfscon sysfs /devices/platform/bootdevice/mmc_host/mmc0/mmc0:0001/block/mmcblk0                       u:object_r:sysfs_devices_block:s0
genfscon sysfs /devices/mtk-msdc.0/11230000.msdc0/mmc_host/mmc0/mmc0:0001/block/mmcblk0                 u:object_r:sysfs_devices_block:s0
genfscon sysfs /devices/platform/mtk-msdc.0/11230000.msdc0/mmc_host/mmc0/mmc0:0001/block/mmcblk0        u:object_r:sysfs_devices_block:s0
genfscon sysfs /devices/platform/bootdevice/mmc_host/mmc0/mmc0:0001/wp_grp_size                         u:object_r:sysfs_devices_block:s0
genfscon sysfs /devices/platform/bootdevice/host0/target0:0:0/0:0:0:0/block/sda                         u:object_r:sysfs_devices_block:s0
genfscon sysfs /devices/platform/bootdevice/host0/target0:0:0/0:0:0:1/block/sdb                         u:object_r:sysfs_devices_block:s0
genfscon sysfs /devices/platform/bootdevice/host0/target0:0:0/0:0:0:2/block/sdc                         u:object_r:sysfs_devices_block:s0
genfscon sysfs /devices/platform/soc/11270000.ufshci/host0/target0:0:0/0:0:0:0/block/sda                u:object_r:sysfs_devices_block:s0
genfscon sysfs /devices/platform/soc/11270000.ufshci/host0/target0:0:0/0:0:0:1/block/sdb                u:object_r:sysfs_devices_block:s0
genfscon sysfs /devices/platform/soc/11270000.ufshci/host0/target0:0:0/0:0:0:2/block/sdc                u:object_r:sysfs_devices_block:s0
genfscon sysfs /devices/platform/soc/11270000.ufshci/host0/target0:0:0/0:0:0:2/block/sdc/sdc15          u:object_r:sysfs_devices_block:s0
genfscon sysfs /devices/platform/soc/11270000.ufshci/host0/target0:0:0/0:0:0:2/block/sdc/sdc33          u:object_r:sysfs_devices_block:s0
genfscon sysfs /devices/platform/soc/11270000.ufshci/host0/target0:0:0/0:0:0:2/block/sdc/sdc43          u:object_r:sysfs_devices_block:s0
genfscon sysfs /devices/platform/soc/11270000.ufshci/host0/target0:0:0/0:0:0:2/block/sdc/sdc53          u:object_r:sysfs_devices_block:s0
genfscon sysfs /devices/platform/soc/112b0000.ufshci/host0/target0:0:0/0:0:0:0/block/sda                u:object_r:sysfs_devices_block:s0
genfscon sysfs /devices/platform/soc/112b0000.ufshci/host0/target0:0:0/0:0:0:1/block/sdb                u:object_r:sysfs_devices_block:s0
genfscon sysfs /devices/platform/soc/112b0000.ufshci/host0/target0:0:0/0:0:0:2/block/sdc                u:object_r:sysfs_devices_block:s0
genfscon sysfs /devices/platform/soc/112b0000.ufshci/host0/target0:0:0/0:0:0:2/block/sdc/sdc2           u:object_r:sysfs_devices_block:s0
genfscon sysfs /devices/platform/soc/112b0000.ufshci/host0/target0:0:0/0:0:0:2/block/sdc/sdc15          u:object_r:sysfs_devices_block:s0
genfscon sysfs /devices/platform/soc/112b0000.ufshci/host0/target0:0:0/0:0:0:2/block/sdc/sdc33          u:object_r:sysfs_devices_block:s0
genfscon sysfs /devices/platform/soc/112b0000.ufshci/host0/target0:0:0/0:0:0:2/block/sdc/sdc43          u:object_r:sysfs_devices_block:s0
genfscon sysfs /devices/platform/soc/112b0000.ufshci/host0/target0:0:0/0:0:0:2/block/sdc/sdc53          u:object_r:sysfs_devices_block:s0
genfscon sysfs /devices/platform/soc/112b0000.ufshci/host0/target0:0:0/0:0:0:2/block/sdc/sdc61          u:object_r:sysfs_devices_block:s0
genfscon sysfs /devices/platform/112b0000.ufshci/host0/target0:0:0/0:0:0:0/block/sda                    u:object_r:sysfs_devices_block:s0
genfscon sysfs /devices/platform/112b0000.ufshci/host0/target0:0:0/0:0:0:1/block/sdb                    u:object_r:sysfs_devices_block:s0
genfscon sysfs /devices/platform/112b0000.ufshci/host0/target0:0:0/0:0:0:2/block/sdc                    u:object_r:sysfs_devices_block:s0
genfscon sysfs /devices/platform/112b0000.ufshci/host0/target0:0:0/0:0:0:2/block/sdc/sdc2               u:object_r:sysfs_devices_block:s0
genfscon sysfs /devices/platform/112b0000.ufshci/host0/target0:0:0/0:0:0:2/block/sdc/sdc15              u:object_r:sysfs_devices_block:s0
genfscon sysfs /devices/platform/112b0000.ufshci/host0/target0:0:0/0:0:0:2/block/sdc/sdc33              u:object_r:sysfs_devices_block:s0
genfscon sysfs /devices/platform/112b0000.ufshci/host0/target0:0:0/0:0:0:2/block/sdc/sdc43              u:object_r:sysfs_devices_block:s0
genfscon sysfs /devices/platform/112b0000.ufshci/host0/target0:0:0/0:0:0:2/block/sdc/sdc53              u:object_r:sysfs_devices_block:s0
genfscon sysfs /devices/platform/112b0000.ufshci/host0/target0:0:0/0:0:0:2/block/sdc/sdc61              u:object_r:sysfs_devices_block:s0
genfscon sysfs /devices/platform/112b0000.ufshci/host0/scsi_host/host0                                  u:object_r:sysfs_devices_block:s0
genfscon sysfs /devices/platform/11230000.mmc/mmc_host/mmc0/mmc0:0001/block/mmcblk0                     u:object_r:sysfs_devices_block:s0
genfscon sysfs /devices/platform/11230000.msdc/mmc_host/mmc0/mmc0:0001/block/mmcblk0                    u:object_r:sysfs_devices_block:s0
genfscon sysfs /devices/platform/11240000.mmc/mmc_host/mmc0/mmc0:aaaa/block/mmcblk0                     u:object_r:sysfs_devices_block:s0
genfscon sysfs /devices/platform/11240000.mmc/mmc_host/mmc0/mmc0:0001/block/mmcblk0                     u:object_r:sysfs_devices_block:s0
genfscon sysfs /devices/platform/11270000.ufshci/host0/target0:0:0/0:0:0:0/block/sda                    u:object_r:sysfs_devices_block:s0
genfscon sysfs /devices/platform/11270000.ufshci/host0/target0:0:0/0:0:0:1/block/sdb                    u:object_r:sysfs_devices_block:s0
genfscon sysfs /devices/platform/11270000.ufshci/host0/target0:0:0/0:0:0:2/block/sdc                    u:object_r:sysfs_devices_block:s0

# Date : 2019/07/12
# Purpose:dumpstate mmcblk1 access
genfscon sysfs /devices/platform/externdevice/mmc_host/mmc0     u:object_r:sysfs_devices_block:s0
genfscon sysfs /devices/platform/externdevice/mmc_host/mmc1     u:object_r:sysfs_devices_block:s0
genfscon sysfs /devices/platform/soc/11240000.mmc/mmc_host/mmc1 u:object_r:sysfs_devices_block:s0

# Date : 2019/09/16
# Purpose : mtk factory fingerprint settings
genfscon sysfs /module/gf_spi_tee u:object_r:sysfs_gf_spi_tee:s0

# Date : 2019/10/22
# Purpose : mrdump_tool(copy_process by aee_aedv) need to write data to lbaooo
genfscon sysfs /module/mrdump/version                          u:object_r:sysfs_mrdump:s0
genfscon sysfs /kernel/mrdump_version                          u:object_r:sysfs_mrdump:s0
genfscon sysfs /firmware/devicetree/base/chosen/mrdump,lk      u:object_r:sysfs_mrdump:s0
genfscon sysfs /module/mrdump/parameters/lbaooo                u:object_r:sysfs_mrdump:s0
genfscon sysfs /firmware/devicetree/base/memory/reg            u:object_r:sysfs_memory:s0
genfscon sysfs /firmware/devicetree/base/memory@0x40000000/reg u:object_r:sysfs_memory:s0

# mtk APUSYS information reading
genfscon sysfs /devices/virtual/misc/apusys/queue u:object_r:sysfs_apusys_queue:s0

# 2019/08/24
genfscon sysfs /class/sensor           u:object_r:sysfs_sensor:s0
genfscon sysfs /devices/virtual/sensor u:object_r:sysfs_sensor:s0

# MTEE trusty
genfscon sysfs /devices/platform/trusty u:object_r:mtee_trusty_file:s0

# 2019/09/05
# Purpose: Allow powerhal to control kernel resources
genfscon sysfs /module/ged      u:object_r:sysfs_ged:s0
genfscon sysfs /module/fbt_cpu  u:object_r:sysfs_fbt_cpu:s0
genfscon sysfs /module/fbt_fteh u:object_r:sysfs_fbt_fteh:s0
genfscon sysfs /module/xgf      u:object_r:sysfs_xgf:s0
genfscon sysfs /module/mtk_fpsgo u:object_r:sysfs_mtk_fpsgo:s0
genfscon sysfs /module/mtk_core_ctl u:object_r:sysfs_mtk_core_ctl:s0

# 2019/09/05
# Purpose: Allow powerhal to control cache audit
genfscon sysfs /module/cache_ctrl u:object_r:sysfs_cache_ctrl:s0
genfscon sysfs /module/pftch_qos  u:object_r:sysfs_pftch_qos:s0

# 2019/09/19
# Purpose: Allow powerhal to trigger task-turbo
genfscon sysfs /module/task_turbo u:object_r:sysfs_task_turbo:s0

# Date : 2019/09/23
# Operation: SQC
# Purpose : Allow powerHAL to control touch boost
genfscon sysfs /devices/platform/mtk-tpd2.0/change_rate u:object_r:sysfs_change_rate:s0

# Date : 2019/10/16
# Operation: SQC
# Purpose : Allow powerHAL to control /sys/fs/ext4/xxx/disable_barrier
genfscon sysfs /fs/ext4/sdc46/disable_barrier u:object_r:sysfs_ext4_disable_barrier:s0
genfscon sysfs /fs/ext4/sdc47/disable_barrier u:object_r:sysfs_ext4_disable_barrier:s0
genfscon sysfs /fs/ext4/sdc48/disable_barrier u:object_r:sysfs_ext4_disable_barrier:s0
genfscon sysfs /fs/ext4/dm-6/disable_barrier  u:object_r:sysfs_ext4_disable_barrier:s0

# Date : WK19.38
# Purpose: Android Migration for video codec driver
genfscon sysfs /firmware/devicetree/base/model u:object_r:sysfs_device_tree_model:s0

# Date : 2019/10/11
# Purpose : allow system_server to access /sys/kernel/mm/ksm/pages_xxx
genfscon sysfs /kernel/mm/ksm/pages_shared   u:object_r:sysfs_pages_shared:s0
genfscon sysfs /kernel/mm/ksm/pages_sharing  u:object_r:sysfs_pages_sharing:s0
genfscon sysfs /kernel/mm/ksm/pages_unshared u:object_r:sysfs_pages_unshared:s0
genfscon sysfs /kernel/mm/ksm/pages_volatile u:object_r:sysfs_pages_volatile:s0

# Date : 2019/10/25
# Purpose : /proc/device-tree/chosen/atag,chipid or  /sysfs/firmware/devicetree/base/chosen/atag,chipid
genfscon sysfs /firmware/devicetree/base/chosen/atag,chipid u:object_r:sysfs_chipid:s0

# Date : 2019/10/18
# Purpose : allow system_server to access rt5509 param and calib node
genfscon sysfs /devices/platform/1100f000.i2c3/i2c-3/3-0034/rt5509_param.0      u:object_r:sysfs_rt_param:s0
genfscon sysfs /devices/platform/1100f000.i2c3/i2c-3/3-0034/rt5509_cal/rt5509.0 u:object_r:sysfs_rt_calib:s0

# Date : 2020/07/10
# Purpose : allow media sources to access /sys/bus/platform/drivers/emi_ctrl/*
genfscon sysfs /bus/platform/drivers/emi_ctrl/concurrency_scenario u:object_r:sysfs_emi_ctrl_concurrency_scenario:s0

# Date : 2019/12/12
# Purpose : allow media sources to access /sys/bus/platform/drivers/mem_bw_ctrl/*
genfscon sysfs /bus/platform/drivers/mem_bw_ctrl/concurrency_scenario u:object_r:sysfs_concurrency_scenario:s0

# Date : WK20.07
# Operation: R migration
# Purpose : Add permission for new device node.
genfscon sysfs /firmware/devicetree/base/chosen/atag,meta u:object_r:sysfs_meta_info:s0

genfscon sysfs /bus/platform/drivers/cache_parity/cache_status u:object_r:sysfs_cache_status:s0
genfscon sysfs /bus/platform/drivers/cache_parity/status u:object_r:sysfs_cache_status:s0

# Date : WK20.17
# Purpose: Allow powerhal to control ged hal
genfscon sysfs /kernel/ged u:object_r:sysfs_ged:s0

# Date : WK20.19
# Purpose: Allow powerhal to control fpsgo
genfscon sysfs /kernel/fpsgo u:object_r:sysfs_fpsgo:s0

# Date : WK20.23
# Purpose: Allow powerhal to control gbe
genfscon sysfs /kernel/gbe u:object_r:sysfs_gbe:s0

# Date : 2020/06/12
# Purpose : Allow powerhal to control mali power policy
genfscon sysfs /class/misc/mali0/device/power_policy u:object_r:sysfs_mali_power_policy:s0
genfscon sysfs /devices/platform/13000000.mali/power_policy u:object_r:sysfs_mali_power_policy:s0

# Date : WK20.25
# Operation: R migration
# Purpose : for VTS NetdSELinuxTest.CheckProperMTULabels requirement.
genfscon sysfs /devices/platform/18000000.wifi/net/wlan0/mtu            u:object_r:sysfs_net:s0
genfscon sysfs /devices/platform/18000000.wifi/net/wlan1/mtu            u:object_r:sysfs_net:s0
genfscon sysfs /devices/platform/soc/18000000.wifi/net/wlan0/mtu        u:object_r:sysfs_net:s0
genfscon sysfs /devices/platform/soc/18000000.wifi/net/wlan1/mtu        u:object_r:sysfs_net:s0
genfscon sysfs /devices/platform/180f0000.wifi/net/wlan0/mtu            u:object_r:sysfs_net:s0
genfscon sysfs /devices/platform/180f0000.wifi/net/p2p0/mtu             u:object_r:sysfs_net:s0
genfscon sysfs /devices/platform/bus/180f0000.WIFI/net/wlan0/mtu        u:object_r:sysfs_net:s0
genfscon sysfs /devices/platform/bus/180f0000.WIFI/net/p2p0/mtu         u:object_r:sysfs_net:s0
genfscon sysfs /devices/platform/bus/180f0000.wifi/net/wlan0/mtu        u:object_r:sysfs_net:s0
genfscon sysfs /devices/platform/bus/180f0000.wifi/net/p2p0/mtu         u:object_r:sysfs_net:s0

# Date : 2020/07/02
# Purpose : mtk nanohub sensor state detect
genfscon sysfs /bus/platform/drivers/mtk_nanohub/state u:object_r:sysfs_mtk_nanohub_state:s0

# Date : 2020/07/13
# Purpose : Add permission for access dvfsrc dbg sysfs
genfscon sysfs /devices/platform/10012000.dvfsrc/helio-dvfsrc                     u:object_r:sysfs_dvfsrc_dbg:s0
genfscon sysfs /devices/platform/10012000.dvfsrc/10012000.dvfsrc:dvfsrc-debug     u:object_r:sysfs_dvfsrc_dbg:s0
genfscon sysfs /devices/platform/10012000.dvfsrc/10012000.dvfsrc:dvfsrc-up        u:object_r:sysfs_dvfsrc_dbg:s0
genfscon sysfs /devices/platform/10012000.dvfsrc/10012000.dvfsrc:dvfsrc-helper    u:object_r:sysfs_dvfsrc_dbg:s0
genfscon sysfs /devices/platform/1c00f000.dvfsrc/1c00f000.dvfsrc:dvfsrc-helper    u:object_r:sysfs_dvfsrc_dbg:s0
genfscon sysfs /devices/platform/soc/10012000.dvfsrc/10012000.dvfsrc:dvfsrc-debug u:object_r:sysfs_dvfsrc_dbg:s0
genfscon sysfs /devices/platform/soc/10012000.dvfsrc/10012000.dvfsrc:dvfsrc-up    u:object_r:sysfs_dvfsrc_dbg:s0
genfscon sysfs /devices/platform/soc/10012000.dvfsrc/10012000.dvfsrc:dvfsrc-helper u:object_r:sysfs_dvfsrc_dbg:s0
genfscon sysfs /devices/platform/soc/1c00f000.dvfsrc/1c00f000.dvfsrc:dvfsrc-helper u:object_r:sysfs_dvfsrc_dbg:s0


# Date : 2020/07/31
# Purpose: add permission for /sys/kernel/apusys/
genfscon sysfs /kernel/apusys/ u:object_r:sysfs_apusys:s0

# Date : WK20.33
# Operation: R migration
# Purpose: Add permission for access aux_adc
genfscon sysfs /bus/platform/drivers/mt6577-auxadc           u:object_r:sys_mt6577_auxadc:s0
genfscon sysfs /devices/platform/11001000.auxadc/iio:device2 u:object_r:sys_mt6577_auxadc:s0

# Date : 2020/08/11
# Purpose : For kernel 4.14 platforms, allow system_server to access rt5509 param and calib node
genfscon sysfs /devices/platform/rt5509_param.0     u:object_r:sysfs_rt_param:s0
genfscon sysfs /devices/virtual/rt5509_cal/rt5509.0 u:object_r:sysfs_rt_calib:s0

# Date : 2020/08/19
# Purpose : Add permission for access dvfsrc dbg sysfs
genfscon sysfs /devices/platform/soc/10012000.dvfsrc/mtk-dvfsrc-devfreq/devfreq/mtk-dvfsrc-devfreq u:object_r:sysfs_dvfsrc_devfreq:s0
genfscon sysfs /devices/platform/10012000.dvfsrc/mtk-dvfsrc-devfreq/devfreq/mtk-dvfsrc-devfreq     u:object_r:sysfs_dvfsrc_devfreq:s0
genfscon sysfs /devices/platform/1c00f000.dvfsrc/mtk-dvfsrc-devfreq/devfreq/mtk-dvfsrc-devfreq     u:object_r:sysfs_dvfsrc_devfreq:s0
genfscon sysfs /devices/platform/soc/1c00f000.dvfsrc/mtk-dvfsrc-devfreq/devfreq/mtk-dvfsrc-devfreq u:object_r:sysfs_dvfsrc_devfreq:s0

# Date : 2020/08/21
# Purpose : allow aee_aedv to access /sys/bus/platform/drivers/systracker node
genfscon sysfs /bus/platform/drivers/systracker u:object_r:sysfs_systracker:s0

# Date : 2020/09/03
# Purpose: mtk MMQoS set camera max BW
genfscon sysfs /devices/platform/soc/soc:interconnect/mmqos_hrt/camera_max_bw u:object_r:sysfs_camera_max_bw:s0
genfscon sysfs /devices/platform/interconnect/mmqos_hrt/camera_max_bw u:object_r:sysfs_camera_max_bw_v2:s0

# Date : 2020/06/15
# Purpose: mtk MMQoS scen change
genfscon sysfs /devices/platform/soc/soc:interconnect/mmqos_hrt/mtk_mmqos_scen u:object_r:sysfs_mtk_mmqos_scen:s0
genfscon sysfs /devices/platform/interconnect/mmqos_hrt/mtk_mmqos_scen u:object_r:sysfs_mtk_mmqos_scen_v2:s0

# Date : 2020/09/29
# Purpose: add permission for /sys/kernel/eara_thermal/
genfscon sysfs /kernel/eara_thermal/ u:object_r:sysfs_eara_thermal:s0

# Date : 2021/3/12
# Purpose : Allow powerhal to control mali power onoff
genfscon sysfs /class/misc/mali0/device/pm_poweroff        u:object_r:sysfs_mali_poweroff:s0
genfscon sysfs /devices/platform/13000000.mali/pm_poweroff u:object_r:sysfs_mali_poweroff:s0

# Date: 2021/05/28
# Purpose: allow DcxoSetCap set dcxo calibration
genfscon sysfs /devices/platform/soc/1000d000.pwrap/1000d000.pwrap:mt6357-pmic/mt6357-dcxo/dcxo_board_offset u:object_r:sysfs_dcxo:s0
genfscon sysfs /devices/platform/soc/1000d000.pwrap/1000d000.pwrap:mt6357-pmic/mt6357-dcxo/nvram_board_offset u:object_r:sysfs_dcxo:s0
genfscon sysfs /devices/platform/soc/1000d000.pwrap/1000d000.pwrap:mt6357-pmic/mt6357-dcxo/dcxo_capid u:object_r:sysfs_dcxo:s0
genfscon sysfs /power/clk_buf/ u:object_r:sysfs_dcxo:s0

# Date : 2021/06/04
# Purpose: Allow mobile log to read apusysy log
genfscon proc /apusys_logger/seq_logl u:object_r:proc_apusys_up_seq_logl:s0

# labeling sysfs wakeup files to avoid sepolicy violation
genfscon sysfs /devices/platform/1000d000.pwrap/1000d000.pwrap:main_pmic/mt6357-charger-type-detection/power_supply/mtk_charger_type/wakeup u:object_r:sysfs_wakeup:s0
genfscon sysfs /devices/platform/1000d000.pwrap/1000d000.pwrap:main_pmic/mt6357-gauge/power_supply/battery/wakeup                           u:object_r:sysfs_wakeup:s0
genfscon sysfs /devices/platform/1000d000.pwrap/1000d000.pwrap:main_pmic/mt6357-gauge/power_supply/mtk-gauge/wakeup                         u:object_r:sysfs_wakeup:s0
genfscon sysfs /devices/platform/1000d000.pwrap/1000d000.pwrap:main_pmic/mt635x-auxadc/wakeup/wakeup                                        u:object_r:sysfs_wakeup:s0
genfscon sysfs /devices/platform/1000d000.pwrap/1000d000.pwrap:main_pmic/mt6397-rtc/rtc/rtc0/wakeup                                         u:object_r:sysfs_wakeup:s0
genfscon sysfs /devices/platform/1000d000.pwrap/1000d000.pwrap:main_pmic/mt6397-rtc/wakeup/wakeup                                           u:object_r:sysfs_wakeup:s0
genfscon sysfs /devices/platform/10026000.pwrap/10026000.pwrap:mt6359-pmic/mt6359-rtc/rtc/rtc0/wakeup                                       u:object_r:sysfs_wakeup:s0
genfscon sysfs /devices/platform/10026000.pwrap/10026000.pwrap:mt6359-pmic/mt6359-rtc/wakeup/wakeup                                         u:object_r:sysfs_wakeup:s0
genfscon sysfs /devices/platform/10026000.pwrap/10026000.pwrap:mt6359-pmic/mt635x-auxadc/wakeup/wakeup                                      u:object_r:sysfs_wakeup:s0
genfscon sysfs /devices/platform/10026000.pwrap/10026000.pwrap:mt6359p/mt6359p-gauge/power_supply/battery/wakeup                            u:object_r:sysfs_wakeup:s0
genfscon sysfs /devices/platform/10026000.pwrap/10026000.pwrap:mt6359p/mt6359p-gauge/power_supply/mtk-gauge/wakeup                          u:object_r:sysfs_wakeup:s0
genfscon sysfs /devices/platform/10026000.pwrap/10026000.pwrap:mt6359p/mt6359p-rtc/rtc/rtc0/wakeup                                          u:object_r:sysfs_wakeup:s0
genfscon sysfs /devices/platform/10026000.pwrap/10026000.pwrap:mt6359p/mt6359p-rtc/wakeup/wakeup                                            u:object_r:sysfs_wakeup:s0
genfscon sysfs /devices/platform/10026000.pwrap/10026000.pwrap:mt6359p/mt635x-auxadc/wakeup/wakeup                                          u:object_r:sysfs_wakeup:s0
genfscon sysfs /devices/platform/10027000.spmi/spmi-0/0-09/10027000.spmi:mt6362@9:chg/power_supply/10027000.spmi:mt6362@9:chg/wakeup        u:object_r:sysfs_wakeup:s0
genfscon sysfs /devices/platform/10027000.spmi/spmi-0/0-09/10027000.spmi:mt6362@9:tcpc/tcpc/type_c_port0/dual-role-type_c_port0/wakeup      u:object_r:sysfs_wakeup:s0
genfscon sysfs /devices/platform/10027000.spmi/spmi-0/0-09/10027000.spmi:mt6362@9:tcpc/wakeup/wakeup                                        u:object_r:sysfs_wakeup:s0
genfscon sysfs /devices/platform/10027000.spmi/spmi-0/0-09/wakeup/wakeup                                                                    u:object_r:sysfs_wakeup:s0
genfscon sysfs /devices/platform/11016000.i2c5/i2c-5/5-0034/mt6370_pmu_charger/power_supply/mt6370_pmu_charger/wakeup                       u:object_r:sysfs_wakeup:s0
genfscon sysfs /devices/platform/11016000.i2c5/i2c-5/5-0034/wakeup/wakeup                                                                   u:object_r:sysfs_wakeup:s0
genfscon sysfs /devices/platform/11016000.i2c5/i2c-5/5-004e/tcpc/type_c_port0/wakeup                                                        u:object_r:sysfs_wakeup:s0
genfscon sysfs /devices/platform/11016000.i2c5/i2c-5/5-004e/wakeup/wakeup                                                                   u:object_r:sysfs_wakeup:s0
genfscon sysfs /devices/platform/11201000.mtu3_0/wakeup/wakeup                                                                              u:object_r:sysfs_wakeup:s0
genfscon sysfs /devices/platform/11201000.usb/wakeup/wakeup                                                                                 u:object_r:sysfs_wakeup:s0
genfscon sysfs /devices/platform/11cb0000.i2c3/i2c-3/3-0018/wakeup/wakeup                                                                   u:object_r:sysfs_wakeup:s0
genfscon sysfs /devices/platform/11d00000.i2c5/i2c-5/5-0034/mt6360_pmu_chg.2.auto/wakeup/wakeup                                             u:object_r:sysfs_wakeup:s0
genfscon sysfs /devices/platform/11d00000.i2c5/i2c-5/5-0034/wakeup/wakeup                                                                   u:object_r:sysfs_wakeup:s0
genfscon sysfs /devices/platform/11d00000.i2c5/i2c-5/5-004e/tcpc/type_c_port0/dual-role-type_c_port0/wakeup                                 u:object_r:sysfs_wakeup:s0
genfscon sysfs /devices/platform/11e00000.i2c5/i2c-5/5-0034/mt6360_pmu_chg.2.auto/wakeup/wakeup                                             u:object_r:sysfs_wakeup:s0
genfscon sysfs /devices/platform/11e00000.i2c5/i2c-5/5-0034/wakeup/wakeup                                                                   u:object_r:sysfs_wakeup:s0
genfscon sysfs /devices/platform/11e00000.i2c5/i2c-5/5-004e/tcpc/type_c_port0/dual-role-type_c_port0/wakeup                                 u:object_r:sysfs_wakeup:s0
genfscon sysfs /devices/platform/11e01000.i2c5/i2c-5/5-0034/mt6360_pmu_chg.3.auto/wakeup/wakeup                                             u:object_r:sysfs_wakeup:s0
genfscon sysfs /devices/platform/11e01000.i2c5/i2c-5/5-0034/wakeup/wakeup                                                                   u:object_r:sysfs_wakeup:s0
genfscon sysfs /devices/platform/11e01000.i2c5/i2c-5/5-004e/tcpc/type_c_port0/dual-role-type_c_port0/wakeup                                 u:object_r:sysfs_wakeup:s0
genfscon sysfs /devices/platform/11f00000.i2c5/i2c-5/5-0034/mt6360_pmu_chg.2.auto/wakeup/wakeup                                             u:object_r:sysfs_wakeup:s0
genfscon sysfs /devices/platform/11f00000.i2c5/i2c-5/5-0034/wakeup/wakeup                                                                   u:object_r:sysfs_wakeup:s0
genfscon sysfs /devices/platform/11f00000.i2c5/i2c-5/5-004e/tcpc/type_c_port0/dual-role-type_c_port0/wakeup                                 u:object_r:sysfs_wakeup:s0
genfscon sysfs /devices/platform/15004000.ispsys/wakeup/wakeup                                                                              u:object_r:sysfs_wakeup:s0
genfscon sysfs /devices/platform/15020000.imgsys/wakeup/wakeup                                                                              u:object_r:sysfs_wakeup:s0
genfscon sysfs /devices/platform/battery/power_supply/ac/wakeup                                                                             u:object_r:sysfs_wakeup:s0
genfscon sysfs /devices/platform/battery/power_supply/battery/wakeup                                                                        u:object_r:sysfs_wakeup:s0
genfscon sysfs /devices/platform/battery/power_supply/usb/wakeup                                                                            u:object_r:sysfs_wakeup:s0
genfscon sysfs /devices/platform/battery/power_supply/wireless/wakeup                                                                       u:object_r:sysfs_wakeup:s0
genfscon sysfs /devices/platform/charger/power_supply/mtk-master-charger/wakeup                                                             u:object_r:sysfs_wakeup:s0
genfscon sysfs /devices/platform/charger/power_supply/mtk-slave-charger/wakeup                                                              u:object_r:sysfs_wakeup:s0
genfscon sysfs /devices/platform/mt_charger/power_supply/ac/wakeup                                                                          u:object_r:sysfs_wakeup:s0
genfscon sysfs /devices/platform/mt_charger/power_supply/charger/wakeup                                                                     u:object_r:sysfs_wakeup:s0
genfscon sysfs /devices/platform/mt_charger/power_supply/usb/wakeup                                                                         u:object_r:sysfs_wakeup:s0
genfscon sysfs /devices/platform/mt_charger/wakeup/wakeup                                                                                   u:object_r:sysfs_wakeup:s0
genfscon sysfs /devices/platform/mtk_lpm/mtk_cpuidle_pm/wakeup/wakeup                                                                       u:object_r:sysfs_wakeup:s0
genfscon sysfs /devices/platform/mt-rtc/rtc/rtc0/wakeup                                                                                     u:object_r:sysfs_wakeup:s0
genfscon sysfs /devices/platform/mt-rtc/wakeup/wakeup                                                                                       u:object_r:sysfs_wakeup:s0
genfscon sysfs /devices/platform/soc/1000f000.pwrap/1000f000.pwrap:mt6392/mt6397-rtc/rtc/rtc0/wakeup                                        u:object_r:sysfs_wakeup:s0
genfscon sysfs /devices/platform/soc/1000f000.pwrap/1000f000.pwrap:mt6392/mt6397-rtc/wakeup/wakeup                                          u:object_r:sysfs_wakeup:s0
genfscon sysfs /devices/platform/soc/11201000.usb0/wakeup/wakeup                                                                            u:object_r:sysfs_wakeup:s0
genfscon sysfs /devices/platform/soc/11e00000.i2c/i2c-7/7-0034/mt6360_chg.1.auto/power_supply/mt6360_chg.1.auto/wakeup                      u:object_r:sysfs_wakeup:s0
genfscon sysfs /devices/platform/soc/11e00000.i2c/i2c-7/7-0034/mt6360_chg.1.auto/wakeup/wakeup                                              u:object_r:sysfs_wakeup:s0
genfscon sysfs /devices/platform/soc/11e00000.i2c/i2c-7/7-0034/wakeup/wakeup                                                                u:object_r:sysfs_wakeup:s0
genfscon sysfs /devices/platform/soc/11e00000.i2c/i2c-7/7-004e/tcpc/type_c_port0/wakeup                                                     u:object_r:sysfs_wakeup:s0
genfscon sysfs /devices/platform/soc/11e00000.i2c/i2c-7/7-004e/wakeup/wakeup                                                                u:object_r:sysfs_wakeup:s0
genfscon sysfs /devices/platform/soc/15020000.imgsys_config/wakeup/wakeup                                                                   u:object_r:sysfs_wakeup:s0
genfscon sysfs /devices/platform/soc/18002000.consys/wakeup/wakeup                                                                          u:object_r:sysfs_wakeup:s0
genfscon sysfs /devices/platform/soc/1a000000.camisp_legacy/wakeup/wakeup                                                                   u:object_r:sysfs_wakeup:s0
genfscon sysfs /devices/platform/soc/mt_usb/musb-hdrc/dual_role_usb/dual-role-usb20/wakeup                                                  u:object_r:sysfs_wakeup:s0
genfscon sysfs /devices/platform/soc/soc:mfgsys-async/wakeup/wakeup                                                                         u:object_r:sysfs_wakeup:s0
genfscon sysfs /devices/platform/trusty/wakeup/wakeup                                                                                       u:object_r:sysfs_wakeup:s0
genfscon sysfs /devices/virtual/misc/alarm/wakeup                                                                                           u:object_r:sysfs_wakeup:s0
genfscon sysfs /devices/virtual/usb_rawbulk/atc/wakeup                                                                                      u:object_r:sysfs_wakeup:s0
genfscon sysfs /devices/virtual/usb_rawbulk/data/wakeup                                                                                     u:object_r:sysfs_wakeup:s0
genfscon sysfs /devices/virtual/usb_rawbulk/dummy0/wakeup                                                                                   u:object_r:sysfs_wakeup:s0
genfscon sysfs /devices/virtual/usb_rawbulk/dummy1/wakeup                                                                                   u:object_r:sysfs_wakeup:s0
genfscon sysfs /devices/virtual/usb_rawbulk/dummy2/wakeup                                                                                   u:object_r:sysfs_wakeup:s0
genfscon sysfs /devices/virtual/usb_rawbulk/ets/wakeup                                                                                      u:object_r:sysfs_wakeup:s0
genfscon sysfs /devices/virtual/usb_rawbulk/gps/wakeup                                                                                      u:object_r:sysfs_wakeup:s0
genfscon sysfs /devices/virtual/usb_rawbulk/pcv/wakeup                                                                                      u:object_r:sysfs_wakeup:s0
genfscon sysfs /devices/virtual/wakeup/wakeup                                                                                               u:object_r:sysfs_wakeup:s0
genfscon sysfs /devices/platform/soc/1f000000.mdp/wakeup/wakeup2/event_count                                                            u:object_r:sysfs_wakeup:s0
genfscon sysfs /devices/platform/soc/1f000000.mdp/wakeup/wakeup2                                                                        u:object_r:sysfs_wakeup:s0

# allow gpu to set dbg
genfscon sysfs /disp/dbg1  u:object_r:sysfs_gpu_mtk:s0
genfscon sysfs /disp/dbg2  u:object_r:sysfs_gpu_mtk:s0
genfscon sysfs /disp/dbg3  u:object_r:sysfs_gpu_mtk:s0

# Purpose: Add permission for /sys/devices/platform/bootdevice/host0/target0:0:0/0:0:0:0/vpd_pg80
genfscon sysfs /devices/platform/bootdevice/host0/target0:0:0/0:0:0:0/vpd_pg80  u:object_r:sysfs_vpd:s0

# Purpose: Add permission for /sys/devices/platform/soc/11270000.ufshci/host0/target0:0:0/0:0:0:0/vpd_pg80
genfscon sysfs /devices/platform/soc/11270000.ufshci/host0/target0:0:0/0:0:0:0/vpd_pg80  u:object_r:sysfs_vpd:s0

# RTK Device
genfscon sysfs /class/extdev_io u:object_r:sysfs_extdev:s0

# 2021/8/25
# allow powerhal to access /sys/kernel/cm_mgr/dbg_cm_mgr
genfscon sysfs /kernel/cm_mgr u:object_r:sysfs_cm_mgr:s0

##########################
# debugfs files
#
genfscon debugfs /displowpower       u:object_r:debugfs_fb:s0
genfscon debugfs /disp               u:object_r:debugfs_fb:s0
genfscon debugfs /dispsys            u:object_r:debugfs_fb:s0
genfscon debugfs /fbconfig           u:object_r:debugfs_fb:s0
genfscon debugfs /fpsgo              u:object_r:debugfs_fpsgo:s0
genfscon debugfs /ion/clients        u:object_r:debugfs_ion:s0
genfscon debugfs /mtkfb              u:object_r:debugfs_fb:s0
genfscon debugfs /mmprofile          u:object_r:debugfs_fb:s0

##########################
# other files
#
genfscon rawfs /   u:object_r:rawfs:s0

# move from plat_private to non_plat
genfscon sysfs /firmware/devicetree/base/chosen/atag,boot    u:object_r:sysfs_boot_info:s0

# 2021/08/24
# Purpose: add dmabuf heap debug info for memtrack
genfscon proc /dma_heap u:object_r:proc_dmaheap:s0

# For CachedAppOptimizer
genfscon proc /mtk_mdp_debug u:object_r:proc_mtk_mdp_debug:s0

# AudioManager/WiredAccessoryManager, extcon uevents
genfscon sysfs /devices/platform/14800000.dp_tx/extcon u:object_r:sysfs_extcon:s0

# dumpstate mmcblk access
genfscon sysfs /devices/platform/soc/11230000.mmc/mmc_host/mmc0 u:object_r:sysfs_devices_block:s0
genfscon sysfs /devices/platform/soc/11240000.mmc/mmc_host/mmc0 u:object_r:sysfs_devices_block:s0

# Purpose: allow otg access
genfscon sysfs /devices/platform/soc/11201000.usb/11200000.xhci/usb1 u:object_r:sysfs_usb_nonplat:s0

# Purpose: allow mgq access
genfscon proc /mgq u:object_r:proc_mgq:s0

# Vibrator
genfscon sysfs /kernel/thunderquake_engine    u:object_r:sysfs_vibrator:s0

# GPU
genfscon sysfs /devices/platform/13040000.mali/dma_buf_gpu_mem u:object_r:sysfs_gpu:s0
genfscon sysfs /devices/platform/13040000.mali/kprcs u:object_r:sysfs_gpu:s0
genfscon sysfs /devices/platform/13040000.mali/total_gpu_mem u:object_r:sysfs_gpu:s0
