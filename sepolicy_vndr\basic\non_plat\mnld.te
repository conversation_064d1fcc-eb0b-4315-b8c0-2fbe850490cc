# ==============================================
# Policy File of /vendor/bin/mnld Executable File

# ==============================================
# Type Declaration
# ==============================================
type mnld, domain;
type mnld_exec, exec_type, file_type, vendor_file_type;
typeattribute mnld mlstrustedsubject;

# ==============================================
# Common SEPolicy Rule
# ==============================================
# STOPSHIP: Permissive is not allowed. CTS violation!
init_daemon_domain(mnld)

net_domain(mnld)

# Purpose : For communicate with AGPSD by socket
allow mnld agpsd_data_file:dir create_dir_perms;
allow mnld agpsd_data_file:sock_file create_file_perms;
allow mnld mtk_agpsd:unix_dgram_socket sendto;
allow mnld sysfs_wake_lock:file rw_file_perms;

# Purpose : For access NVRAM data
allow mnld nvram_data_file:dir create_dir_perms;
allow mnld nvram_data_file:file create_file_perms;
allow mnld nvram_data_file:lnk_file r_file_perms;
allow mnld nvdata_file:lnk_file r_file_perms;
allow mnld nvram_device:blk_file rw_file_perms;
allow mnld nvram_device:chr_file rw_file_perms;
allow mnld nvdata_file:dir create_dir_perms;
allow mnld nvdata_file:file create_file_perms;
allow mnld gsi_metadata_file:dir search;

# Purpose : For access kernel device
allow mnld mnld_data_file:dir rw_dir_perms;
allow mnld mnld_data_file:sock_file create_file_perms;
allow mnld mnld_device:chr_file rw_file_perms;
allow mnld stpgps_device:chr_file rw_file_perms;
allow mnld gps2scp_device:chr_file rw_file_perms;
allow mnld gps_pwr_device:chr_file rw_file_perms;
allow mnld mnld_data_file:file create_file_perms;
allow mnld mnld_data_file:fifo_file create_file_perms;
allow mnld gps_emi_device:chr_file rw_file_perms;

# Purpose : For init process
allow mnld init:udp_socket rw_socket_perms_no_ioctl;

# Purpose : For RTKSDK
allow mnld proc_net:file r_file_perms;
allow mnld gps_data_file:sock_file create_file_perms;

# Send the message to the LBS HIDL Service to forward to applications
allow mnld lbs_hidl_service:unix_dgram_socket sendto;

# Send the message to the merged hal Service to forward to applications
allow mnld merged_hal_service:unix_dgram_socket sendto;

# Purpose : For access system data
allow mnld block_device:dir search;
set_prop(mnld, vendor_mtk_mnld_prop)
allow mnld mdlog_device:chr_file rw_file_perms;
allow mnld self:capability fsetid;
allow mnld stpbt_device:chr_file rw_file_perms;
allow mnld gpsdl_device:chr_file rw_file_perms;
allow mnld ttyGS_device:chr_file rw_file_perms;

# Purpose : For file system operations
allow mnld sdcard_type:dir create_dir_perms;
allow mnld sdcard_type:file create_file_perms;
allow mnld tmpfs:lnk_file create_file_perms;
allow mnld mtd_device:dir search;
allow mnld mnt_user_file:lnk_file r_file_perms;
allow mnld mnt_user_file:dir search;
allow mnld gps_data_file:dir { create_dir_perms unlink };
allow mnld gps_data_file:file create_file_perms;
allow mnld gps_data_file:lnk_file r_file_perms;

allow mnld storage_file:lnk_file r_file_perms;

# Date : WK15.30
# Operation : Migration
# Purpose : for device bring up, not to block early migration/sanity
allow mnld proc_lk_env:file rw_file_perms;

# For HIDL, communicate mtk_hal_gnss instead of system_server
allow mnld mtk_hal_gnss:unix_dgram_socket sendto;

# Purpose : MPE sensor HIDL policy
hwbinder_use(mnld)
binder_call(mnld, system_server)
allow mnld fwk_sensor_hwservice:hwservice_manager find;
get_prop(mnld, hwservicemanager_prop)
allow mnld debugfs_tracing:file w_file_perms;

allow mnld mnt_vendor_file:dir search;

#get waks_alarm timer create prop
allow mnld mnld:capability2 wake_alarm;

# Date : WK18.26
# Purpose : for atci gps test
allow mnld atci_service:unix_dgram_socket sendto;

allow mnld sysfs_boot_mode:file r_file_perms;

set_prop(mnld, vendor_mtk_radio_prop)

allow mnld proc_cmdline:file r_file_perms;
allow mnld sysfs_dt_firmware_android:dir search;
allow mnld sysfs_dt_firmware_android:file r_file_perms;
allow mnld metadata_file:dir search;

#for mnld get screen on/off
allow mnld sysfs_leds:dir search;
allow mnld sysfs_leds:file r_file_perms;

#Add for /nvcfg/almanac.dat
allow mnld nvcfg_file:dir w_dir_perms;
allow mnld nvcfg_file:file create_file_perms;
