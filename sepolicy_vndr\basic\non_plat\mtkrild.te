# ==============================================
# Policy File of /vendor/bin/mtkrild Executable File

# ==============================================
# Type Declaration
# ==============================================
type mtkrild_exec, exec_type, file_type, vendor_file_type;
type mtkrild, domain;

# ==============================================
# Common SEPolicy Rule
# ==============================================
init_daemon_domain(mtkrild)
net_domain(mtkrild)

# Access to wake locks
wakelock_use(mtkrild)

# Allow to use vendor binder
vndbinder_use(mtkrild)

# Trigger module auto-load.
allow mtkrild kernel:system module_request;

# Capabilities assigned for mtkrild
allow mtkrild self:capability { setuid net_admin net_raw };

# Control cgroups
allow mtkrild cgroup:dir create_dir_perms;

# Property service
# allow set RIL related properties (radio./net./system./etc)
set_prop(mtkrild, vendor_mtk_ril_active_md_prop)

# allow set muxreport control properties
set_prop(mtkrild, vendor_mtk_ril_cdma_report_prop)
set_prop(mtkrild, vendor_mtk_ril_mux_report_case_prop)
set_prop(mtkrild, vendor_mtk_ctl_muxreport-daemon_prop)

#Dat: 2017/02/14
#Purpose: allow set telephony Sensitive property
set_prop(mtkrild, vendor_mtk_telephony_sensitive_prop)

# Allow access permission to efs files
allow mtkrild efs_file:dir create_dir_perms;
allow mtkrild efs_file:file create_file_perms;
allow mtkrild bluetooth_efs_file:file r_file_perms;
allow mtkrild bluetooth_efs_file:dir r_dir_perms;

# Allow access permission to dir/files
# (radio data/system data/proc/etc)

allow mtkrild sdcardfs:dir r_dir_perms;

allow mtkrild proc_net:file w_file_perms;

# Set and get routes directly via netlink.
allow mtkrild self:netlink_route_socket nlmsg_write;

# Allow read/write to devices/files
allow mtkrild mtk_radio_device:dir search;
allow mtkrild radio_device:chr_file rw_file_perms;
allow mtkrild radio_device:blk_file r_file_perms;
allow mtkrild mtd_device:dir search;

# Allow read/write to tty devices
allow mtkrild tty_device:chr_file rw_file_perms;
allow mtkrild eemcs_device:chr_file rw_file_perms;

allow mtkrild devmap_device:chr_file r_file_perms;
allow mtkrild devpts:chr_file rw_file_perms;
allow mtkrild ccci_device:chr_file rw_file_perms;
allow mtkrild misc_device:chr_file rw_file_perms;
allow mtkrild proc_lk_env:file rw_file_perms;
allow mtkrild para_block_device:blk_file rw_file_perms;

# Allow dir search, fd uses
allow mtkrild block_device:dir search;
allow mtkrild platform_app:fd use;
allow mtkrild radio:fd use;

# For MAL MFI
allow mtkrild mal_mfi_socket:sock_file w_file_perms;

# For ccci sysfs node
allow mtkrild sysfs_ccci:dir search;
allow mtkrild sysfs_ccci:file r_file_perms;

# Allow ioctl in order to control network interface
allowxperm mtkrild self:udp_socket ioctl {SIOCDELRT SIOCSIFFLAGS SIOCSIFADDR SIOCKILLADDR SIOCDEVPRIVATE SIOCDEVPRIVATE_1};

# Allow to trigger IPv6 RS
allow mtkrild node:rawip_socket node_bind;

#Date : W18.15
#Purpose: allow rild access to vendor.ril.ipo system property
set_prop(mtkrild, vendor_mtk_ril_ipo_prop)

# Date : WK18.16
# Operation: P migration
# Purpose: Allow mtkrild to get vendor_mtk_tel_switch_prop
get_prop(mtkrild, vendor_mtk_tel_switch_prop)

#Date: W1817
#Purpose: allow rild access property of vendor_mtk_radio_prop
set_prop(mtkrild, vendor_mtk_radio_prop)

# Date : WK18.26
# Operation: P migration
# Purpose: Allow carrier express HIDL to set vendor property
set_prop(mtkrild, vendor_mtk_cxp_vendor_prop)
allow mtkrild mnt_vendor_file:dir search;
allow mtkrild mnt_vendor_file:file create_file_perms;
allow mtkrild nvdata_file:dir create_dir_perms;
allow mtkrild nvdata_file:file create_file_perms;

# Date : WK18.31
# Operation: P migration
# Purpose: Allow supplementary service HIDL to set vendor property
set_prop(mtkrild, vendor_mtk_ss_vendor_prop)

# Date : W19.16
# Operation: Q migration
# Purpose: Allow mtkrild access to send SUPL INIT to mnld
allow mtkrild mnld:unix_dgram_socket sendto;

# Date : WK19.43
# Purpose: Allow wfc module from rild read system property from wfc module
get_prop(mtkrild, vendor_mtk_wfc_serv_prop)

# Date : 2020/06/11
# Operation: R migration
# Purpose: Allow mtkrild to get system_boot_reason_prop
get_prop(mtkrild, system_boot_reason_prop)

# Date: WK20.31
# Operation: RILD init flow
# Purpose: To handle illegal rild started
set_prop(mtkrild, vendor_mtk_gsm0710muxd_prop)

# Date : 2020/08/17
# Purpose: Allow mtkrild to access netlink_xfrm_socket
allow mtkrild self:netlink_xfrm_socket { create_socket_perms_no_ioctl nlmsg_read nlmsg_write };
