# ==============================================
# Policy File of /vendor/bin/atci_service Executable File
# ==============================================

# ==============================================
# Common SEPolicy Rule
# ==============================================
type atci_service, domain;
type atci_service_exec, exec_type, file_type, vendor_file_type;

init_daemon_domain(atci_service)

allow atci_service block_device:dir search;
allow atci_service misc2_block_device:blk_file rw_file_perms;
allow atci_service misc2_device:chr_file rw_file_perms;
allow atci_service camera_isp_device:chr_file rw_file_perms;
allow atci_service graphics_device:chr_file rw_file_perms;
allow atci_service graphics_device:dir search;
allow atci_service kd_camera_hw_device:chr_file rw_file_perms;
allow atci_service self:capability { sys_nice ipc_lock sys_boot };
allow atci_service nvram_device:chr_file rw_file_perms;
allow atci_service camera_sysram_device:chr_file r_file_perms;
allow atci_service camera_tsf_device:chr_file rw_file_perms;
allow atci_service camera_rsc_device:chr_file rw_file_perms;
allow atci_service camera_gepf_device:chr_file rw_file_perms;
allow atci_service camera_fdvt_device:chr_file rw_file_perms;
allow atci_service camera_wpe_device:chr_file rw_file_perms;
allow atci_service camera_owe_device:chr_file rw_file_perms;
allow atci_service camera_pda_device:chr_file rw_file_perms;
allow atci_service kd_camera_flashlight_device:chr_file rw_file_perms;
allow atci_service ccu_device:chr_file rw_file_perms;
allow atci_service vpu_device:chr_file rw_file_perms;
allow atci_service MTK_SMI_device:chr_file rw_file_perms;
allow atci_service DW9714AF_device:chr_file rw_file_perms;
allow atci_service devmap_device:chr_file rw_file_perms;
allow atci_service sdcard_type:dir create_dir_perms;
allow atci_service sdcard_type:file create_file_perms;
allow atci_service mediaserver:binder call;

# Date : 2015/09/17
# Operation : M-Migration
# Purpose : to operation CCT tool
allow atci_service nvram_device:blk_file rw_file_perms;
allow atci_service input_device:dir r_dir_perms;
allow atci_service input_device:file rw_file_perms;
allow atci_service input_device:chr_file rw_file_perms;
allow atci_service MAINAF_device:chr_file rw_file_perms;
allow atci_service MAIN2AF_device:chr_file rw_file_perms;
allow atci_service MAIN3AF_device:chr_file rw_file_perms;
allow atci_service MAIN4AF_device:chr_file rw_file_perms;
allow atci_service SUBAF_device:chr_file rw_file_perms;
allow atci_service SUB2AF_device:chr_file rw_file_perms;
allow atci_service tmpfs:lnk_file r_file_perms;
allow atci_service self:capability2 block_suspend;

# Date : 2015/10/13
# Operation : M-Migration
# Purpose : to operation CCT tool
allow atci_service mnt_user_file:dir search;
allow atci_service mnt_user_file:lnk_file r_file_perms;
allow atci_service storage_file:lnk_file r_file_perms;

set_prop(atci_service, vendor_mtk_em_prop)

# Date : 2016/03/02
# Operation : M-Migration
# Purpose : to support ATCI touch tool
allow atci_service vendor_shell_exec:file rx_file_perms;

# Date : WK16.33
# Purpose: Allow to access ged for gralloc_extra functions
allow atci_service proc_ged:file rw_file_perms;

# Date : WK16.35
# Operation : Migration
# Purpose : Update camera flashlight driver device file
allow atci_service flashlight_device:chr_file rw_file_perms;

# Date : WK17.01
# Operation : Migration
# Purpose : Update AT_Command NFC function
allow atci_service factory_data_file:sock_file write;

# Date : WK17.23
# Stage: O Migration, SQC
# Purpose: Allow to use HAL PQ
hal_client_domain(atci_service, hal_mtk_pq)

# Date : WK17.28
# Purpose : Allow to execute battery command
allow atci_service MT_pmic_adc_cali_device:chr_file rw_file_perms;

# Date : WK17.43
# Purpose : CCT
allow atci_service CAM_CAL_DRV_device:chr_file rw_file_perms;
allow atci_service CAM_CAL_DRV1_device:chr_file rw_file_perms;
allow atci_service CAM_CAL_DRV2_device:chr_file rw_file_perms;
allow atci_service camera_eeprom_device:chr_file rw_file_perms;
allow atci_service seninf_n3d_device:chr_file rw_file_perms;
allow atci_service fwk_sensor_hwservice:hwservice_manager find;
allow atci_service ion_device:chr_file r_file_perms;
allow atci_service mtk_cmdq_device:chr_file r_file_perms;
allow atci_service mtk_mdp_device:chr_file r_file_perms;
allow atci_service mtk_mdp_sync_device:chr_file r_file_perms;
allow atci_service sw_sync_device:chr_file r_file_perms;
hal_client_domain(atci_service, hal_power)
allow atci_service sysfs_batteryinfo:dir search;
allow atci_service sysfs_batteryinfo:file r_file_perms;
allow atci_service system_file:dir r_dir_perms;
allow atci_service camera_pipemgr_device:chr_file r_file_perms;
allow atci_service mtk_hal_camera:binder call;
allow atci_service debugfs_ion:dir search;
allow atci_service sysfs_tpd_setting:file rw_file_perms;
allow atci_service sysfs_vibrator_setting:file rw_file_perms;
allow atci_service sysfs_leds_setting:file rw_file_perms;
allow atci_service vendor_toolbox_exec:file rx_file_perms;

# Date : WK18.21
# Purpose: Allow to use HIDL
hal_client_domain(atci_service, hal_mtk_atci)

# Date : WK18.26
# Purpose: Allow gps socket sendto
allow atci_service mnld:unix_dgram_socket sendto;

# Date : WK18.35
# Purpose : allow CCT to allocate memory
hal_client_domain(atci_service, hal_allocator)

allow atci_service gpu_device:chr_file rw_file_perms;
