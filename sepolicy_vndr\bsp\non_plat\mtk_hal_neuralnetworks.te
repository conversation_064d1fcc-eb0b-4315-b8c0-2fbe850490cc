# ==============================================
# Common SEPolicy Rule
# =============================================

type mtk_hal_neuralnetworks_exec, exec_type, vendor_file_type, file_type;

init_daemon_domain(mtk_hal_neuralnetworks)

hal_server_domain(mtk_hal_neuralnetworks, hal_neuralnetworks)
allow mtk_hal_neuralnetworks ion_device:chr_file rw_file_perms;
allow mtk_hal_neuralnetworks debugfs_ion:dir r_dir_perms;
allow mtk_hal_neuralnetworks vpu_device:chr_file rw_file_perms;
allow mtk_hal_neuralnetworks mdla_device:chr_file rw_file_perms;
allow mtk_hal_neuralnetworks apusys_device:chr_file rw_file_perms;
allow mtk_hal_neuralnetworks gpu_device:chr_file rw_file_perms;
binder_call(mtk_hal_neuralnetworks, untrusted_app_25)
binder_call(mtk_hal_neuralnetworks, untrusted_app)
allow mtk_hal_neuralnetworks shell_data_file:file read;
allow mtk_hal_neuralnetworks sdcardfs:file r_file_perms;
allow mtk_hal_neuralnetworks fuse:file r_file_perms;
allow mtk_hal_neuralnetworks sysfs_lowmemorykiller:dir r_dir_perms;
allow mtk_hal_neuralnetworks sysfs_lowmemorykiller:file r_file_perms;
allow mtk_hal_neuralnetworks proc_zoneinfo:file r_file_perms;
allow mtk_hal_neuralnetworks apk_data_file:file read;
allow mtk_hal_neuralnetworks gpu_device:dir r_dir_perms;

# Date : WK14.40 2018/11/16
# Purpose : allow access to /data/vendor for blob cache for gpunn service
allow mtk_hal_neuralnetworks mnt_user_file:lnk_file r_file_perms;
allow mtk_hal_neuralnetworks mnt_user_file:dir search;
allow mtk_hal_neuralnetworks storage_file:lnk_file r_file_perms;
allow mtk_hal_neuralnetworks sdcardfs:dir search;
dontaudit mtk_hal_neuralnetworks media_rw_data_file:dir search;

# Date : WK19.03 2019/01/14
# Purpose : allow access to /data/vendor for blob cache for gpunn service
allow mtk_hal_neuralnetworks gpunn_data_file:dir create_dir_perms;
allow mtk_hal_neuralnetworks gpunn_data_file:file create_file_perms;

# Date : WK1919 2019/0513
# Purpose : allow access to perfmgr for EARA-QoS
allow mtk_hal_neuralnetworks proc_perfmgr:dir r_dir_perms;
allow mtk_hal_neuralnetworks proc_perfmgr:file r_file_perms;
allowxperm mtk_hal_neuralnetworks proc_perfmgr:file ioctl {
  PERFMGR_EARA_NN_BEGIN
  PERFMGR_EARA_NN_END
  PERFMGR_EARA_GETUSAGE
};

allow mtk_hal_neuralnetworks proc_ged:file rw_file_perms;
allowxperm mtk_hal_neuralnetworks proc_ged:file ioctl proc_ged_ioctls;

# Date : WK1946
# Purpose : allow access to /proc/[pid]/cmdline
typeattribute mtk_hal_neuralnetworks mlstrustedsubject;
allow mtk_hal_neuralnetworks untrusted_app_all:dir search;
allow mtk_hal_neuralnetworks untrusted_app_all:file r_file_perms;

# Date : WK2003
# Purpose : read chip id and segment code
allow mtk_hal_neuralnetworks sysfs_chipid:file r_file_perms;
allow mtk_hal_neuralnetworks proc_devinfo:file r_file_perms;

# Date : WK2016 2020/0415
# Purpose : Support AHardwareBuffer
hal_client_domain(mtk_hal_neuralnetworks, hal_graphics_allocator)

# Date : WK2023
# Purpose : Allow access to PowerHal for performance boost
hal_client_domain(mtk_hal_neuralnetworks, hal_power)

# Date : WK2038
# Purpose : Allow VtsHalNeuralnetworks search fd
allow mtk_hal_neuralnetworks shell:dir search;

# Date : WK2127
# Purpose : Allow Debug Plus read/write report
allow mtk_hal_neuralnetworks data_vendor_nn_file:dir create_dir_perms;
allow mtk_hal_neuralnetworks data_vendor_nn_file:file create_file_perms;

# Date : WK2127
# Purpose : Allow HMP custom api request
allow mtk_hal_neuralnetworks data_vendor_hmp_file:dir { rw_dir_perms };
allow mtk_hal_neuralnetworks data_vendor_hmp_file:file { create_file_perms };

# Date : WK2128
# Purpose: Allow Neuron Hidl set property
set_prop(mtk_hal_neuralnetworks, vendor_mtk_apuware_debug_prop)
