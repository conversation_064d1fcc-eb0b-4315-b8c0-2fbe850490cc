# Camera
/data/vendor/camera_update(/.*)?                                                                        u:object_r:vendor_camera_update_data_file:s0
/mnt/vendor/persist/camera(/.*)?                                                                        u:object_r:persist_camera_file:s0
/(odm|vendor/odm)/bin/hw/vendor\.oplus\.hardware\.engcamera@1\.0-service                                u:object_r:mtk_hal_camera_exec:s0
/(odm|vendor/odm)/bin/hw/vendor\.oplus\.hardware\.cammidasservice@1\.0-service                          u:object_r:mtk_hal_camera_exec:s0
/(vendor|odm)/lib(64)?/android\.hardware\.graphics\.allocator@2\.0\.so                                  u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/android\.hardware\.graphics\.allocator@3\.0\.so                                  u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/android\.hardware\.graphics\.allocator@4\.0\.so                                  u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/android\.hardware\.graphics\.common-V2-ndk_platform\.so                          u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/android\.hardware\.graphics\.common-V2-ndk\.so                                   u:object_r:same_process_hal_file:s0

# Charging
/dev/oplus_chg                                                                                          u:object_r:oplus_charger_device:s0

# Display
/dev/oplus_display                                                                                      u:object_r:oplus_display_device:s0
/sys/devices/platform/disp-leds/leds/lcd-backlight(/.*)?   u:object_r:sysfs_leds:s0

# Vibrator
/vendor/bin/hw/vendor\.qti\.hardware\.vibrator\.service                               u:object_r:hal_vibrator_default_exec:s0

# Nfc
/dev/nq-nci                                                                                             u:object_r:nfc_device:s0

# Sensors
/(vendor|system/vendor)/bin/hw/android\.hardware\.sensors@2\.0-service-multihal\.MT6893                 u:object_r:hal_sensors_default_exec:s0
/(odm|vendor/odm)/bin/hw/vendor\.oplus\.hardware\.oplusSensor@1\.0-service                              u:object_r:hal_sensors_default_exec:s0
/dev/m_virtual_sensor_misc                                                                              u:object_r:virtual_sensor_device:s0
/dev/block/sdc4                                                                                         u:object_r:oplus_block_device:s0
/dev/block/11270000.ufshci/by-name/oplusreserve*                                                        u:object_r:oplus_block_device:s0
/dev/block/soc/11270000.ufshci/by-name/oplusreserve*                                                    u:object_r:oplus_block_device:s0
/dev/block/platform/11270000.ufshci/by-name/reserve.*                                                   u:object_r:oplus_block_device:s0
/dev/block/platform/11270000.ufshci/by-name/oplusreserve.*                                              u:object_r:oplus_block_device:s0
/dev/block/platform/11270000.ufshci/by-name/oplus_custom                                                u:object_r:oplus_block_device:s0
/dev/block/by-name/oplusreserve.*                                                                       u:object_r:oplus_block_device:s0
/dev/block/platform/soc/11270000.ufshci/by-name/reserve.*                                               u:object_r:oplus_block_device:s0
/dev/block/platform/soc/11270000.ufshci/by-name/oplusreserve.*                                          u:object_r:oplus_block_device:s0
/dev/block/platform/soc/11270000.ufshci/by-name/oplus_custom                                            u:object_r:oplus_block_device:s0
/dev/block/by-name/cdt_engineering(_[ab])?                                                              u:object_r:oplus_block_device:s0
/dev/block/bootdevice/by-name/oplusreserve*                                                             u:object_r:oplus_block_device:s0
/dev/block/platform/bootdevice/by-name/reserve.*                                                        u:object_r:oplus_block_device:s0
/dev/block/platform/bootdevice/by-name/oplusreserve.*                                                   u:object_r:oplus_block_device:s0
/dev/block/sdc3                                                                                         u:object_r:oplus_block_device:s0
/dev/block/sdb3                                                                                         u:object_r:oplus_block_device:s0
/dev/block/platform/bootdevice/by-name/oplus_custom                                                     u:object_r:oplus_block_device:s0
/dev/block/sdc7                                                                                         u:object_r:oplus_block_device:s0
/dev/block/sdc16                                                                                        u:object_r:oplus_block_device:s0
/mnt/vendor/persist/engineermode(/.*)?                                                                  u:object_r:persist_engineer_file:s0
/storage/persist/engineermode(/.*)?                                                                     u:object_r:persist_engineer_file:s0

# Fingerprint
/(odm|vendor/odm)/bin/hw/vendor\.oplus\.hardware\.biometrics\.fingerprint@2\.1-service                  u:object_r:hal_fingerprint_default_exec:s0
/(vendor|system/vendor)/bin/hw/android\.hardware\.biometrics\.fingerprint@2\.3-service\.MT6893           u:object_r:hal_fingerprint_default_exec:s0
/mnt/vendor/persist/fingerprint/silead(/.*)?                                                            u:object_r:oplus_fingerprint_file:s0
/mnt/vendor/persist/fingerprint/jiiov(/.*)?	                                                        u:object_r:factorytestreport_vendor_data_file:s0
/data/vendor/optical_fingerprint(/.*)?                                                                  u:object_r:oplus_fingerprint_file:s0
/mnt/vendor/persist/fingerprint(/.*)?                                                                   u:object_r:oplus_fingerprint_file:s0
/data/vendor/fingerprint(/.*)?                                                                          u:object_r:oplus_fingerprint_file:s0
/data/vendor/silead(/.*)?                                                                               u:object_r:oplus_fingerprint_file:s0
/data/gf_data(/.*)?		                                                                        u:object_r:fingerprintd_data_file:s0

# Latency
/dev/cpu_dma_latency                                                                                    u:object_r:latency_device:s0

# Firmwares
/odm/vendor/firmware(/.*)?                                                                              u:object_r:vendor_firmware_file:s0
/odm/firmware(/.*)?                                                                                     u:object_r:vendor_firmware_file:s0

# ORMS Hal
/(vendor|odm)/bin/hw/vendor\.oplus\.hardware\.orms\.ormsHalService@1\.0-service                         u:object_r:oplus_hal_ormsHal_exec:s0

# Performance
/(odm|vendor/odm)/bin/hw/vendor\.oplus\.hardware\.performance@1\.0-service                              u:object_r:hal_performance_oplus_exec:s0

# Touch
/(vendor|system/vendor)/bin/hw/vendor\.lineage\.touch@[0-9]\.[0-9]-service\.oplus                       u:object_r:hal_lineage_touch_default_exec:s0

# Perfmgr
/(vendor|system/vendor)/bin/hw/android\.hardware\.power-service\.pixel-libperfmgr                       u:object_r:hal_power_default_exec:s0
