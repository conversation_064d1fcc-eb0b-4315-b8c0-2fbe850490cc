# ==============================================
# Common SEPolicy Rule
# ==============================================

##########################
# Filesystem types
#
##########################
# Proc Filesystem types
#
# For DuraSpeed
type proc_cpu_loading, fs_type, proc_type;
type proc_low_memory_hit, fs_type, proc_type;

# TKCore proc file
type proc_tkcore, fs_type, proc_type;

# For CachedAppOptimizer
type proc_freqhopping, fs_type, proc_type;

##########################
# Sys Filesystem types
#
# TEEI data file
type teei_control_file, fs_type, sysfs_type;

##########################
# File types
#
# mobicore device type and file type
type mobicore_vendor_file, file_type, vendor_file_type;

type tkcore_systa_file, file_type, vendor_file_type;

type wo_starter_exec, exec_type, file_type, vendor_file_type;
type wo_charon_exec, exec_type, file_type, vendor_file_type;
type wo_stroke_exec, exec_type, file_type, vendor_file_type;

##########################
# File types
# Data file types
#
# TEEI data file
type teei_data_file, file_type, data_file_type;

# Date:WK1822
# Purpose: Store ims config data
type mtk_radio_data_file, file_type, data_file_type;

# mobicore file type
type mobicore_data_file, file_type, data_file_type;

# DOE
type doe_vendor_data_file, file_type, data_file_type;

# md monitor file
type md_monitor_vendor_file, file_type, data_file_type;

# MTK omadm data folder
type omadm_data_file, file_type, data_file_type;
type omadm_misc_file, file_type, data_file_type;

# camera_dump
type vendor_camera_dump_file, file_type, data_file_type;

type tkcore_spta_file, file_type, data_file_type, mlstrustedobject;
type tkcore_data_file, file_type, data_file_type, mlstrustedobject;

type tkcore_protect_data_file, file_type, data_file_type;
type tkcore_log_file, file_type, data_file_type;

# Date : W1949
# Purpose: for thp data file
type mtk_thp_data_file, file_type, data_file_type;

# for img gpu_nn_service
type gpunn_data_file, file_type, data_file_type;

# Date : 2021/06/30
# Purpose: add permission for /data/vendor/nn/
type data_vendor_nn_file, data_file_type, file_type;

# Date : 2021/07/01
# Purpose: add permission for /data/vendor/hmp/
type data_vendor_hmp_file, data_file_type, file_type;

##########################
# File types
# Core domain data file types
#
# android log too much data/misc/log
type logmuch_data_file, file_type, data_file_type, core_data_file_type;

# For DuraSpeed
type duraspeed_data_file, file_type, data_file_type, core_data_file_type;

##########################
# Socket types
#
type statusd_socket, file_type;

# Date : WK17.30
# Purpose : wifi offload daemon access files and sockets
type wo_epdg_ipsec_socket, file_type;

# Date : WK17.48
# Purpose: RCS stack for 2/3G network
type rcs_volte_stack_socket, file_type;

# Date : WK1929
# Purpose: Rcs Volte stack submarine development
type rcs_rild_socket, file_type;

# vtservice_hidl for imsvt socket
type volte_imsvt1_socket, file_type;

type rcs_ua_proxy_socket, file_type;

# For chager enable fast charging algorithm
type sysfs_fs_chg_file, fs_type, sysfs_type;

# For InputReader scan and search power_supply path
type sysfs_power_supply, fs_type, sysfs_type;
