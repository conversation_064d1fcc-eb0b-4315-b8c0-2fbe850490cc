# ==============================================
# Common SEPolicy Rule
# ==============================================
##########################
# proc files
#
# Purpose : Camera need read cl_cam_status
# Package: com.mediatek.camera
genfscon proc /driver/cl_cam_status u:object_r:proc_cl_cam_status:s0

# Date : 2020/03/25
# Operation: R migration
# Purpose: mtk EM battery log
genfscon proc /mtk_battery_cmd u:object_r:proc_battery_cmd:s0

# Date : 2020/03/25
# Operation: R migration
# mtk EM flash reading
genfscon proc /partitions u:object_r:proc_partition:s0

##########################
# sysfs files
#
# label for SN process
genfscon sysfs /class/android_usb/android0 u:object_r:sysfs_android0_usb:s0

genfscon sysfs /devices/platform/mt_usb/cmode u:object_r:sysfs_usb_plat:s0
genfscon sysfs /class/udc/musb-hdrc/device/cmode                u:object_r:sysfs_usb_plat:s0

# Date : 2020/03/25
# Operation: R migration
# Purpose: for engineermode Usb PHY Tuning
genfscon sysfs /devices/platform/soc/usb-phy0 u:object_r:sysfs_usb_plat:s0

# Purpose: Allow EM USB/UART switch
genfscon sysfs /devices/platform/mt_usb/portmode                       u:object_r:sysfs_usb_plat:s0
genfscon sysfs /devices/platform/musb-mtu3d/musb-hdrc/portmode         u:object_r:sysfs_usb_plat:s0
genfscon sysfs /bus/platform/devices/musb-hdrc/portmode                u:object_r:sysfs_usb_plat:s0
genfscon sysfs /class/udc/musb-hdrc/device/portmode                    u:object_r:sysfs_usb_plat:s0
genfscon sysfs /devices/platform/11201000.mtu3_0/portmode              u:object_r:sysfs_usb_plat:s0

# for wifi throttle
genfscon sysfs /devices/platform/CONNAC/net/wlan0/operstate u:object_r:sysfs_thermald:s0
genfscon sysfs /devices/virtual/net/ap0/operstate           u:object_r:sysfs_thermald:s0
genfscon sysfs /devices/virtual/net/p2p0/operstate          u:object_r:sysfs_thermald:s0

# label for battery warning
genfscon sysfs /devices/platform/charger/BatteryNotify    u:object_r:sysfs_battery_warning:s0
genfscon sysfs /devices/platform/mt-battery/BatteryNotify u:object_r:sysfs_battery_warning:s0

# Date : 2020/03/25
# Operation: R migration
# Purpose: mtk EM battery temprature settings
genfscon sysfs /devices/platform/battery/Battery_Temperature u:object_r:sysfs_battery_temp:s0

# Date: 2020/03/25
# Operation: R migration
# Purpose: EM power ChargeBattery
genfscon sysfs /devices/platform/battery/FG_Battery_CurrentConsumption u:object_r:sysfs_battery_consumption:s0
genfscon sysfs /devices/platform/battery/Power_On_Voltage              u:object_r:sysfs_power_on_vol:s0
genfscon sysfs /devices/platform/battery/Power_Off_Voltage             u:object_r:sysfs_power_off_vol:s0
genfscon sysfs /devices/platform/battery/FG_daemon_disable             u:object_r:sysfs_fg_disable:s0
genfscon sysfs /devices/platform/battery/disable_nafg                  u:object_r:sysfs_dis_nafg:s0
# 6762/6765/6789
genfscon sysfs /devices/platform/1000d000.pwrap/1000d000.pwrap:main_pmic/mt6357-gauge/Battery_Temperature           u:object_r:sysfs_battery_temp:s0
genfscon sysfs /devices/platform/1000d000.pwrap/1000d000.pwrap:main_pmic/mt6357-gauge/FG_Battery_CurrentConsumption u:object_r:sysfs_battery_consumption:s0
genfscon sysfs /devices/platform/1000d000.pwrap/1000d000.pwrap:main_pmic/mt6357-gauge/Power_On_Voltage              u:object_r:sysfs_power_on_vol:s0
genfscon sysfs /devices/platform/1000d000.pwrap/1000d000.pwrap:main_pmic/mt6357-gauge/Power_Off_Voltage             u:object_r:sysfs_power_off_vol:s0
genfscon sysfs /devices/platform/1000d000.pwrap/1000d000.pwrap:main_pmic/mt6357-gauge/FG_daemon_disable             u:object_r:sysfs_fg_disable:s0
genfscon sysfs /devices/platform/1000d000.pwrap/1000d000.pwrap:main_pmic/mt6357-gauge/disable_nafg                  u:object_r:sysfs_dis_nafg:s0
# 6779
genfscon sysfs /devices/platform/soc/1000d000.pwrap/1000d000.pwrap:main_pmic/mt6359-gauge/Battery_Temperature           u:object_r:sysfs_battery_temp:s0
genfscon sysfs /devices/platform/soc/1000d000.pwrap/1000d000.pwrap:main_pmic/mt6359-gauge/FG_Battery_CurrentConsumption u:object_r:sysfs_battery_consumption:s0
genfscon sysfs /devices/platform/soc/1000d000.pwrap/1000d000.pwrap:main_pmic/mt6359-gauge/Power_On_Voltage              u:object_r:sysfs_power_on_vol:s0
genfscon sysfs /devices/platform/soc/1000d000.pwrap/1000d000.pwrap:main_pmic/mt6359-gauge/Power_Off_Voltage             u:object_r:sysfs_power_off_vol:s0
genfscon sysfs /devices/platform/soc/1000d000.pwrap/1000d000.pwrap:main_pmic/mt6359-gauge/FG_daemon_disable             u:object_r:sysfs_fg_disable:s0
genfscon sysfs /devices/platform/soc/1000d000.pwrap/1000d000.pwrap:main_pmic/mt6359-gauge/disable_nafg                  u:object_r:sysfs_dis_nafg:s0
# 6873/6893
genfscon sysfs /devices/platform/10026000.pwrap/10026000.pwrap:mt6359p/mt6359p-gauge/Battery_Temperature           u:object_r:sysfs_battery_temp:s0
genfscon sysfs /devices/platform/10026000.pwrap/10026000.pwrap:mt6359p/mt6359p-gauge/FG_Battery_CurrentConsumption u:object_r:sysfs_battery_consumption:s0
genfscon sysfs /devices/platform/10026000.pwrap/10026000.pwrap:mt6359p/mt6359p-gauge/Power_On_Voltage              u:object_r:sysfs_power_on_vol:s0
genfscon sysfs /devices/platform/10026000.pwrap/10026000.pwrap:mt6359p/mt6359p-gauge/Power_Off_Voltage             u:object_r:sysfs_power_off_vol:s0
genfscon sysfs /devices/platform/10026000.pwrap/10026000.pwrap:mt6359p/mt6359p-gauge/FG_daemon_disable             u:object_r:sysfs_fg_disable:s0
genfscon sysfs /devices/platform/10026000.pwrap/10026000.pwrap:mt6359p/mt6359p-gauge/disable_nafg                  u:object_r:sysfs_dis_nafg:s0
# Date: 2021/08/16
# Purpose: 6983
genfscon sysfs /devices/platform/11e01000.i2c/i2c-5/5-0034/11e01000.i2c:mt6375@34:mtk_gauge/Battery_Temperature           u:object_r:sysfs_battery_temp:s0
genfscon sysfs /devices/platform/11e01000.i2c/i2c-5/5-0034/11e01000.i2c:mt6375@34:mtk_gauge/FG_Battery_CurrentConsumption u:object_r:sysfs_battery_consumption:s0
genfscon sysfs /devices/platform/11e01000.i2c/i2c-5/5-0034/11e01000.i2c:mt6375@34:mtk_gauge/Power_On_Voltage              u:object_r:sysfs_power_on_vol:s0
genfscon sysfs /devices/platform/11e01000.i2c/i2c-5/5-0034/11e01000.i2c:mt6375@34:mtk_gauge/Power_Off_Voltage             u:object_r:sysfs_power_off_vol:s0
genfscon sysfs /devices/platform/11e01000.i2c/i2c-5/5-0034/11e01000.i2c:mt6375@34:mtk_gauge/FG_daemon_disable             u:object_r:sysfs_fg_disable:s0
genfscon sysfs /devices/platform/11e01000.i2c/i2c-5/5-0034/11e01000.i2c:mt6375@34:mtk_gauge/disable_nafg                  u:object_r:sysfs_dis_nafg:s0
# Purpose:6879
genfscon sysfs /devices/platform/11ed1000.i2c/i2c-5/5-0034/11ed1000.i2c:mt6375@34:mtk_gauge/Battery_Temperature           u:object_r:sysfs_battery_temp:s0
genfscon sysfs /devices/platform/11ed1000.i2c/i2c-5/5-0034/11ed1000.i2c:mt6375@34:mtk_gauge/FG_Battery_CurrentConsumption u:object_r:sysfs_battery_consumption:s0
genfscon sysfs /devices/platform/11ed1000.i2c/i2c-5/5-0034/11ed1000.i2c:mt6375@34:mtk_gauge/Power_On_Voltage              u:object_r:sysfs_power_on_vol:s0
genfscon sysfs /devices/platform/11ed1000.i2c/i2c-5/5-0034/11ed1000.i2c:mt6375@34:mtk_gauge/Power_Off_Voltage             u:object_r:sysfs_power_off_vol:s0
genfscon sysfs /devices/platform/11ed1000.i2c/i2c-5/5-0034/11ed1000.i2c:mt6375@34:mtk_gauge/FG_daemon_disable             u:object_r:sysfs_fg_disable:s0
genfscon sysfs /devices/platform/11ed1000.i2c/i2c-5/5-0034/11ed1000.i2c:mt6375@34:mtk_gauge/disable_nafg                  u:object_r:sysfs_dis_nafg:s0
# Date: 2021/10/19
# Purpose: 6895
genfscon sysfs /devices/platform/soc/11280000.i2c/i2c-5/5-0034/11280000.i2c:mt6375@34:mtk_gauge/Battery_Temperature           u:object_r:sysfs_battery_temp:s0
genfscon sysfs /devices/platform/soc/11280000.i2c/i2c-5/5-0034/11280000.i2c:mt6375@34:mtk_gauge/FG_Battery_CurrentConsumption u:object_r:sysfs_battery_consumption:s0
genfscon sysfs /devices/platform/soc/11280000.i2c/i2c-5/5-0034/11280000.i2c:mt6375@34:mtk_gauge/Power_On_Voltage              u:object_r:sysfs_power_on_vol:s0
genfscon sysfs /devices/platform/soc/11280000.i2c/i2c-5/5-0034/11280000.i2c:mt6375@34:mtk_gauge/Power_Off_Voltage             u:object_r:sysfs_power_off_vol:s0
genfscon sysfs /devices/platform/soc/11280000.i2c/i2c-5/5-0034/11280000.i2c:mt6375@34:mtk_gauge/FG_daemon_disable             u:object_r:sysfs_fg_disable:s0
genfscon sysfs /devices/platform/soc/11280000.i2c/i2c-5/5-0034/11280000.i2c:mt6375@34:mtk_gauge/disable_nafg                  u:object_r:sysfs_dis_nafg:s0
# Date: 2021/12/25
# Purpose: 6855
genfscon sysfs /devices/platform/11b20000.i2c/i2c-5/5-0034/11b20000.i2c:mt6375@34:mtk_gauge/power_supply/Battery_Temperature           u:object_r:sysfs_battery_temp:s0
genfscon sysfs /devices/platform/11b20000.i2c/i2c-5/5-0034/11b20000.i2c:mt6375@34:mtk_gauge/power_supply/FG_Battery_CurrentConsumption u:object_r:sysfs_battery_consumption:s0
genfscon sysfs /devices/platform/11b20000.i2c/i2c-5/5-0034/11b20000.i2c:mt6375@34:mtk_gauge/power_supply/Power_On_Voltage              u:object_r:sysfs_power_on_vol:s0
genfscon sysfs /devices/platform/11b20000.i2c/i2c-5/5-0034/11b20000.i2c:mt6375@34:mtk_gauge/power_supply/Power_Off_Voltage             u:object_r:sysfs_power_off_vol:s0
genfscon sysfs /devices/platform/11b20000.i2c/i2c-5/5-0034/11b20000.i2c:mt6375@34:mtk_gauge/power_supply/FG_daemon_disable             u:object_r:sysfs_fg_disable:s0
genfscon sysfs /devices/platform/11b20000.i2c/i2c-5/5-0034/11b20000.i2c:mt6375@34:mtk_gauge/power_supply/disable_nafg                  u:object_r:sysfs_dis_nafg:s0
# Purpose:6789 mt6366 em/battery
genfscon sysfs /devices/platform/soc/10026000.pwrap/10026000.pwrap:mt6366/mt6358-gauge/Battery_Temperature           u:object_r:sysfs_battery_temp:s0
genfscon sysfs /devices/platform/soc/10026000.pwrap/10026000.pwrap:mt6366/mt6358-gauge/FG_Battery_CurrentConsumption u:object_r:sysfs_battery_consumption:s0
genfscon sysfs /devices/platform/soc/10026000.pwrap/10026000.pwrap:mt6366/mt6358-gauge/Power_On_Voltage              u:object_r:sysfs_power_on_vol:s0
genfscon sysfs /devices/platform/soc/10026000.pwrap/10026000.pwrap:mt6366/mt6358-gauge/Power_Off_Voltage             u:object_r:sysfs_power_off_vol:s0
genfscon sysfs /devices/platform/soc/10026000.pwrap/10026000.pwrap:mt6366/mt6358-gauge/FG_daemon_disable             u:object_r:sysfs_fg_disable:s0
genfscon sysfs /devices/platform/soc/10026000.pwrap/10026000.pwrap:mt6366/mt6358-gauge/disable_nafg                  u:object_r:sysfs_dis_nafg:s0

genfscon sysfs /dev/gauge/Battery_Temperature           u:object_r:sysfs_battery_temp:s0
genfscon sysfs /dev/gauge/FG_Battery_CurrentConsumption u:object_r:sysfs_battery_consumption:s0
genfscon sysfs /dev/gauge/Power_On_Voltage              u:object_r:sysfs_power_on_vol:s0
genfscon sysfs /dev/gauge/Power_Off_Voltage             u:object_r:sysfs_power_off_vol:s0
genfscon sysfs /dev/gauge/FG_daemon_disable             u:object_r:sysfs_fg_disable:s0
genfscon sysfs /dev/gauge/disable_nafg                  u:object_r:sysfs_dis_nafg:s0

# Date : 2020/03/25
# Operation: R migration
# mtk EM pmic & pmu register
genfscon sysfs /devices/platform/mt-pmic                                                                            u:object_r:sysfs_pmu:s0
genfscon sysfs /devices/platform/1000d000.pwrap/mt-pmic                                                             u:object_r:sysfs_pmu:s0
genfscon sysfs /devices/platform/1000d000.pwrap/1000d000.pwrap:mt6358-pmic/mt-pmic                                  u:object_r:sysfs_pmu:s0
genfscon sysfs /devices/platform/1000d000.pwrap/1000d000.pwrap:mt6359-pmic/mt-pmic                                  u:object_r:sysfs_pmu:s0
genfscon sysfs /devices/platform/10026000.pwrap/10026000.pwrap:mt6359-pmic/mt-pmic                                  u:object_r:sysfs_pmu:s0
genfscon sysfs /devices/platform/10026000.pwrap/10026000.pwrap:mt6359p/mt-pmic                                      u:object_r:sysfs_pmu:s0
genfscon sysfs /devices/platform/1c015000.spmi/spmi-0/0-04/mt-pmic                                                  u:object_r:sysfs_pmu:s0
genfscon sysfs /devices/platform/1c804000.spmi/spmi-0/0-04/mt-pmic                                                  u:object_r:sysfs_pmu:s0
genfscon sysfs /devices/platform/mt6333-user                                                                        u:object_r:sysfs_pmu:s0
genfscon sysfs /devices/platform/mt6311-user                                                                        u:object_r:sysfs_pmu:s0
genfscon sysfs /devices/platform/10027000.spmi/spmi-0/0-03/10027000.spmi:mt6315@3:mt6315_3_regulator/extbuck_access u:object_r:sysfs_pmu:s0
genfscon sysfs /devices/platform/10027000.spmi/spmi-0/0-06/10027000.spmi:mt6315@6:mt6315_6_regulator/extbuck_access u:object_r:sysfs_pmu:s0
genfscon sysfs /devices/platform/10027000.spmi/spmi-0/0-07/10027000.spmi:mt6315@7:mt6315_7_regulator/extbuck_access u:object_r:sysfs_pmu:s0
genfscon sysfs /devices/platform/10027000.spmi/spmi-0/0-03/10027000.spmi:mt6315@3:extbuck_debug/extbuck_access      u:object_r:sysfs_pmu:s0
genfscon sysfs /devices/platform/10027000.spmi/spmi-0/0-06/10027000.spmi:mt6315@6:extbuck_debug/extbuck_access      u:object_r:sysfs_pmu:s0
genfscon sysfs /devices/platform/10027000.spmi/spmi-0/0-07/10027000.spmi:mt6315@7:extbuck_debug/extbuck_access      u:object_r:sysfs_pmu:s0
genfscon sysfs /devices/platform/soc/11cb1000.i2c/i2c-9/9-0034/extdev_io/MT6375.9-0034                              u:object_r:sysfs_pmu:s0
genfscon sysfs /devices/platform/11e01000.i2c/i2c-5/5-0034/extdev_io/MT6375.5-0034                                  u:object_r:sysfs_pmu:s0
genfscon sysfs /devices/platform/soc/11280000.i2c/i2c-5/5-0018/extdev_io/MT6375.5-0034                              u:object_r:sysfs_pmu:s0
genfscon sysfs /devices/platform/soc/11280000.i2c/i2c-5/5-0034/extdev_io/MT6375.5-0034                              u:object_r:sysfs_pmu:s0
# Purpose: 6855
genfscon sysfs /devices/platform/soc/1c804000.spmi/spmi-0/0-04/mt-pmic                                              u:object_r:sysfs_pmu:s0
genfscon sysfs /devices/platform/11b20000.i2c/i2c-5/5-0034/extdev_io/MT6375.5-0034                                  u:object_r:sysfs_pmu:s0
# 6789 mt6375 em/pmic
genfscon sysfs /devices/platform/soc/10026000.pwrap/10026000.pwrap:mt6366/mt-pmic                                   u:object_r:sysfs_pmu:s0
genfscon sysfs /devices/platform/soc/11017000.i2c/i2c-5/5-0034/extdev_io/MT6375.5-0034                              u:object_r:sysfs_pmu:s0

# Date : 2020/06/01
# Operation: R migration
# Purpose : Add permission for acess /sys/devices/platform/mhl@0/extcon/HDMI_audio_extcon/state.
genfscon sysfs /devices/platform/mhl@0/extcon/HDMI_audio_extcon/state u:object_r:sysfs_HDMI_audio_extcon_state:s0

# Date : 2018/11/01 -> 2020/06/23
# Operation: R migration
# Purpose : mtk EM c2k bypass read usb file
genfscon sysfs /devices/virtual/usb_rawbulk u:object_r:sys_usb_rawbulk:s0

# Date : 2020/12/16
# Operation: R migration
# Purpose: mtk meta wifi daemon access pmu register
genfscon sysfs /devices/platform/1000d000.pwrap/1000d000.pwrap:main_pmic/mt-pmic u:object_r:sysfs_pmu:s0
