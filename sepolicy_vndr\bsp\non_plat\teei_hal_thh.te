# ==============================================
# MICROTRUST SEPolicy Rule
# ==============================================
# Set exec file type
type teei_hal_thh_exec, exec_type, vendor_file_type, file_type;

# Setup for domain transition
init_daemon_domain(teei_hal_thh)

# Set teei_hal_thh as server domain of hal_teei_thh
hal_server_domain(teei_hal_thh, hal_teei_thh)

hal_client_domain(teei_hal_thh, hal_teei_capi)
hal_client_domain(teei_hal_thh, hal_allocator)

# Access thh devices at all.
allow teei_hal_thh teei_client_device:chr_file { create setattr unlink rw_file_perms };
allow teei_hal_thh teei_data_file:dir create_dir_perms;
allow teei_hal_thh teei_data_file:file create_file_perms;
set_prop(teei_hal_thh, vendor_mtk_soter_teei_prop)
