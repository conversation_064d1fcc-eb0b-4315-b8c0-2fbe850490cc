# ==============================================
# Policy File of /vendor/bin/mtk_hal_nvramagent Executable File

# ==============================================
# Type Declaration
# ==============================================
type mtk_hal_nvramagent_exec, exec_type, file_type, vendor_file_type;
type mtk_hal_nvramagent, domain;

# ==============================================
# Common SEPolicy Rule
# ==============================================
init_daemon_domain(mtk_hal_nvramagent)

# Date : WK14.43
# Operation : 2rd Selinux Migration
# Purpose : the role of mtk_hal_nvramagent is same with nvram_daemon except property_set & exect permission
allow mtk_hal_nvramagent nvram_device:blk_file rw_file_perms;
allow mtk_hal_nvramagent nvdata_device:blk_file rw_file_perms;
allow mtk_hal_nvramagent nvram_data_file:dir create_dir_perms;
allow mtk_hal_nvramagent nvram_data_file:file create_file_perms;
allow mtk_hal_nvramagent nvram_data_file:lnk_file r_file_perms;
allow mtk_hal_nvramagent nvdata_file:lnk_file r_file_perms;
allow mtk_hal_nvramagent nvdata_file:dir create_dir_perms;
allow mtk_hal_nvramagent nvdata_file:file create_file_perms;

allow mtk_hal_nvramagent als_ps_device:chr_file r_file_perms;
allow mtk_hal_nvramagent mtk-adc-cali_device:chr_file rw_file_perms;
allow mtk_hal_nvramagent gsensor_device:chr_file r_file_perms;
allow mtk_hal_nvramagent gyroscope_device:chr_file r_file_perms;
allow mtk_hal_nvramagent self:capability { fowner chown fsetid };

# Purpose: for backup
allow mtk_hal_nvramagent nvram_device:chr_file rw_file_perms;
allow mtk_hal_nvramagent pro_info_device:chr_file rw_file_perms;
allow mtk_hal_nvramagent block_device:dir search;

# for MLC device
allow mtk_hal_nvramagent mtd_device:dir search;
allow mtk_hal_nvramagent mtd_device:chr_file rw_file_perms;

# Allow a set of permissions required for a domain to be a server which provides a HAL implementation over HWBinder.
hal_server_domain(mtk_hal_nvramagent, hal_mtk_nvramagent)
read_fstab(mtk_hal_nvramagent)
get_prop(mtk_hal_nvramagent, vendor_mtk_rat_config_prop)

# Date : WK18.21
# Operation: P migration
# Purpose: Allow to search /mnt/vendor/nvdata when using nvram function
allow mtk_hal_nvramagent mnt_vendor_file:dir search;

allow mtk_hal_nvramagent sysfs_boot_mode:file r_file_perms;
