# ==============================================
# Type Declaration
# ==============================================

type aee_hal,domain;
type aee_hal_exec, exec_type, file_type, vendor_file_type;
typeattribute aee_hal mlstrustedsubject;

# ==============================================
# Common SEPolicy Rule
# ==============================================

init_daemon_domain(aee_hal)

hal_server_domain(aee_hal, hal_mtk_aee)

allow aee_hal aee_exp_vendor_file:dir w_dir_perms;
allow aee_hal aee_exp_vendor_file:file create_file_perms;
allow aee_hal aee_exp_data_file:file { read write };

set_prop(aee_hal, vendor_mtk_persist_mtk_aeev_prop)
set_prop(aee_hal, vendor_mtk_persist_aeev_prop)
set_prop(aee_hal, vendor_mtk_debug_mtk_aeev_prop)

binder_call(aee_hal, system_app);
