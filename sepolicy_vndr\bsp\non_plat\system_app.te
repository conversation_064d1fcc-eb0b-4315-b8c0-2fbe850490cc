# ==============================================
# Common SEPolicy Rule
# ==============================================

# Date : 2014/11/19
# Operation: SQC
# Purpose: [Settings][RenderThread][operate device file failed]
# Package: com.android.settings
allow system_app proc_secmem:file rw_file_perms;

# Date: 2014/08/01
# Operation: BaseUT
# Purpose: [Settings][Settings used list views need velocity tracker access touch dev]
# Package: com.android.settings
allow system_app touch_device:chr_file r_file_perms;

# Date: 2014/08/04
# Stage: BaseUT
# Purpose: [MTKThermalManager][View thermal zones and coolers, and change thermal policies]
# Package Name: com.mediatek.mtkthermalmanager
allow system_app apk_private_data_file:dir getattr;
allow system_app asec_image_file:dir getattr;
allow system_app dontpanic_data_file:dir getattr;
allow system_app drm_data_file:dir getattr;
allow system_app install_data_file:file getattr;
allow system_app lost_found_data_file:dir getattr;
allow system_app media_data_file:dir getattr;
allow system_app property_data_file:dir getattr;
allow system_app proc_thermal:dir search;
allow system_app proc_thermal:file rw_file_perms;
allow system_app proc_mtkcooler:dir search;
allow system_app proc_mtkcooler:file rw_file_perms;
allow system_app proc_mtktz:dir search;
allow system_app proc_mtktz:file rw_file_perms;
allow system_app proc_slogger:file rw_file_perms;

# Date : WK17.23
# Stage: Migration, SQC
# Purpose: Allow to use HAL PQ
hal_client_domain(system_app, hal_mtk_pq)

# Date : WK17.29
# Operation : Migration
# Purpose : for device bring up, not to block early SQC
allow system_app debugfs_ion:dir search;

# Date:W17.29
# Operation : presence hal developing
# Purpose : Allow to use HAL presence
hal_client_domain(system_app, hal_presence)

# Date : WK17.31
# Operation : Migration
# Purpose : Carrier express service on BSP
get_prop(system_app, vendor_mtk_volte_prop)
get_prop(system_app, vendor_mtk_wfc_prop)
get_prop(system_app, vendor_mtk_vt_prop)
get_prop(system_app, vendor_mtk_cxp_vendor_prop)

# Date:W17.31
# Operation : rcs hal developing
# Purpose : Allow to use HAL rcs
hal_client_domain(system_app, hal_rcs)

# Date : WK17.29
# Operation : SQC
# Purpose : allow SystemUpdate to access ota_package file
allow system_app ota_package_file:dir { create_dir_perms };
allow system_app ota_package_file:file { create_file_perms };

# Date : WK17.30
# Operation : SQC
# Purpose : allow SystemUpdate to access Update engine
allow system_app update_engine:binder { call transfer };

# Date : WK17.41
# Stage: Migration, IT
# Purpose: allow PermissionControl use mtk_hal_netdagent_hwservice
hal_client_domain(system_app, mtk_hal_netdagent)

# Date: WK17.41
# Operation: SQC
# Purpose: [sysoper][sysoper will create folder /cache/recovery]
# Package: com.mediatek.systemupdate.sysoper
allow system_app cache_file:dir { write search create add_name remove_name };
allow system_app cache_file:file { read write create open getattr unlink };

# Date: 2016/07/05
# Operation: SQC
# Purpose: Add permission to access recovery folder and write command files to recovery for System Update
allow system_app cache_recovery_file:dir { write search add_name remove_name };
allow system_app cache_recovery_file:file { read write create open getattr unlink };

# Date: 2018/05/08
# Operation: Migration
# Purpose : Allow Privacy protection lock to find ppl agent
# Package: com.mediatek.PrivacyProtectionLock
allow system_app mtk_hal_pplagent_hwservice:hwservice_manager find;
allow system_app ppl_agent:binder call;

# Date : WK18.25
# Stage: Migration
# Purpose: allow AtciService to access atcid
hal_client_domain(system_app, hal_mtk_atci)

# Date: 2018/07/30
# Purpose: Allow BackupRestore can read /dev/block/mmcblk1.
# Package Name: com.mediatek.backuprestore
allow system_app block_device:dir search;

# Date: W18.31
# Purpose: Allow system-app to get vendor_mtk_ss_vendor_prop
# Package Name: com.mediatek.engineermode
get_prop(system_app, vendor_mtk_ss_vendor_prop)

# Date: 2018/04/18
# Purpose: Allow to use HIDL and access mtk_hal_neuralnetworks
allow system_app mtk_hal_neuralnetworks:binder { call transfer };
allow system_app debugfs_ion:dir search;

# Date: 2018/10/31
# Operation: Support SubsidyLock
hal_client_domain(system_app, hal_telephony)
binder_call(system_app, rild)

# Date:W18.43
# Operation : clientapi hal developing
# Purpose : Allow to use HAL presence
hal_client_domain(system_app, hal_clientapi)

# Date : 2019/05/09
# Operation: TrustKernel integration
# Purpose: access for client device of TKCore
allow system_app tkcore_admin_device:chr_file rw_file_perms;

# Date: 2019/05/24
# Purpose: System APP can submit KPI to DMC through APM HIDL interface
# Package Name: com.mediatek.apmonitor
hal_client_domain(system_app, hal_mtk_apm)

# Date: 2019/05/24
# Purpose: System APP can check DMC proerpty to submit KPI or not.
# Package Name: com.mediatek.apmonitor
get_prop(system_app, vendor_mtk_dmc_prop)

# Date : 2019/06/27
# Operation : system app need to read vendor_mtk_cta_support_prop property
# Purpose : allow to get mtk_cta_support property
get_prop(system_app, vendor_mtk_cta_support_prop)

# Date : 2019/07/15
# Operation : it
# Purpose : for setting ims nr enable property
get_prop(system_app, vendor_mtk_vonr_prop)
get_prop(system_app, vendor_mtk_vinr_prop)

# Date : 2019/07/08
# Operation : New feature
# Purpose : VoW 2E2K request model update: system APP write and audio HAL read
# Package Name: com.mediatek.voicecommand
allow system_app mtk_audiohal_data_file:dir create_dir_perms;
allow system_app mtk_audiohal_data_file:file create_file_perms;


hal_client_domain(system_app, hal_fingerprint)
