# ==============================================
# Common SEPolicy Rule
# ==============================================

# Date: W18.32
# Operation : dontaudit writing to timerslack_ns
dontaudit system_server appdomain:file w_file_perms;
allow system_server ota_package_file:dir getattr;

# Purpose: receive dropbox message
allow system_server crash_dump:fifo_file w_file_perms;
allow system_server crash_dump:fd use;

# Property service.
set_prop(system_server, ctl_bootanim_prop)

# Date : WK16.36
# Purpose: Allow to set property log.tag.WifiHW to control log level of WifiHW
set_prop(system_server, log_tag_prop)

# Fix bootup violation
get_prop(system_server, wifi_prop)

#Date:2019/10/09
#Operation:Q Migration
get_prop(system_server, system_mtk_debug_bq_dump_prop)

#Date:2019/10/10
#Operation:Q Migration
allow system_server mddb_filter_data_file:dir getattr;

allow system_server netdiag:fd use;

#Date :2020/10/19
#Operation : Allow system server to kill dex2oat
allow system_server dex2oat:process sigkill;

#Date:2021/9/22
# Allow system server to get pgid
allow system_server rs:process getpgid;
allow system_server webview_zygote:process getpgid;
allow system_server netd:process setsched;
allow system_server keystore:process setsched;
allow system_server audioserver_tmpfs:file write;
#Date:2021/10/13
# neverallow system server to kill process
dontaudit system_server mediaserver:process sigkill;
dontaudit system_server netd:process sigkill;
dontaudit system_server keystore:process sigkill;

#support system server domain do ioctl
allow system_server system_mtk_pmb_file:file ioctl;
allowxperm system_server system_mtk_pmb_file:file ioctl FS_IOC_MEASURE_VERITY;
