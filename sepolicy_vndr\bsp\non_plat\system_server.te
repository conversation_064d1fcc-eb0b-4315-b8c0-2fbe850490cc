# ==============================================
# Common SEPolicy Rule
# ==============================================

# Date: WK14.43
# Operation : Migration
# Purpose : for bring up
allow system_server sf_rtt_file:dir { relabelto r_dir_perms };

# Date: WK14.47
# Operation : MTBF
# Purpose : for debug
allow system_server sf_rtt_file:file r_file_perms;

# Date: WK14.47
# Operation : Sanity
# Purpose : for /proc/secmem (TEE enable)
allow system_server proc_secmem:file rw_file_perms;

# Date: WK16.30
# Operation : Migration
# Purpose : for system_server operate /dev/RT_Monitor when enable hang detect
allow system_server RT_Monitor_device:chr_file r_file_perms;

# Date : WK15.24
# Operation: TEEI integration
# Purpose: access for fp device
allow system_server teei_fp_device:chr_file rw_file_perms;
allow system_server teei_client_device:chr_file r_file_perms;

# Date : 2016/07/11
# Operation : Migration
# Purpose : Add permission for gpu access
allow system_server dri_device:chr_file rw_file_perms;

# Date : W17.24
# Purpose: Allow to use HAL PQ
hal_client_domain(system_server, hal_mtk_pq)

# Date : W17.31
# Purpose: Allow to use Ape swip decoder
hal_client_domain(system_server, hal_mtk_codecservice)

# Date:W17.33
# Operation : camera hal developing
# Purpose : camera hal binder_call permission
binder_call(system_server, mtk_hal_camera)

# Date:W17.36
# Operation : Migration
# Purpose : Allow to send signal
allow system_server netd:process signal;

# Date:W17.07
# Operation : dfps hal
# Purpose : dfps hal interface permission
hal_client_domain(system_server, hal_dfps)

allow system_server audioserver:file w_file_perms;

# Date : 2018/03/06
# Purpose : Add mtk_hal_netdagent_hwservice for EM firewall usage
allow system_server mtk_hal_netdagent_hwservice:hwservice_manager find;
allow system_server netdagent:binder call;

# Date : W18.20
# Operation : Migration
# Purpose : for mobicore (Trustonic TEE)
allow system_server mobicore_vendor_file:dir r_file_perms;

# Date : 6/20/2018
# Operation : MTK fm hal migration
# Purpose : MTK fm hal interface permission
hal_client_domain(system_server, hal_mtk_fm)

# Date : W19.12
# Operation : For DuraSpeed Migration
allow system_server proc_cpu_loading:file rw_file_perms;
userdebug_or_eng(`
allow system_server debugfs_tracing_debug:file r_file_perms;
')
allow system_server proc_low_memory_hit:file rw_file_perms;
allow system_server duraspeed_data_file:dir create_dir_perms;
allow system_server duraspeed_data_file:file create_file_perms;

# Date : WK18.36
# Operation : omadm hidl
# Purpose : hidl interface permission
hal_client_domain(system_server, hal_mtk_omadm)

# Date : WK19.29
# Operation : nwk_opt hal
# Purpose : nwk_opt hal permission
hal_client_domain(system_server, hal_nwk_opt)

# Date:2020/08/07
# Operation:R Migration
userdebug_or_eng(` allow system_server md_monitor:process signal; ')

# Date:2020/08/26
# Operation:kill hal_drm_widevine permission when ANR happened
allow system_server hal_drm_widevine:process signal;

# Date:2020/09/03
# Operation:R Migration
allow system_server proc_ion:dir search;

# Date:2020/09/07
# Operation:R Migration
allow system_server proc_m4u_dbg:dir search;

# Date:2020/09/08
# Operation:R Migration
allow system_server proc_displowpower:dir search;
allow system_server proc_freqhopping:file getattr;

# Date:2020/09/11
# Operation:R Migration
allow system_server proc_freqhopping:dir search;

# Date:2020/09/18
# Operation:R Migration
allow system_server procfs_gpu_img:dir { search getattr };

# Date:2020/09/30
# Operation:R Migration
allow system_server procfs_gpu_img:file getattr;

# Read/Write /proc/pressure/cpu
allow system_server proc_pressure_cpu:file rw_file_perms;

# Search /proc/usb/plat
allow system_server proc_usb_plat:dir search;

# Search /proc/gpufreqv2
allow system_server proc_gpufreqv2:dir search;

# Search /proc/mtkfb
allow system_server proc_mtkfb:dir search;

# Search /proc/stat
allow system_server proc_stat:dir search;

# Date: 2021/08/10
# Operation: S Migration
# Purpose: InputReader read files under power_supply to detect battery device
allow system_server sysfs_power_supply:dir {r_dir_perms};
allow system_server sysfs_power_supply:file r_file_perms;
