# ==============================================
# Common SEPolicy Rule
# ==============================================

# For Rild hidl connection
allow rild system_app:binder call;

# Date: 2018/10/31
# Operation: Support SubsidyLock
# For Rild hidl connection
allow rild em_app:binder call;

allow rild statusd:unix_stream_socket connectto;
allow rild rild_via_socket:sock_file write;
allow rild viarild:unix_stream_socket connectto;
set_prop(rild, vendor_mtk_cdma_prop)
set_prop(rild, vendor_mtk_ril_cdma_report_prop)
allow rild rild_vsim_socket:sock_file write;

# Allow the find/call of netdagent for rilproxy
allow rild mtk_hal_netdagent_hwservice:hwservice_manager find;
allow rild netdagent:binder call;

#Dat: 2017/02/14
#Purpose: allow set telephony Sensitive property
set_prop(rild, vendor_mtk_telephony_sensitive_prop)

# Date : WK18.26
# Operation: P migration
# Purpose: Allow rild to set ims support property
set_prop(rild, vendor_mtk_volte_support_prop)
set_prop(rild, vendor_mtk_wfc_support_prop)
set_prop(rild, vendor_mtk_vilte_support_prop)
set_prop(rild, vendor_mtk_viwifi_support_prop)
set_prop(rild, vendor_mtk_rcs_ua_support_prop)

# Date : WK18.29
# Operation: Ims config TelephonyWare dev
# Purpose: Allow rild to get/set vendor_mtk_provision_prop
set_prop(rild, vendor_mtk_provision_prop)

# Date : WK18.33
# Operation: IT
# Purpose: Allow rild to set ims enable property
set_prop(rild, vendor_mtk_volte_prop)
set_prop(rild, vendor_mtk_wfc_prop)
set_prop(rild, vendor_mtk_vilte_prop)
set_prop(rild, vendor_mtk_viwifi_prop)
set_prop(rild, vendor_mtk_vt_prop)

# Date : WK19.29
# Operation: IT
# Purpose: Allow rild to set ims nr enable property
set_prop(rild, vendor_mtk_vonr_prop)
set_prop(rild, vendor_mtk_vinr_prop)

# Date : 2020/10/30
# Operation: IMS Config NR force value
# Purpose: Allow rild to force set IMS NR feature
set_prop(rild, vendor_mtk_vonr_force_prop)
