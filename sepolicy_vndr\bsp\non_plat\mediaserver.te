# ==============================================
# Common SEPolicy Rule
# ==============================================

# Date : WK14.52
# Operation : WVL1 IT
# Purpose : SVP module operates secmem driver
allow mediaserver mobicore_data_file:file getattr;
allow mediaserver mobicore_data_file:file getattr;

allow mediaserver mobicore_data_file:file { getattr read};
allow mediaserver mobicore_user_device:chr_file { read write open ioctl};

# Date: WK14.45
# Operation : Migration
# Purpose : HDCP
allow mediaserver persist_data_file:file { read write getattr };

# Date : WK15.03
# Operation : Migration
# Purpose : offloadservice
allow mediaserver offloadservice_device:chr_file { read write ioctl open };

# Data : WK14.38
# Operation : Migration
# Purpose : WFD
allow mediaserver surfaceflinger:dir search;
allow mediaserver surfaceflinger:file { read open };

# Date : WK14.49
# Operation : WFD
# Purpose : WFD notifies its status to thermal module
allow mediaserver proc_thermal:file { write getattr open };
allow mediaserver proc_mtkcooler:file { read write open };
allow mediaserver proc_mtktz:file { read write open };
allow mediaserver proc_thermal:file { read write open };

# Date : WK15.44
# Operation : Migration
# Purpose : ancservice
allow mediaserver ancservice_device:chr_file { read write ioctl open };

# Date : WK16.29
# Operation : Migration
# Purpose : Add permission for gpu access
allow mediaserver dri_device:chr_file { read write open ioctl };

# Date : WK17.23
# Stage: O Migration, SQC
# Purpose: Allow to use HAL PQ
hal_client_domain(mediaserver, hal_mtk_pq)

# Date : WK17.23
# Stage: O Migration, SQC
# Purpose: Allow to use shared memory for HAL PQ
hal_client_domain(mediaserver, hal_allocator)

# Date : WK17.31
# Stage: O Migration, SQC
# Purpose: Allow to use ape decoder
hal_client_domain(mediaserver, hal_mtk_codecservice)

# Date : WK17.31
# Operation : ViLTE
# Purpose : for ViLTE - set VTservice has permission to access me
allow mediaserver vtservice:binder { transfer call };
allow mediaserver vtservice:fd use;

# Date : WK17.43
# Operation : OMA DRM
# Purpose : Allow mediaserver to read processname to pass OMA DRM permisson check
allow mediaserver platform_app:dir search;
allow mediaserver platform_app:file { read open };

# Date : WK17.47
# Operation : SQC
# Purpose : Allow mediaserver to read processname of DeskClock to pass OMA DRM permisson check
allow mediaserver mediaprovider:dir search;
allow mediaserver platform_app:file getattr;
allow mediaserver system_app:dir search;
allow mediaserver system_app:file read;
allow mediaserver system_app:file open;

# Date : WK17.49
# Operation : VOW
# Purpose: Allow read and getattr path="/data/data/com.mediatek.voicecommand/training
# /anyone/passwordfile/0.dat"
allow mediaserver system_app_data_file:file { read getattr };

# Date : WK19.16
# Operation : WFD
# Purpose: Allow ioctl
allowxperm mediaserver proc_perfmgr:file ioctl {
  PERFMGR_FPSGO_QUEUE
  PERFMGR_FPSGO_DEQUEUE
};

# Date : WK19.43
# Operation : HDCP
# Purpose : Allow to connect HDCP HIDL server
hal_client_domain(mediaserver, hal_tesiai_hdcp)

# Date : WK21.37
# Operation : HDCP
# Purpose : Allow HDCP to access wv dev to get handle
allow mediaserver widevine_drv_device:chr_file rw_file_perms_no_map;
