# ==============================================
# Common SEPolicy Rule
# ============

userdebug_or_eng(`
# Date : WK19.07 2019/06/13
# Operation : mdi_redirector integration test with AT&T Linkmaster
# Purpose : Allow shell to listen MAPI payload from network socket
allow shell mdi_redirector:unix_stream_socket { connectto };

# Date : WK19.07 2019/06/13
# Operation : mdi_redirector integration test with AT&T Linkmaster
# Purpose : Allow shell to execute mdi_redirector_ctrl to start md_monitor & mdi_redirector daemons
set_prop(shell, ctl_start_prop)
set_prop(shell, ctl_stop_prop)

# Date : 2019/07/15
# Operation: support heavy loading
# Purpose: Allow shell to write/read the property
set_prop(shell, system_mtk_heavy_loading_prop)

# Date : WK1925
# Operation : MDMI Android Q migration
# Purpose : Allow shell to listen MDMI payload from network socket
allow shell mdmi_redirector:unix_stream_socket { connectto };

# Date : WK20.14 2020/03/31
# Operation : adb shell to read netlfix property for test
# Purpose : Allow shell to read netlfix property
get_prop(shell, netflix_bsp_rev_prop)

# Date : 2020/04/14
# Purpose: Allow adb shell to get/set USB tethering system property for auto test
set_prop(shell, system_mtk_usb_tethering_prop)

')
