# ==============================================
# Common SEPolicy Rule
# ==============================================

# Date : WK15.15
# Operation : Migration
# Purpose : set specific label for used raw partitions, for dumchar cases.
allow init system_block_device:blk_file relabelfrom;
allow init nvram_device:blk_file relabelto;
allow init nvdata_device:blk_file relabelto;
allow init nvcfg_block_device:blk_file relabelto;
allow init expdb_block_device:blk_file relabelto;
allow init misc2_block_device:blk_file relabelto;
allow init logo_block_device:blk_file relabelto;
allow init para_block_device:blk_file relabelto;
allow init tee_block_device:blk_file relabelto;
allow init seccfg_block_device:blk_file relabelto;
allow init secro_block_device:blk_file relabelto;
allow init frp_block_device:blk_file relabelto;
allow init userdata_block_device:blk_file relabelto;
allow init mtk_hal_dfps_exec:file getattr;

# Operation : Migration
# Purpose : for init reboot operate /dev/RT_Monitor when enable hang detect
allow init RT_Monitor_device:chr_file rw_file_perms;

# Purpose : For DuraSpeed
allow init proc_drop_caches:file w_file_perms;

allow init teei_client_device:chr_file rw_file_perms;

# Date : W19.28
# Purpose: Allow to setattr for duraspeed.rc
allow init proc_cpu_loading:file setattr;
allow init proc_pressure_cpu:file setattr;

# Date : W20.20
# Purpose: Allow to create socket for rild
allow init {
    volte_imsvt1_socket
    wfca_socket
}:sock_file create_file_perms;
