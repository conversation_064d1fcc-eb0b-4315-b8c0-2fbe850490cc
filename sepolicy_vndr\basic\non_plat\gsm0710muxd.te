# ==============================================
# Policy File of /vendor/bin/gsm0710muxd Executable File

# ==============================================
# Type Declaration
# ==============================================
type gsm0710muxd, domain;
type gsm0710muxd_exec, exec_type, file_type, vendor_file_type;

# ==============================================
# Common SEPolicy Rule
# ==============================================
init_daemon_domain(gsm0710muxd)

# Capabilities assigned for gsm0710muxd
allow gsm0710muxd self:capability { chown fowner setuid };

# Property service
set_prop(gsm0710muxd, vendor_mtk_ctl_ril-daemon-mtk_prop)
set_prop(gsm0710muxd, vendor_mtk_ctl_fusion_ril_mtk_prop)
set_prop(gsm0710muxd, vendor_mtk_gsm0710muxd_prop)
set_prop(gsm0710muxd, vendor_mtk_radio_prop)
set_prop(gsm0710muxd, ctl_stop_prop)
set_prop(gsm0710muxd, ctl_start_prop)

# Date: w2043
# Allow to get AOSP property persist.radio.multisim.config
get_prop(gsm0710muxd, radio_control_prop)

# allow set muxreport control properties
set_prop(gsm0710muxd, vendor_mtk_ril_mux_report_case_prop)

# Allow read/write to devices/files
allow gsm0710muxd gsm0710muxd_device:chr_file rw_file_perms;
allow gsm0710muxd mtk_radio_device:dir rw_dir_perms;
allow gsm0710muxd mtk_radio_device:lnk_file create_file_perms;
allow gsm0710muxd devpts:chr_file setattr;
allow gsm0710muxd eemcs_device:chr_file rw_file_perms;

# Allow read to sys/kernel/ccci/* files
allow gsm0710muxd sysfs_ccci:dir search;
allow gsm0710muxd sysfs_ccci:file r_file_perms;
