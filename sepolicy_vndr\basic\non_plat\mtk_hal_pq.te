# ==============================================
# Policy File of /vendor/bin/hw/vendor.mediatek.hardware.pq@2.0-service Executable File

# ==============================================
# Type Declaration
# ==============================================

type mtk_hal_pq, domain;
type mtk_hal_pq_exec, exec_type, file_type, vendor_file_type;

# ==============================================
# Common SEPolicy Rule
# ==============================================

# Setup for domain transition
init_daemon_domain(mtk_hal_pq)

# Allow a set of permissions required for a domain to be a server which provides a HAL implementation over HWBinder.
hal_server_domain(mtk_hal_pq, hal_mtk_pq)

# Allow to allocate hidl memory
hal_client_domain(mtk_hal_pq, hal_allocator)

# Purpose : Allow to use kernel driver
allow mtk_hal_pq graphics_device:chr_file rw_file_perms;

# Purpose : Allow permission to get AmbientLux from hwservice_manager
allow mtk_hal_pq fwk_sensor_hwservice:hwservice_manager find;

# Purpose : Allow permission to set pq property
set_prop(mtk_hal_pq, vendor_mtk_pq_prop)

# Purpose : Allow permission to get pq property
get_prop(mtk_hal_pq, vendor_mtk_pq_ro_prop)

# Purpose :
allow mtk_hal_pq gpu_device:dir search;
allow mtk_hal_pq dri_device:chr_file rw_file_perms;
allow mtk_hal_pq mml_pq_device:chr_file rw_file_perms;

# Purpose : Allow permission to get MMAgent from hwservice_manager
hal_client_domain(mtk_hal_pq, hal_mtk_mmagent)

# Purpose : Allow permission to use DMABuffer
allow mtk_hal_pq dmabuf_system_heap_device:chr_file r_file_perms;

# Purpose : Allow change priority
allow mtk_hal_pq self:capability sys_nice;