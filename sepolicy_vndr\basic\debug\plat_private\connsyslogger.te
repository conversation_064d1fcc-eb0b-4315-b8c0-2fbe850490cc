# ==============================================
# Common SEPolicy Rule
# ==============================================

typeattribute connsyslogger coredomain;
typeattribute connsyslogger mlstrustedsubject;
type connsyslogger_exec, system_file_type, exec_type, file_type;
init_daemon_domain(connsyslogger)

set_prop(connsyslogger, system_mtk_connsysfw_prop)

#Date:2019/06/27
#access data/debuglog
allow connsyslogger debuglog_data_file:dir {relabelto create_dir_perms};
allow connsyslogger debuglog_data_file:file create_file_perms;
