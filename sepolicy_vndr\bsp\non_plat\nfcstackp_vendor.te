# ==============================================
# Policy File of /vendor/bin/nfcstackp_vendor Executable File

# ==============================================
# Common SEPolicy Rule
# ==============================================
type nfcstackp_vendor_exec, exec_type, file_type, vendor_file_type;

# Date : WK2019
# Purpose : Start nfcstackp_vendor to serve EM
init_daemon_domain(nfcstackp_vendor)

# Date : WK2019
# Purpose : Add availablities to access nfc socket
allow nfcstackp_vendor vendor_nfc_socket_file:dir w_dir_perms;

# Date : WK2019
# Purpose : Add availablities to access nfc device
allow nfcstackp_vendor st21nfc_device:chr_file rw_file_perms;

