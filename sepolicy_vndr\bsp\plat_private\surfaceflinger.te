# ==============================================
# Common SEPolicy Rule
# ==============================================

# notify perf service of SF information for performance
allow surfaceflinger mtk_perf_service:service_manager find;

# for FpsPolicyServer
# GL context of each APP will send fps request, so we have loose policy
# FpsPolicyServer control refresh rate through dfrc
allow surfaceflinger fpspolicy-server_service:service_manager add;

# Date: WK19.122
# Stage: Q Migration, SQC
# purpose: allow RTT_Dumper access file context
allow surfaceflinger file_contexts_file:file r_file_perms;

# Date : WK20.02
# Operation: Graphics low latency 2.0
# Purpose: Allow surfaceflinger and power hal can communicate with each other
allow surfaceflinger mtk_power_hal_mgr_service:service_manager find;

# Date : WK20.02
# Operation: Graphic low latency 2.0
# Purpose: Allow surfaceflinger to read the version of Graphic Low Latency
get_prop(surfaceflinger, system_mtk_graphics_sf_gll_ro_prop)

# Date : WK20.03
# Operation: Mediatek Debug Functions
# Purpose: Allow surfaceflinger getprop debug options
get_prop(surfaceflinger, system_mtk_debug_sf_prop)

# Date : WK20.25
# Operation: Graphic low latency 2.0
# Purpose: Allow surfaceflinger to set related information for HE2.0 debug tool
set_prop(surfaceflinger, system_mtk_graphics_sf_gll_prop)

# Date : WK20.28
# Operation: Mediatek BufferQueue Debug
# Purpose: Allow surfaceflinger getprop debug options for BufferQueue
get_prop(surfaceflinger, system_mtk_debug_bq_prop)
