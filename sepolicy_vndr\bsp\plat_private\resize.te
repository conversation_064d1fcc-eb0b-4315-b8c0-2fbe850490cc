# ==============================================
# Policy File of /system/bin/resize_xxx Executable File

# ==============================================
# Common SEPolicy Rule
# ==============================================

typeattribute resize coredomain;
type resize_exec, exec_type, file_type, system_file_type;

# Date : WK15.30 => WK20.10, Android R Version
# Operation : Migration
# Purpose : resize fs(ext4/f2fs) partition, only run once.
init_daemon_domain(resize)

allow resize resize_exec:file execute_no_trans;

# Inherit and use pty created by android_fork_execvp_ext().
allow resize devpts:chr_file rw_file_perms;
allow resize proc_swaps:file rw_file_perms;
allow resize dm_device:blk_file rw_file_perms;
allow resize userdata_block_device:blk_file rw_file_perms;
allow resize metadata_block_device:blk_file getattr;
allow resize block_device:dir search;
allowxperm resize userdata_block_device:blk_file ioctl { BLKDISCARDZEROES BLKROGET };
allow resize sysfs_fs_ext4_features:dir search;
