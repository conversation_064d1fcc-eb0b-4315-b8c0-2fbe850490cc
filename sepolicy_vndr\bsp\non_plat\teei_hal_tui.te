# ==============================================
# MICROTRUST SEPolicy Rule
# ==============================================
# Set exec file type
type teei_hal_tui_exec, exec_type, vendor_file_type, file_type;

# Setup for domain transition
init_daemon_domain(teei_hal_tui)

# Set teei_hal_tui as server domain of hal_teei_tui
hal_server_domain(teei_hal_tui, hal_teei_tui)

# Access tui devices at all.
allow teei_hal_tui utr_tui_device:chr_file rw_file_perms;

# Allow to use shared memory for HAL PQ
hal_client_domain(teei_hal_tui, hal_allocator)
