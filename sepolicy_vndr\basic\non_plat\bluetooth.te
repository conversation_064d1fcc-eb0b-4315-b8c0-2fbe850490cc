# ==============================================
# Common SEPolicy Rule
# ==============================================

# Date:W17.07
# Operation : bt hal developing
# Purpose : bt hal interface permission
binder_call(bluetooth, mtk_hal_bluetooth)

allow bluetooth storage_stub_file:dir getattr;

# Date: 2018/02/02
# Major permission allow are in /system/sepoplicy/private/bluetooth.te
# Add dir create perms for bluetooth on /data/misc/bluetooth/logs
allow bluetooth bluetooth_logs_data_file:dir { create_dir_perms relabelto };
allow bluetooth bluetooth_logs_data_file:fifo_file create_file_perms;

# Date: 2019/09/19
allow bluetooth mtk_hal_bluetooth_audio_hwservice:hwservice_manager find;

# Date :  2020/06/11
# Operation : allow bt native process to access driver debug node and set kernel thread priority
# Purpose: allow bt native process to access driver debug node and set kernel thread priority
allow bluetooth proc_btdbg:file rw_file_perms;
allow bluetooth kernel:process setsched;

get_prop(bluetooth, vendor_mtk_bt_perf_prop)

# Date : 2021/09/07
# Operation : allow bluetooth to access mediametrics
# Purpose: This operation will block A2DP Sink playback
allow bluetooth mediametrics_service:service_manager find;
