# ==============================================
# MTK Attribute declarations
# ==============================================

# Attribute that represents all mtk property types (except those with ctl_xxx prefix)
attribute mtk_core_property_type;

# attribute that represents all MTK IMS types. It should be used by AP side module only.
attribute mtkimsapdomain;
#
# # attribute that represents all MTK IMS types. It should be used by MD side module only.
attribute mtkimsmddomain;

# Just for MTK's neverallow rules
attribute domain_deprecated;
attribute public_mtk_debug_domain;
attribute system_aosp_dev_type;
attribute system_aosp_domain;
attribute system_mtk_debug_dev_type;
attribute system_mtk_debug_domain;
attribute system_mtk_dev_type;
attribute system_mtk_domain;
attribute vendor_aosp_dev_type;
attribute vendor_aosp_domain;
attribute vendor_mtk_debug_dev_type;
attribute vendor_mtk_debug_domain;
attribute vendor_mtk_dev_type;
attribute vendor_mtk_domain;

# Date: 2017/06/12
# LBS HIDL
attribute hal_mtk_lbs;
attribute hal_mtk_lbs_client;
attribute hal_mtk_lbs_server;

# GPU HIDL
attribute hal_gpu;
attribute hal_gpu_client;
attribute hal_gpu_server;

# Date: 2017/06/27
# IMSA HIDL
attribute hal_mtk_imsa;
attribute hal_mtk_imsa_client;
attribute hal_mtk_imsa_server;

# Date: 2017/07/13
# NVRAM AGENT HIDL
attribute hal_mtk_nvramagent;
attribute hal_mtk_nvramagent_client;
attribute hal_mtk_nvramagent_server;

# Date: 2017/07/19
# PQ HIDL
attribute hal_mtk_pq;
attribute hal_mtk_pq_client;
attribute hal_mtk_pq_server;

# Date: 2017/07/28
# KEY ATTESTATION HIDL
attribute hal_mtk_keyattestation;
attribute hal_mtk_keyattestation_client;
attribute hal_mtk_keyattestation_server;

# Date: 2018/05/25
# FM HIDL
attribute hal_mtk_fm;
attribute hal_mtk_fm_client;
attribute hal_mtk_fm_server;

# Date: 2018/07/02
# MDP HIDL
attribute hal_mtk_mms;
attribute hal_mtk_mms_client;
attribute hal_mtk_mms_server;

# Date: 2019/06/12
# modem db filter hidl
attribute hal_mtk_md_dbfilter;
attribute hal_mtk_md_dbfilter_client;
attribute hal_mtk_md_dbfilter_server;

# Date: 2019/07/16
# HDMI HIDL
attribute hal_mtk_hdmi;
attribute hal_mtk_hdmi_client;
attribute hal_mtk_hdmi_server;

# Date: 2019/09/06
# BGService HIDL
attribute hal_mtk_bgs;
attribute hal_mtk_bgs_client;
attribute hal_mtk_bgs_server;

# Date: 2019/11/18
# em hidl
attribute hal_mtk_em;
attribute hal_mtk_em_client;
attribute hal_mtk_em_server;

attribute hal_mtk_codecservice;
attribute hal_mtk_codecservice_client;
attribute hal_mtk_codecservice_server;

attribute hal_mtk_atci;
attribute hal_mtk_atci_client;
attribute hal_mtk_atci_server;

# All types used for mtk's safe hwservice
attribute mtk_safe_hwservice_manager_type;

# All types used for mtk's safe halserver
attribute mtk_safe_halserverdomain_type;

# Date: 2020/12/30
attribute hal_mtk_mmagent;
attribute hal_mtk_mmagent_client;
attribute hal_mtk_mmagent_server;

attribute hal_mtkcodecservice;
attribute hal_mtkcodecservice_client;
attribute hal_mtkcodecservice_server;
