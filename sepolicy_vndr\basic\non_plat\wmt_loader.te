# ==============================================
# Policy File of /vendor/bin/wmt_loader Executable File

# ==============================================
# Type Declaration
# ==============================================
type wmt_loader, domain;
type wmt_loader_exec, exec_type, file_type, vendor_file_type;

# ==============================================
# Common SEPolicy Rule
# ==============================================
init_daemon_domain(wmt_loader)

allow wmt_loader self:capability chown;

# Set the property
set_prop(wmt_loader, vendor_mtk_wmt_prop)

# add ioctl/open/read/write permission for wmt_loader with /dev/wmtdetect
allow wmt_loader wmtdetect_device:chr_file rw_file_perms;

# add ioctl/open/read/write permission for wmt_loader with /dev/stpwm
allow wmt_loader stpwmt_device:chr_file rw_file_perms;
allow wmt_loader devpts:chr_file rwx_file_perms;

# Date: 2019/06/14
# Operation : Migration
allow wmt_loader proc_wmtdbg:file setattr;

# Date : 2020/08/05
# add setattr permission for wmt_loader with /proc/driver/wmt_user_proc
allow wmt_loader proc_wmtuserproc:file setattr;
