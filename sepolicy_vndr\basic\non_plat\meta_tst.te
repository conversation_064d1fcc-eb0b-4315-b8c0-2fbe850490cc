# ==============================================
# Policy File of /vendor/bin/meta_tst Executable File

# ==============================================
# Type Declaration
# ==============================================
type meta_tst, domain;
type meta_tst_exec, exec_type, file_type, vendor_file_type;

# ==============================================
# Common SEPolicy Rule
# ==============================================

init_daemon_domain(meta_tst)

# Date: WK16.12
# Operation : Migration
# Purpose : for meta mode device node USB
allow meta_tst ttyGS_device:chr_file rw_file_perms;

# Date: WK16.12
# Operation : Migration
# Purpose : for meta mode device node UART
allow meta_tst ttyMT_device:chr_file rw_file_perms;

# Date: WK17.12
# Operation : Migration
# Purpose : for meta mode device node UART
allow meta_tst ttyS_device:chr_file rw_file_perms;

# Date: WK16.12
# Operation : Migration
# Purpose : for meta mode device node CCCI
allow meta_tst ccci_device:chr_file rw_file_perms;
allow meta_tst eemcs_device:chr_file rw_file_perms;
allow meta_tst emd_device:chr_file rw_file_perms;
allow meta_tst ttyACM_device:chr_file rw_file_perms;
allow meta_tst mdlog_device:chr_file rw_file_perms;

# Data: WK15.07
# Purpose : SDIO
allow meta_tst ttySDIO_device:chr_file rw_file_perms;

# Date: WK16.12
# Operation : Migration
# Purpose : for meta mode file system
allow meta_tst bootdevice_block_device:blk_file rw_file_perms;
allow meta_tst mmcblk1_block_device:blk_file rw_file_perms;
allow meta_tst userdata_block_device:blk_file rw_file_perms;
allow meta_tst cache_block_device:blk_file rw_file_perms;

# Date: WK16.12
# Operation : Migration
# Purpose : for meta mode nvram
allow meta_tst nvram_data_file:dir create_dir_perms;
allow meta_tst nvram_data_file:file create_file_perms;
allow meta_tst nvram_data_file:lnk_file r_file_perms;
allow meta_tst nvdata_file:lnk_file r_file_perms;
allow meta_tst nvdata_file:dir create_dir_perms;
allow meta_tst nvdata_file:file create_file_perms;
allow meta_tst nvram_device:chr_file rw_file_perms;
allow meta_tst nvram_device:blk_file rw_file_perms;
allow meta_tst nvdata_device:blk_file rw_file_perms;
read_fstab(meta_tst)
get_prop(meta_tst, vendor_mtk_rat_config_prop)

# Date: WK14.47
# Operation : Migration
# Purpose : for meta mode audio
allow meta_tst audio_device:chr_file rw_file_perms;
allow meta_tst audio_device:dir rw_dir_perms;
allow meta_tst audio_ipi_device:chr_file rw_file_perms;
set_prop(meta_tst, vendor_mtk_audiohal_prop)

# Date: WK16.12
# Operation : Migration
# Purpose : for meta mode RTC and PMIC
allow meta_tst rtc_device:chr_file r_file_perms;
allow meta_tst MT_pmic_adc_cali_device:chr_file rw_file_perms;

# Date: WK14.46
# Operation : Migration
# Purpose : Camera
allow meta_tst devmap_device:chr_file rw_file_perms;
allow meta_tst camera_pipemgr_device:chr_file rw_file_perms;
allow meta_tst MTK_SMI_device:chr_file rw_file_perms;
allow meta_tst camera_isp_device:chr_file rw_file_perms;
allow meta_tst camera_sysram_device:chr_file r_file_perms;
allow meta_tst kd_camera_flashlight_device:chr_file rw_file_perms;
allow meta_tst kd_camera_hw_device:chr_file rw_file_perms;
allow meta_tst AD5820AF_device:chr_file rw_file_perms;
allow meta_tst DW9714AF_device:chr_file rw_file_perms;
allow meta_tst DW9714A_device:chr_file rw_file_perms;
allow meta_tst LC898122AF_device:chr_file rw_file_perms;
allow meta_tst LC898212AF_device:chr_file rw_file_perms;
allow meta_tst BU6429AF_device:chr_file rw_file_perms;
allow meta_tst DW9718AF_device:chr_file rw_file_perms;
allow meta_tst BU64745GWZAF_device:chr_file rw_file_perms;
allow meta_tst MAINAF_device:chr_file rw_file_perms;
allow meta_tst MAIN2AF_device:chr_file rw_file_perms;
allow meta_tst MAIN3AF_device:chr_file rw_file_perms;
allow meta_tst MAIN4AF_device:chr_file rw_file_perms;
allow meta_tst SUBAF_device:chr_file rw_file_perms;
allow meta_tst SUB2AF_device:chr_file rw_file_perms;

# Date: WK16.12
# Operation : Migration
# Purpose : meta mode LCM
allow meta_tst graphics_device:chr_file rw_file_perms;
allow meta_tst graphics_device:dir search;

# Date: WK16.12
# Operation : Migration
# Purpose : meta mode sensor
allow meta_tst als_ps_device:chr_file r_file_perms;
allow meta_tst gsensor_device:chr_file r_file_perms;
allow meta_tst msensor_device:chr_file r_file_perms;
allow meta_tst gyroscope_device:chr_file r_file_perms;

# Date: WK16.12
# Operation : Migration
# Purpose : meta mode FM
allow meta_tst fm_device:chr_file rw_file_perms;
allow meta_tst FM50AF_device:chr_file rw_file_perms;

# Date: WK16.12
# Operation : Migration
# Purpose : meta mode wifi
allow meta_tst wmtWifi_device:chr_file w_file_perms;

# Date: WK16.12
# Operation : Migration
# Purpose : meta mode BT
allow meta_tst stpbt_device:chr_file rw_file_perms;

# Date: WK16.12
# Operation : Migration
# Purpose : meta mode GPS
allow meta_tst gps_data_file:dir { w_dir_perms unlink};
allow meta_tst gps_data_file:file create_file_perms;
allow meta_tst gps_data_file:lnk_file r_file_perms;
allow meta_tst tmpfs:lnk_file r_file_perms;
allow meta_tst agpsd_data_file:dir search;
allow meta_tst agpsd_data_file:sock_file w_file_perms;
allow meta_tst mnld_device:chr_file rw_file_perms;
allow meta_tst stpgps_device:chr_file rw_file_perms;
allow meta_tst mnld_exec:file rx_file_perms;
allow meta_tst mnld:unix_dgram_socket sendto;
set_prop(meta_tst, vendor_mtk_mnld_prop)

#Date WK14.49
#Operation : Migration
#Purpose : DRM key installation
allow meta_tst key_install_data_file:dir w_dir_perms;
allow meta_tst key_install_data_file:file create_file_perms;

# Date: WK14.51
# Purpose : set/get cryptfs cfg in sys env
allow meta_tst misc_device:chr_file rw_file_perms;
allow meta_tst proc_lk_env:file rw_file_perms;

# Purpose : FT_EMMC_OP_FORMAT_TCARD
allow meta_tst system_block_device:blk_file getattr;

# Date: WK15.52
# Purpose : NVRAM related LID
allow meta_tst pro_info_device:chr_file rw_file_perms;

# Date: WK15.13
# Purpose: for nand project
allow meta_tst mtd_device:dir search;
allow meta_tst mtd_device:chr_file rw_file_perms;

# Date: WK16.17
# Purpose:  N Migration For ccci sysfs node
allow meta_tst sysfs_ccci:dir search;
allow meta_tst sysfs_ccci:file r_file_perms;

#Date: W18.22
# Purpose: P Migration meta_tst get com port type/uart port info/boot mode/usb state/usb close
allow meta_tst sysfs_comport_type:file rw_file_perms;
allow meta_tst sysfs_uart_info:file rw_file_perms;
allow meta_tst sysfs_boot_mode:file rw_file_perms;
allow meta_tst sysfs_boot_type:file r_file_perms;
allow meta_tst sysfs_android_usb:file rw_file_perms;
allow meta_tst sysfs_android_usb:dir search;
allow meta_tst sysfs_usb_nonplat:file rw_file_perms;
allow meta_tst sysfs_usb_nonplat:dir search;
allow meta_tst sysfs_batteryinfo:file rw_file_perms;
allow meta_tst sysfs_batteryinfo:dir search;

#Date: W16.17
# Purpose:  N Migration For meta_tst load MD NVRAM database
# Detail avc log: [04-23-20:41:58][  160.687655] <1>.(1)[230:logd.auditd]type=
#1400 audit(1262304165.560:24): avc: denied { read } for pid=228 comm=
#"meta_tst" name="mddb" dev="mmcblk0p20" ino=664 scontext=u:r:meta_tst:
#s0 tcontext=u:object_r:system_file:s0 tclass=dir permissive=0
allow meta_tst system_file:dir r_dir_perms;

# Date: WK16.18
# Purpose: for CCCI reboot modem
allow meta_tst gsm0710muxd_device:chr_file rw_file_perms;

# Date : WK16.35
# Purpose : Update camera flashlight driver device file
allow meta_tst flashlight_device:chr_file rw_file_perms;

#Date: W16.36
# Purpose:  meta_tst use libmeta_rat to write libsysenv
# Detail avc log:[   25.307141] .(5)[264:logd.auditd]type=1400 audit(1469438818.570:7):
#avc: denied { read write } for pid=312 comm="meta_tst" name="mmcblk0p2" dev="tmpfs"
#ino=4561 scontext=u:r:meta_tst:s0 tcontext=u:object_r:para_block_device:s0 tclass=blk_file permissive=0
allow meta_tst para_block_device:blk_file rw_file_perms;

#Date: W16.44
allow meta_tst nvcfg_file:dir r_dir_perms;

#Date: W16.45
# Purpose : Allow unmount sdcardfs mounted on /data/media
allow meta_tst sdcard_type:filesystem unmount;
allow meta_tst storage_stub_file:dir search;

# Date : WK16.19
# Operation: meta_tst set persist.meta.connecttype property
# Purpose: Switch meta connect type, set persist.meta.connecttype as "wifi" or "usb".
set_prop(meta_tst, vendor_mtk_meta_connecttype_prop)

# Date : WK16.23
# Purpose: support meta_tst check key event
allow meta_tst input_device:dir r_dir_perms;
allow meta_tst input_device:chr_file r_file_perms;

# Date : WK16.29
# Purpose: support meta mode show string on screen
allow meta_tst ashmem_device:chr_file x_file_perms;

#Date: W16.50
# Purpose : Allow meta_tst stop service which occupy data partition.
set_prop(meta_tst, ctl_default_prop)

#Date: W17.25
# Purpose : Allow meta_tst stop service which occupy data partition.
set_prop(meta_tst, system_mtk_ctl_emdlogger1_prop)

#Date: W17.27
# Purpose: STMicro NFC solution integration
allow meta_tst vendor_file:file rx_file_perms;
allow meta_tst debugfs_tracing:file w_file_perms;

# Date: W17.29
# Purpose : Allow meta_tst to call vendor.mediatek.hardware.keymaster_attestation@1.0-service.
hal_client_domain(meta_tst, hal_mtk_keyattestation)

# Date : WK17.30
# Operation : Android O migration
# Purpose : add sepolicy for accessing sysfs_leds
allow meta_tst sysfs_leds:lnk_file r_file_perms;
allow meta_tst sysfs_leds:file rw_file_perms;
allow meta_tst sysfs_leds:dir r_dir_perms;

# Date: WK17.43
# Purpose: add permission for meta_tst access md image
allow meta_tst md_block_device:blk_file r_file_perms;
allow meta_tst mddb_data_file:file create_file_perms;
allow meta_tst mddb_data_file:dir create_dir_perms;

# Date: W17.43
# Purpose : Allow meta_tst to call Audio HAL service
binder_call(meta_tst, hal_audio_default)
allow meta_tst mtk_audiohal_data_file:dir r_dir_perms;

#Data:W1745
# Purpose : Allow meta_tst to open and read proc/bootprof
allow meta_tst proc_bootprof:file rw_file_perms;

# Date:W17.51
# Operation : lbs hal
# Purpose : lbs hidl interface permission
hal_client_domain(meta_tst, hal_mtk_lbs)

# Data:W1750
# Purpose : Allow meta_tst to access mtd device
allow meta_tst mtd_device:blk_file rw_file_perms;

#Date: W17.51
#Purpose : Allow meta_tst to access pesist.atm.mdmode in ATM.
set_prop(meta_tst, vendor_mtk_atm_mdmode_prop)

#Date: W17.51
#Purpose : Allow meta_tst to access pesist.atm.ipaddress in ATM.
set_prop(meta_tst, vendor_mtk_atm_ipaddr_prop)

# Date : WK18.16
# Operation: P migration
# Purpose: Allow meta_tst to get vendor_mtk_tel_switch_prop
get_prop(meta_tst, vendor_mtk_tel_switch_prop)

# Date : WK18.21
# Operation: P migration
# Purpose : Allow meta_tst to call nvram hal
hal_client_domain(meta_tst, hal_mtk_nvramagent)

# Date : WK18.21
# Operation: P migration
# Purpose : Allow meta_tst to write misc partition
allow meta_tst block_device:dir search;

# Date : W18.24
# Operation: P migration
# Purpose : Allow meta_tst to access tpd sysfs nodes for CTP test
allow meta_tst sysfs_tpd_setting:dir search;
allow meta_tst sysfs_tpd_setting:file r_file_perms;

# Date : WK18.24
# Operation: P migration
# Purpose : Allow meta_tst to unmount partition, stop service, and then erase partition
allow meta_tst vendor_shell_exec:file rx_file_perms;
allow meta_tst vendor_toolbox_exec:file x_file_perms;
allow meta_tst labeledfs:filesystem { unmount };
allow meta_tst proc_cmdline:file r_file_perms;
allow meta_tst self:capability { sys_admin sys_module net_admin net_raw sys_time };
allow meta_tst sysfs_dt_firmware_android:file r_file_perms;
allow meta_tst sysfs_dt_firmware_android:dir r_dir_perms;

# Purpose : Allow meta_tst to communicate with driver thru socket
allow meta_tst self:udp_socket create_socket_perms;
allowxperm meta_tst self:udp_socket ioctl priv_sock_ioctls;

# Date : WK18.25
# Operation: P migration
# Purpose : GPS test, Allow meta_tst to write/connect tcp socket
allow meta_tst node:tcp_socket node_bind;
allow meta_tst port:tcp_socket { name_bind name_connect };

# Date : WK18.28
# Operation: P migration
# Purpose : AUDIO test, Allow meta_tst to write/read asound
allow meta_tst proc_asound:dir r_dir_perms;
allow meta_tst proc_asound:file rw_file_perms;
allow meta_tst sysfs_headset:file r_file_perms;

# Date: W18.05
# Purpose : Allow meta_tst to use socket for listening uevent
allow meta_tst meta_tst:netlink_kobject_uevent_socket create_socket_perms_no_ioctl;

# Date : WK18.28
# Operation: P migration
# Purpose :
set_prop(meta_tst, vendor_mtk_usb_prop)

# Date: W18.32
# Operation: Android P migration
# Purpose : Allow meta_tst to set powerctl property
# avc:  denied  { set } for property=sys.powerctl pid=330 uid=0 gid=1001 scontext=u:r:meta_tst:s0
# tcontext=u:object_r:powerctl_prop:s0 tclass=property_service permissive=0
set_prop(meta_tst, powerctl_prop)

# Data: W18.42
# Operation: Android P migration
# Purpose : add socket permission for meta
allow meta_tst fwmarkd_socket:sock_file write;

#Date: W18.42
# Operation: Android P migration
# Purpose : Add ATM meta mvram sepolicy
allow meta_tst mnt_vendor_file:dir search;

# Date : WK18.44
# Operation: P migration
# Purpose : adsp
allow meta_tst adsp_device:chr_file rw_file_perms;

# Date : WK19.08
# Operation: P migration
# Purpose : audio scp recovery
allow meta_tst audio_scp_device:chr_file r_file_perms;

# Date : WK19.50
# Purpose: Allow bt process or tool to control bt_dbg
allow meta_tst proc_btdbg:file rw_file_perms;

# Date : WK20.07
# Operation: R migration
# Purpose : Add permission for new device node.
allow meta_tst sysfs_boot_info:file r_file_perms;
allow meta_tst sysfs_meta_info:file r_file_perms;

# Date : WK20.16
# Operation: R migration
# Purpose : Allow meta_tst to access /sys/power/*
allow meta_tst sysfs_power:file rw_file_perms;
allow meta_tst sysfs_power:dir r_dir_perms;
allow meta_tst self:capability2 block_suspend;

# Date : WK20.14
# Purpose: Allow meta connect GPS MNLD
allow meta_tst mnld:unix_stream_socket connectto;

# Date : WK20.25
# Operation: Android R migration
# Purpose : for sensor test
allow meta_tst hf_manager_device:chr_file rw_file_perms;

# Date : WK20.25
# Operation: Android S migration
# Purpose : Allow  meta_tst to  operate tcp socket
allow meta_tst self:tcp_socket { listen accept create_socket_perms_no_ioctl };

allow meta_tst meta_atci_socket:sock_file w_file_perms;

# Date : WK21.40
# Operation: Android S migration
# Purpose : Allow  meta_tst to read/write ccci/ccb
allow meta_tst ccci_ccb_device:chr_file rw_file_perms;