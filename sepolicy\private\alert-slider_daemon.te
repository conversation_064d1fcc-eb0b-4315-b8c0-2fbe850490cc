type proc_tristatekey, fs_type, proc_type;

type alert-slider_daemon, domain, coredomain;
type alert-slider_daemon_exec, system_file_type, exec_type, file_type;
type service_call_exec, system_file_type, exec_type, file_type;

init_daemon_domain(alert-slider_daemon)

allow alert-slider_daemon uhid_device:chr_file rw_file_perms;
allow alert-slider_daemon self:netlink_kobject_uevent_socket create_socket_perms_no_ioctl;
allow alert-slider_daemon proc_tristatekey:dir r_dir_perms;
allow alert-slider_daemon proc_tristatekey:file { open read };
allow alert-slider_daemon shell_exec:file { execute execute_no_trans read open map getattr };
allow alert-slider_daemon input_device:dir { search };
allow alert-slider_daemon input_device:chr_file { open read write ioctl };
allow alert-slider_daemon audio_service:service_manager { find };
allow alert-slider_daemon service_call_exec:file { execute execute_no_trans read open map getattr };

allow alert-slider_daemon sysfs_leds:dir search;
allow alert-slider_daemon sysfs_vibrator:file { open write };

binder_call(alert-slider_daemon, system_server)
binder_call(alert-slider_daemon, servicemanager)
