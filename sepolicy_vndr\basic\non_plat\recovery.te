# ==============================================
# Common SEPolicy Rule
# ==============================================
# recovery console (used in recovery init.rc for /sbin/recovery)

# Date : WK18.16
# Operation : UT
# Purpose : Refine policy
allow recovery misc_sd_device:chr_file rw_file_perms;
allow recovery vfat:dir r_dir_perms;
allow recovery vfat:file r_file_perms;
allow recovery sysfs_devices_block:dir r_dir_perms;
allow recovery sysfs_devices_block:file rw_file_perms;
allow recovery sysfs_devices_block:lnk_file r_file_perms;

# Date : WK18.25
# Operation : UT
# Purpose : Add policy for therm, gpu, battery, and boot_type
allow recovery sysfs:dir r_dir_perms;
allow recovery sysfs_batteryinfo:dir r_dir_perms;
allow recovery sysfs_boot_type:file r_file_perms;
allow recovery sysfs_therm:dir r_dir_perms;
allow recovery sysfs_therm:file r_file_perms;
allow recovery gpu_device:dir r_dir_perms;
allow recovery dri_device:chr_file rw_file_perms;

# Date : WK18.09
# Operation : UT
# Purpose : Allow recovery can update boot partition
allow recovery tmpfs:lnk_file r_file_perms;

# Date : WK19.03
# Operation : UT
# Purpose : Android Migration
allow recovery bootdevice_block_device:blk_file rw_file_perms;
allowxperm recovery bootdevice_block_device:blk_file ioctl {
 MMC_IOCTLCMD
 UFS_IOCTLCMD
};

allow recovery sysfs_dm:dir search;
allow recovery sysfs_dm:file r_file_perms;
allowxperm recovery tmpfs:file ioctl FS_IOC_FIEMAP;
allowxperm recovery cache_block_device:blk_file ioctl BLKPBSZGET;
allowxperm recovery nvdata_device:blk_file ioctl BLKPBSZGET;
allow recovery proc_filesystems:file r_file_perms;

# Seen during 'Wipe data/factory reset'
allow recovery devpts:chr_file rw_file_perms;
