# ==============================================
# Common SEPolicy Rule
# ==============================================

# ccci device for internal modem
allow mdlogger ccci_device:chr_file rw_file_perms;
allow mdlogger ccci_mdl_device:chr_file rw_file_perms;

# usb device ttyGSx for modem logger usb logging
allow mdlogger ttyGS_device:chr_file rw_file_perms;

# modem logger access on /data/mdlog
allow mdlogger mdlog_data_file:dir { create_dir_perms relabelto};
allow mdlogger mdlog_data_file:fifo_file create_file_perms;
allow mdlogger mdlog_data_file:file create_file_perms;

# modem logger control port access /dev/ttyC1
allow mdlogger mdlog_device:chr_file rw_file_perms;

#modem logger SD logging in factory mode
allow mdlogger vfat:dir create_dir_perms;
allow mdlogger vfat:file create_file_perms;

#mdlogger for read /sdcard
allow mdlogger tmpfs:lnk_file r_file_perms;
allow mdlogger storage_file:lnk_file rw_file_perms;
allow mdlogger storage_file:dir create_dir_perms;
allow mdlogger storage_file:file create_file_perms;
allow mdlogger mnt_user_file:dir search;
allow mdlogger mnt_user_file:lnk_file rw_file_perms;
allow mdlogger sdcard_type:file create_file_perms;
allow mdlogger sdcard_type:dir create_dir_perms;

# Allow read to sys/kernel/ccci/* files
allow mdlogger sysfs_ccci:dir search;
allow mdlogger sysfs_ccci:file r_file_perms;

# purpose: allow mdlogger to access storage in new version
allow mdlogger media_rw_data_file:file create_file_perms;
allow mdlogger media_rw_data_file:dir create_dir_perms;

## purpose: avc: denied { read } for name="plat_file_contexts"
allow emdlogger file_contexts_file:file r_file_perms;

#permission for read boot mode
#avc: denied { open }  path="/sys/devices/virtual/BOOT/BOOT/boot/boot_mode" dev="sysfs"
allow mdlogger sysfs_boot_mode:file r_file_perms;

# avc: denied { open } for path="system/etc/mddb" dev="mmcblk0p21" scontext=u:r:emdlogger:s0 tcontext=u:object_r:system_file:s0 tclass=dir permissive=0
allow mdlogger system_file:dir r_dir_perms;

# Add permission to access new bootmode file
allow mdlogger sysfs_boot_info:file r_file_perms;

#avc: denied { connectto } for path=006165653A72747464 scontext=u:r:mdlogger:s0
#tcontext=u:object_r:aee_aed_socket:s0 tclass=unix_stream_socket permissive=0
#security issue control
allow mdlogger crash_dump:unix_stream_socket connectto;
