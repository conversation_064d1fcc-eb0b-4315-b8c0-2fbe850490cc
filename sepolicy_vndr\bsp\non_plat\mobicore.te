##
# Trustonic TEE (mobicore) daemon
#

# ==============================================
# Type Declaration
# ==============================================
type mobicore_exec, exec_type, file_type, vendor_file_type;

# ==============================================
# Common SEPolicy Rule
# ==============================================
# permissive mobicore;
init_daemon_domain(mobicore)
allow mobicore mobicore_admin_device:chr_file rw_file_perms;
allow mobicore mobicore_user_device:chr_file rw_file_perms;
allow mobicore mobicore_data_file:dir { create rw_dir_perms rename reparent };
allow mobicore mobicore_data_file:file { create_file_perms rw_file_perms };

# Date : 2016/10/17 (or WK16.43)
# Operation : TUI Migration/SQC
# Purpose : Set new added properties for TuiStarter in 311B mcDriverDaemon
allow mobicore mobicore_tui_device:chr_file { read open ioctl };

# Date: 2017/12/11
# Purpose: set policy for FBE
allow mobicore unlabeled:dir search;

# Date: 2018/06/22
# Purpose: set sepolicy for access mnt vendor file on Android P
allow mobicore mnt_vendor_file:dir search;
allow mobicore persist_data_file:dir { ra_dir_perms };
allow mobicore persist_data_file:file { read write create open getattr };
