# ==============================================
# Type Declaration
# ==============================================
type mtk_hal_dplanner_exec, exec_type, file_type, vendor_file_type;

# ==============================================
# Common SEPolicy Rule
# ==============================================
init_daemon_domain(mtk_hal_dplanner);

userdebug_or_eng(`
    #Allow domain:mtk_hal_dplanner to use HWBinder IPC
    hwbinder_use(mtk_hal_dplanner);

    #Allow a set of permissions required for a domain to be a server which provides a HAL implementation over HWBinder
    hal_server_domain(mtk_hal_dplanner, hal_dplanner);

    #Add/find permission rule to hwservicemanager
    add_hwservice(hal_dplanner, mtk_hal_dplanner_hwservice);

    #Allow platform app calls
    allow mtk_hal_dplanner platform_app:binder { call transfer };

    #Give permissions of dconfig
    domain_auto_trans(mtk_hal_dplanner, mtk_dconfig_exec, mtk_dconfig);

    #allow store data
    allow mtk_hal_dplanner doe_vendor_data_file:dir create_dir_perms;
    allow mtk_hal_dplanner doe_vendor_data_file:file create_file_perms;
')
