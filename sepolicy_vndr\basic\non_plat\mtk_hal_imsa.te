# ==============================================================================
# Type Declaration
# ==============================================================================
type mtk_hal_imsa, domain, mtkimsapdomain;
type mtk_hal_imsa_exec, exec_type, vendor_file_type, file_type;

# ==============================================
# Common SEPolicy Rule
# ==============================================

init_daemon_domain(mtk_hal_imsa)

# hwbinder access
hal_server_domain(mtk_hal_imsa, hal_mtk_imsa)

# call into system_server process (callbacks)
binder_call(mtk_hal_imsa, system_server)

# Date : 2017/05/18
# Operation : VoLTE sanity
# Purpose : Add permission for IMSA connect to IMSM
allow mtk_hal_imsa rild_imsm_socket:sock_file w_file_perms;

# Date : 2017/06/13
# Operation : IMSA sanity
# Purpose : Add permission for IMSA to access radio
allow mtk_hal_imsa radio:binder call;
allow mtk_hal_imsa debugfs_tracing:file w_file_perms;
