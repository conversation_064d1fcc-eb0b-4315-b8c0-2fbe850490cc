# ==============================================
# Policy File of /vendor/bin/fuelgauged_nvram Executable File

# ==============================================
# Type Declaration
# ==============================================
type fuelgauged_nvram, domain;
type fuelgauged_nvram_exec , exec_type, file_type, vendor_file_type;

# ==============================================
# Common SEPolicy Rule
# ==============================================

init_daemon_domain(fuelgauged_nvram)

# Data : WK16.21
# Operation : New Feature
# Purpose : For fg daemon can do nvram r/w to save car_tune_value
allow fuelgauged_nvram nvdata_file:dir rw_dir_perms;
allow fuelgauged_nvram nvdata_file:file create_file_perms;
allow fuelgauged_nvram nvdata_file:lnk_file rw_file_perms;
allow fuelgauged_nvram nvram_data_file:lnk_file rw_file_perms;

# Data : W16.43
# Operation : New Feature
# Purpose : Change from /data to /cache
allow fuelgauged_nvram self:capability { chown fsetid };
allow fuelgauged_nvram kmsg_device:chr_file w_file_perms;

# Data : W17.34
# Operation : New Feature
# Purpose : fgauge_nvram could use IOCTL
allow fuelgauged_nvram MT_pmic_adc_cali_device:chr_file rw_file_perms;

# Date: W18.03
# Operation : change fuelgagued_nvram access from cache to nvcfg
# Purpose : add fuelgauged to nvcfg read write permit
# need add label
allow fuelgauged_nvram nvcfg_file:dir create_dir_perms;
allow fuelgauged_nvram nvcfg_file:file create_file_perms;

# Date: W18.17
# Operation : add label for /sys/devices/platform/battery(/.*)
# Purpose : add fuelgauged could access
r_dir_file(fuelgauged_nvram, sysfs_batteryinfo)


# Date : WK18.21
# Operation: P migration
# Purpose: Allow to search /mnt/vendor/nvdata for fstab when using NVM_Init()
allow fuelgauged_nvram mnt_vendor_file:dir search;

allow fuelgauged_nvram sysfs_boot_mode:file r_file_perms;

# Allow ReadDefaultFstab().
read_fstab(fuelgauged_nvram)
