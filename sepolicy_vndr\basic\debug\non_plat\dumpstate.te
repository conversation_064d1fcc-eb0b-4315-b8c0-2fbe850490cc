# Purpose: data/aee_exp/*
allow dumpstate aee_exp_data_file:dir rw_dir_perms;
allow dumpstate aee_exp_data_file:file create_file_perms;

# Data : 2017/03/22
# Operation : add fd use selinux rule
# Purpose : type=1400 audit(0.0:81356): avc: denied { use } for path="/system/bin/linker"
#           dev="mmcblk0p26" ino=250 scontext=u:r:dumpstate:s0
#           tcontext=u:r:crash_dump:s0 tclass=fd permissive=0
allow dumpstate crash_dump:fd use;
allow dumpstate crash_dump:unix_stream_socket { rw_socket_perms connectto };

# Purpose: access dev/aed0
allow dumpstate aed_device:chr_file r_file_perms;
allow dumpstate vcp_device:chr_file r_file_perms_no_map;

# Purpose: 01-01 08:30:57.260  3070  3070 W aee_dumpstate: type=1400 audit(0.0:13196): avc: denied
# { read } for name="SF_dump" dev="dm-0" ino=352257 scontext=u:r:dumpstate:s0 tcontext=u:object_r:
# sf_bqdump_data_file:s0 tclass=dir permissive=0
allow dumpstate sf_bqdump_data_file:dir r_dir_perms;
allow dumpstate sf_bqdump_data_file:file r_file_perms;

# Purpose:
# 01-01 17:59:14.440  7664  7664 I aee_dumpstate: type=1400 audit(0.0:63497):
# avc: denied { open } for path="/sys/kernel/debug/tracing/tracing_on" dev=
# "debugfs" ino=2087 scontext=u:r:dumpstate:s0 tcontext=u:object_r:
# tracing_shell_writable:s0 tclass=file permissive=1
allow dumpstate debugfs_tracing:file rw_file_perms;

# Purpose: Allow aee_dumpstate to invoke "lshal debug <interface>", where <interface> is "ICameraProvider".
allow dumpstate mtk_hal_camera:binder call;

# Purpose: Allow aee_dumpstate to read /proc/slabinfo
allow dumpstate proc_slabinfo:file r_file_perms;

# Purpose: Allow aee_dumpstate to read /proc/zraminfo
allow dumpstate proc_zraminfo:file r_file_perms;

# Purpose: Allow aee_dumpstate to read /proc/gpulog
allow dumpstate proc_gpulog:file r_file_perms;

# Purpose: Allow aee_dumpstate to read /proc/sched_debug
allow dumpstate proc_sched_debug:file r_file_perms;

# Purpose: Allow aee_dumpstate to read /proc/chip/hw_ver
allow dumpstate proc_chip:file r_file_perms;
allow dumpstate proc_chip:dir r_dir_perms;

# Purpose: Allow aee_dumpstate to write /sys/devices/virtual/timed_output/vibrator/enable
allow dumpstate sysfs_vibrator_setting:file w_file_perms;

# Date : 2020/12/14
# Purpose: allow aee_dumpstate to read /sys/kernel/mm/mlog/dump
allow dumpstate sysfs_mm:file r_file_perms;

#Purpose: Allow dumpstate to read /sys/bus/scsi/devices/0:0:0:0/vpd_pg80
allow dumpstate sysfs_vpd:dir r_dir_perms;
allow dumpstate sysfs_vpd:file r_file_perms;

#Purpose: Alloc dumpstate to read /proc/dma_heap/
allow dumpstate proc_dmaheap:dir r_dir_perms;
allow dumpstate proc_dmaheap:file r_file_perms;

#Purpose: Allow dumpstate to read /proc/iommu_debug/
allow dumpstate proc_iommu_debug:dir r_dir_perms;
allow dumpstate proc_iommu_debug:file r_file_perms;

#Date: 2020/07/23
#Purpose: Allow dumpstate to read /sys/kernel/notes
allow dumpstate sysfs_kernel_notes:file r_file_perms;

no_debugfs_restriction(`
  userdebug_or_eng(`
    allow dumpstate debugfs_blockio:file r_file_perms;
    allow dumpstate debugfs_fb:dir search;
    allow dumpstate debugfs_fb:file r_file_perms;
    allow dumpstate debugfs_fuseio:dir search;
    allow dumpstate debugfs_fuseio:file r_file_perms;
    allow dumpstate debugfs_rcu:dir search;
    allow dumpstate debugfs_shrinker_debug:file r_file_perms;
    allow dumpstate debugfs_dmlog_debug:file r_file_perms;
    allow dumpstate debugfs_page_owner_slim_debug:file r_file_perms;
    allow dumpstate debugfs_ion_mm_heap:dir search;
    allow dumpstate debugfs_ion_mm_heap:file r_file_perms;
    allow dumpstate debugfs_ion_mm_heap:lnk_file r_file_perms;
    allow dumpstate debugfs_cpuhvfs:dir search;
    allow dumpstate debugfs_cpuhvfs:file r_file_perms;

    # Purpose: Allow dumpstate to read /sys/kernel/debug/rcu/rcu_callback_log
    allow dumpstate debugfs_rcu:file r_file_perms;

    # Date: 19/07/15
    # Purpose: Allow dumpstate to read /sys/kernel/debug/kmemleak
    allow dumpstate debugfs_kmemleak:file r_file_perms;

    #Purpose: Allow dumpstate to read /sys/kernel/debug/smi_mon
    allow dumpstate debugfs_smi_mon:file r_file_perms;

    allow dumpstate debugfs_cmdq:file r_file_perms;
    allow dumpstate debugfs_mml:file r_file_perms;
    allow dumpstate debugfs_wakeup_sources:file r_file_perms;
  ')
')

#Date: 2021/08/24
#Purpose: debugfs files
no_debugfs_restriction(`
  userdebug_or_eng(`
    allow dumpstate debugfs_cam_dbg:file r_file_perms;
    allow dumpstate debugfs_cam_exception:file r_file_perms;
   ')
')

allow dumpstate sysfs_dvfsrc_dbg:dir r_dir_perms;
allow dumpstate sysfs_dvfsrc_dbg:file r_file_perms;
#Purpose: Allow dumpstate to read /proc/apusys_rv/apusys_rv_xfile and /proc/apusys_logger/seq_log
allow dumpstate proc_apusys_rv_xfile_debug:file r_file_perms;
allow dumpstate proc_apusys_logger_seq_log_debug:file r_file_perms;
allow dumpstate sysfs_emiisu:file r_file_perms;

#Purpose: Allow dumpstate to read /proc/vpu/vpu_memory
allow dumpstate proc_vpu_memory:file r_file_perms;

#Purpose: Allow dumpstate to read /proc/mtk_mali/gpu_memory
allow dumpstate proc_gpu_memory:file r_file_perms;

