# ==============================================
# Common SEPolicy Rule
# ==============================================

type cmddumper_exec, system_file_type, exec_type, file_type;
typeattribute cmddumper coredomain;

init_daemon_domain(cmddumper)

# for modem logging sdcard access
allow cmddumper sdcard_type:dir create_dir_perms;
allow cmddumper sdcard_type:file create_file_perms;

# modem logger socket access
allow cmddumper platform_app:unix_stream_socket connectto;
allow cmddumper shell_exec:file rx_file_perms;
allow cmddumper system_file:file x_file_perms;

# purpose: allow cmddumper to access storage in N version
allow cmddumper media_rw_data_file:file create_file_perms;
allow cmddumper media_rw_data_file:dir create_dir_perms;

# purpose: access plat_file_contexts
allow cmddumper file_contexts_file:file r_file_perms;

# Save C2K modem log into data
allow cmddumper debuglog_data_file:dir { relabelto create_dir_perms };
allow cmddumper debuglog_data_file:file create_file_perms;

#allow emdlogger to set property
set_prop(cmddumper, system_mtk_debug_mdlogger_prop)
set_prop(cmddumper, debug_prop)

# Android P migration
set_prop(cmddumper, system_mtk_persist_mtklog_prop)
set_prop(cmddumper, system_mtk_mdl_prop)
