allow hal_drm_widevine debugfs_tracing:file write;
allow hal_drm_widevine debugfs_ion:dir search;

# Allow widevine hidl process read keybox stored in /mnt/vendor/persist
allow hal_drm_widevine mnt_vendor_file:dir search;

# Add sepolicy for tee relate
allow hal_drm_widevine mobicore:unix_stream_socket connectto;
allow hal_drm_widevine persist_data_file:dir search;
allow hal_drm_widevine mobicore_user_device:chr_file { read write ioctl open };
allow hal_drm_widevine persist_data_file:file { read getattr open };
allow hal_drm_widevine mobicore_data_file:file { read open getattr};
allow hal_drm_widevine mobicore_data_file:dir search;
allow hal_drm_widevine block_device:dir search;
allow hal_drm_widevine kb_block_device:blk_file r_file_perms;
allow hal_drm_widevine dkb_block_device:blk_file r_file_perms;

# Allow widevine hidl process access teeperf
allow hal_drm_widevine teeperf_device:chr_file rw_file_perms_no_map;

# Allow widevine hidl process access TEEI
hal_client_domain(hal_drm_widevine, hal_teei_capi)
hal_client_domain(hal_drm_widevine, hal_allocator)
allow hal_drm_widevine teei_client_device:chr_file rw_file_perms;

# Add selinux policy for mtee related permission
allow hal_drm_widevine kisd:unix_stream_socket connectto;
allow hal_drm_widevine sysfs_mmcblk:dir search;
allow hal_drm_widevine sysfs_mmcblk:file { read open getattr};

# Allow widevine hidl process access widevine kernel driver
allow hal_drm_widevine widevine_drv_device:chr_file { getattr open read ioctl write };

# Allow widevine hidl process to read vendor_mtk_soc_prop
get_prop(hal_drm_widevine, vendor_mtk_soc_prop)
