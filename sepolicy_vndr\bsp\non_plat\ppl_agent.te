# ==============================================
# Policy File of /vendor/bin/ppl_agent Executable File

# ==============================================
# Common SEPolicy Rule
# ==============================================

type ppl_agent_exec, exec_type, file_type, vendor_file_type;

init_daemon_domain(ppl_agent)

# Date : 2014/10/16
# Operation : QC
# Purpose : [Privacy protection lock][ppl_agent call FileOp_BackupToBinRegionForDM to do nvram backup]
# Package name : com.mediatek.ppl
allow ppl_agent nvram_device:blk_file rw_file_perms;

# Data : 2014/10/24
# Operation : Migration
# Purpose : [Privacy protection lock][ppl_agent need access nvram data file for backup restore function]
# Package name : com.mediatek.ppl
allow ppl_agent nvram_data_file:dir create_dir_perms;
allow ppl_agent nvram_data_file:file create_file_perms;
allow ppl_agent nvram_data_file:lnk_file read;
allow ppl_agent nvdata_file:lnk_file read;
allow ppl_agent nvdata_file:dir create_dir_perms;
allow ppl_agent nvdata_file:file create_file_perms;

# Data : 2014/10/31
# Operation : QC
# Purpose : [Privacy protection lock][ppl_agent need access nvram data file for backup restore function on MT6582]
# Package name : ServiceManager
allow ppl_agent nvram_device:chr_file rw_file_perms;

# Data : 2015/10/09
# Operation : IT
# Purpose : [Privacy protection lock][ppl_agent need access ppl data file for backup restore function on MT6577]
# Package name : ppl_agent
allow ppl_agent ppl_block_device:blk_file rw_file_perms;

# Data : 2015/10/16
# Operation : QC
# Purpose : [Privacy protection lock][ppl_agent need access nvcfg ext4 partiton ppl on MT6797]
# Package name : com.mediatek.ppl
allow ppl_agent nvcfg_file:dir create_dir_perms;
allow ppl_agent nvcfg_file:file create_file_perms;

# Data : 2018/05/23
# Operation : QC
# Purpose : [Privacy protection lock]
allow ppl_agent mnt_vendor_file:dir search;

# Data : 2018/06/12
# Operation : QC
# Purpose : [Privacy protection lock]
allow ppl_agent proc_cmdline:file r_file_perms;
allow ppl_agent sysfs_dt_firmware_android:dir search;

# Data: 2018/08/02
# Operation: iT
# Purpose : [Privacy protection lock]
allow ppl_agent block_device:dir search;

hal_server_domain(ppl_agent, mtk_hal_pplagent)
