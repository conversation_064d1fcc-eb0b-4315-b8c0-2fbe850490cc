# ==============================================
# Policy File of /system/bin/volte_imcb Executable File

# ==============================================
# Type Declaration
# ==============================================
type volte_imcb, domain, mtkimsmddomain;
type volte_imcb_exec, exec_type, file_type, vendor_file_type;
type volte_imsa_socket, file_type;
type volte_imsvt_socket, file_type;

# ==============================================
# Common SEPolicy Rule
# ==============================================
#permissive volte_imcb;
init_daemon_domain(volte_imcb)
net_domain(volte_imcb)

# Date : WK14.42
# Operation : Migration
# Purpose : for VoLTE L early bring up and first call
allow volte_imcb node:tcp_socket node_bind;
allow volte_imcb port:tcp_socket name_bind;
allow volte_imcb self:tcp_socket { bind create setopt accept listen };
allow volte_imcb self:tcp_socket { read getattr };
allow volte_imcb self:tcp_socket write;
allow volte_imcb self:capability { setuid setgid };

# Date : 2015/8/5
# Operation : M Migration
# Purpose : For imcb connect to ua by local socket
unix_socket_connect(volte_imcb, volte_ua, volte_ua)

allow volte_imcb volte_imcb_socket:sock_file write;
allow volte_imcb volte_ut_socket:sock_file write;

# Dtae : WK15.42
# Operation : ViLTE Migration
# Purpose : For open socket device to vtservice connect

# Date : 2016/12/14
# Purpose : TRM
set_prop(volte_imcb, vendor_mtk_md_volte_prop)

# to NETD
allow volte_imcb netd:unix_stream_socket connectto;
allow volte_imcb netd_socket:sock_file write;
allow netd volte_imcb:fd use;
allow netd volte_imcb:tcp_socket { read write setopt getopt };
allow netd volte_imcb:udp_socket {read write setopt getopt};

# Date : 2020/02/24
# Purpose : pttyims
allow volte_imcb mtk_radio_device:dir w_dir_perms;
allow volte_imcb mtk_radio_device:lnk_file create_file_perms;
allow volte_imcb devpts:chr_file setattr;
allow volte_imcb self:capability2 wake_alarm;
allow volte_imcb sysfs_ccci:dir search;
allow volte_imcb sysfs_ccci:file r_file_perms;
allow volte_imcb ccci_device:chr_file rw_file_perms;

