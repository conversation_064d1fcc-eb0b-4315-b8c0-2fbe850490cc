07-20 13:56:38.304  2664  2664 W lowpool[15]: type=1400 audit(0.0:259): avc:  denied  { search } for  name="checkin" dev="dm-38" ino=2716 scontext=u:r:priv_app:s0:c512,c768 tcontext=u:object_r:checkin_data_file:s0:c512,c768 tclass=dir permissive=0 app=com.google.android.gms
07-20 13:56:42.832   580   580 E SELinux : avc:  denied  { add } for interface=vendor.oplus.hardware.cammidasservice::IMIDASService sid=u:r:mtk_hal_camera:s0 pid=5431 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:hal_camera_oplus_hwservice:s0 tclass=hwservice_manager permissive=0
07-20 13:56:47.855   580   580 E SELinux : avc:  denied  { add } for interface=vendor.oplus.hardware.cammidasservice::IMIDASService sid=u:r:mtk_hal_camera:s0 pid=5453 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:hal_camera_oplus_hwservice:s0 tclass=hwservice_manager permissive=0
07-20 13:56:48.312  2664  2664 W lowpool[11]: type=1400 audit(0.0:260): avc:  denied  { search } for  name="checkin" dev="dm-38" ino=2716 scontext=u:r:priv_app:s0:c512,c768 tcontext=u:object_r:checkin_data_file:s0:c512,c768 tclass=dir permissive=0 app=com.google.android.gms
07-20 13:56:52.843   580   580 E SELinux : avc:  denied  { add } for interface=vendor.oplus.hardware.cammidasservice::IMIDASService sid=u:r:mtk_hal_camera:s0 pid=5479 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:hal_camera_oplus_hwservice:s0 tclass=hwservice_manager permissive=0
07-20 13:56:57.845   580   580 E SELinux : avc:  denied  { add } for interface=vendor.oplus.hardware.cammidasservice::IMIDASService sid=u:r:mtk_hal_camera:s0 pid=5491 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:hal_camera_oplus_hwservice:s0 tclass=hwservice_manager permissive=0
07-20 13:57:02.880   580   580 E SELinux : avc:  denied  { add } for interface=vendor.oplus.hardware.cammidasservice::IMIDASService sid=u:r:mtk_hal_camera:s0 pid=5502 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:hal_camera_oplus_hwservice:s0 tclass=hwservice_manager permissive=0
07-20 13:57:07.300  2664  2664 W lowpool[17]: type=1400 audit(0.0:261): avc:  denied  { search } for  name="checkin" dev="dm-38" ino=2716 scontext=u:r:priv_app:s0:c512,c768 tcontext=u:object_r:checkin_data_file:s0:c512,c768 tclass=dir permissive=0 app=com.google.android.gms
07-20 13:57:07.857   580   580 E SELinux : avc:  denied  { add } for interface=vendor.oplus.hardware.cammidasservice::IMIDASService sid=u:r:mtk_hal_camera:s0 pid=5584 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:hal_camera_oplus_hwservice:s0 tclass=hwservice_manager permissive=0
07-20 13:57:08.324  2664  2664 W lowpool[12]: type=1400 audit(0.0:262): avc:  denied  { search } for  name="checkin" dev="dm-38" ino=2716 scontext=u:r:priv_app:s0:c512,c768 tcontext=u:object_r:checkin_data_file:s0:c512,c768 tclass=dir permissive=0 app=com.google.android.gms
07-20 13:57:08.996  2664  2664 W lowpool[17]: type=1400 audit(0.0:263): avc:  denied  { search } for  name="checkin" dev="dm-38" ino=2716 scontext=u:r:priv_app:s0:c512,c768 tcontext=u:object_r:checkin_data_file:s0:c512,c768 tclass=dir permissive=0 app=com.google.android.gms
07-20 13:57:11.108  2664  2664 W lowpool[21]: type=1400 audit(0.0:264): avc:  denied  { search } for  name="checkin" dev="dm-38" ino=2716 scontext=u:r:priv_app:s0:c512,c768 tcontext=u:object_r:checkin_data_file:s0:c512,c768 tclass=dir permissive=0 app=com.google.android.gms
07-20 13:57:11.144  2664  2664 W lowpool[21]: type=1400 audit(0.0:265): avc:  denied  { search } for  name="checkin" dev="dm-38" ino=2716 scontext=u:r:priv_app:s0:c512,c768 tcontext=u:object_r:checkin_data_file:s0:c512,c768 tclass=dir permissive=0 app=com.google.android.gms
07-20 13:57:11.492  3789  3789 W binder:3789_4: type=1400 audit(0.0:266): avc:  denied  { read } for  name="net" dev="sysfs" ino=26508 scontext=u:r:priv_app:s0:c512,c768 tcontext=u:object_r:sysfs_net:s0 tclass=dir permissive=0 app=com.google.android.gms
07-20 13:57:12.858   580   580 E SELinux : avc:  denied  { add } for interface=vendor.oplus.hardware.cammidasservice::IMIDASService sid=u:r:mtk_hal_camera:s0 pid=5822 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:hal_camera_oplus_hwservice:s0 tclass=hwservice_manager permissive=0
07-20 13:57:16.196  2761  2761 W highpool[12]: type=1400 audit(0.0:267): avc:  denied  { search } for  name="checkin" dev="dm-38" ino=2716 scontext=u:r:priv_app:s0:c512,c768 tcontext=u:object_r:checkin_data_file:s0:c512,c768 tclass=dir permissive=0 app=com.google.android.gms
07-20 13:57:17.867   580   580 E SELinux : avc:  denied  { add } for interface=vendor.oplus.hardware.cammidasservice::IMIDASService sid=u:r:mtk_hal_camera:s0 pid=5903 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:hal_camera_oplus_hwservice:s0 tclass=hwservice_manager permissive=0
07-20 13:57:21.252  2761  2761 W highpool[9]: type=1400 audit(0.0:268): avc:  denied  { search } for  name="checkin" dev="dm-38" ino=2716 scontext=u:r:priv_app:s0:c512,c768 tcontext=u:object_r:checkin_data_file:s0:c512,c768 tclass=dir permissive=0 app=com.google.android.gms
07-20 13:57:22.867   580   580 E SELinux : avc:  denied  { add } for interface=vendor.oplus.hardware.cammidasservice::IMIDASService sid=u:r:mtk_hal_camera:s0 pid=5914 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:hal_camera_oplus_hwservice:s0 tclass=hwservice_manager permissive=0
07-20 13:57:27.876   580   580 E SELinux : avc:  denied  { add } for interface=vendor.oplus.hardware.cammidasservice::IMIDASService sid=u:r:mtk_hal_camera:s0 pid=5938 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:hal_camera_oplus_hwservice:s0 tclass=hwservice_manager permissive=0
07-20 13:57:29.460  1543  1543 W kworker/u24:25: type=1400 audit(0.0:269): avc:  denied  { search } for  name="adb" dev="dm-38" ino=363 scontext=u:r:kernel:s0 tcontext=u:object_r:adb_data_file:s0 tclass=dir permissive=0
07-20 13:57:32.883   580   580 E SELinux : avc:  denied  { add } for interface=vendor.oplus.hardware.cammidasservice::IMIDASService sid=u:r:mtk_hal_camera:s0 pid=5965 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:hal_camera_oplus_hwservice:s0 tclass=hwservice_manager permissive=0
07-20 13:57:37.940   580   580 E SELinux : avc:  denied  { add } for interface=vendor.oplus.hardware.cammidasservice::IMIDASService sid=u:r:mtk_hal_camera:s0 pid=5977 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:hal_camera_oplus_hwservice:s0 tclass=hwservice_manager permissive=0
07-20 13:57:42.908   580   580 E SELinux : avc:  denied  { add } for interface=vendor.oplus.hardware.cammidasservice::IMIDASService sid=u:r:mtk_hal_camera:s0 pid=5986 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:hal_camera_oplus_hwservice:s0 tclass=hwservice_manager permissive=0
07-20 13:57:47.954   580   580 E SELinux : avc:  denied  { add } for interface=vendor.oplus.hardware.cammidasservice::IMIDASService sid=u:r:mtk_hal_camera:s0 pid=5995 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:hal_camera_oplus_hwservice:s0 tclass=hwservice_manager permissive=0
07-20 13:57:48.340  2664  2664 W lowpool[22]: type=1400 audit(0.0:270): avc:  denied  { search } for  name="checkin" dev="dm-38" ino=2716 scontext=u:r:priv_app:s0:c512,c768 tcontext=u:object_r:checkin_data_file:s0:c512,c768 tclass=dir permissive=0 app=com.google.android.gms
07-20 13:57:52.924   580   580 E SELinux : avc:  denied  { add } for interface=vendor.oplus.hardware.cammidasservice::IMIDASService sid=u:r:mtk_hal_camera:s0 pid=6017 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:hal_camera_oplus_hwservice:s0 tclass=hwservice_manager permissive=0
07-20 13:57:57.973   580   580 E SELinux : avc:  denied  { add } for interface=vendor.oplus.hardware.cammidasservice::IMIDASService sid=u:r:mtk_hal_camera:s0 pid=6026 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:hal_camera_oplus_hwservice:s0 tclass=hwservice_manager permissive=0
07-20 13:58:02.991   580   580 E SELinux : avc:  denied  { add } for interface=vendor.oplus.hardware.cammidasservice::IMIDASService sid=u:r:mtk_hal_camera:s0 pid=6035 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:hal_camera_oplus_hwservice:s0 tclass=hwservice_manager permissive=0
07-20 13:58:08.000   580   580 E SELinux : avc:  denied  { add } for interface=vendor.oplus.hardware.cammidasservice::IMIDASService sid=u:r:mtk_hal_camera:s0 pid=6043 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:hal_camera_oplus_hwservice:s0 tclass=hwservice_manager permissive=0
07-20 13:58:13.008   580   580 E SELinux : avc:  denied  { add } for interface=vendor.oplus.hardware.cammidasservice::IMIDASService sid=u:r:mtk_hal_camera:s0 pid=6053 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:hal_camera_oplus_hwservice:s0 tclass=hwservice_manager permissive=0
07-20 13:58:17.986   580   580 E SELinux : avc:  denied  { add } for interface=vendor.oplus.hardware.cammidasservice::IMIDASService sid=u:r:mtk_hal_camera:s0 pid=6066 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:hal_camera_oplus_hwservice:s0 tclass=hwservice_manager permissive=0
07-20 13:58:22.991   580   580 E SELinux : avc:  denied  { add } for interface=vendor.oplus.hardware.cammidasservice::IMIDASService sid=u:r:mtk_hal_camera:s0 pid=6074 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:hal_camera_oplus_hwservice:s0 tclass=hwservice_manager permissive=0
07-20 13:58:25.748  2664  2664 W lowpool[26]: type=1400 audit(0.0:271): avc:  denied  { search } for  name="checkin" dev="dm-38" ino=2716 scontext=u:r:priv_app:s0:c512,c768 tcontext=u:object_r:checkin_data_file:s0:c512,c768 tclass=dir permissive=0 app=com.google.android.gms
07-20 13:58:25.812  2664  2664 W lowpool[27]: type=1400 audit(0.0:272): avc:  denied  { search } for  name="checkin" dev="dm-38" ino=2716 scontext=u:r:priv_app:s0:c512,c768 tcontext=u:object_r:checkin_data_file:s0:c512,c768 tclass=dir permissive=0 app=com.google.android.gms
07-20 13:58:28.037   580   580 E SELinux : avc:  denied  { add } for interface=vendor.oplus.hardware.cammidasservice::IMIDASService sid=u:r:mtk_hal_camera:s0 pid=6091 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:hal_camera_oplus_hwservice:s0 tclass=hwservice_manager permissive=0
07-20 13:58:30.944  2761  2761 W highpool[9]: type=1400 audit(0.0:273): avc:  denied  { search } for  name="checkin" dev="dm-38" ino=2716 scontext=u:r:priv_app:s0:c512,c768 tcontext=u:object_r:checkin_data_file:s0:c512,c768 tclass=dir permissive=0 app=com.google.android.gms
07-20 13:58:33.053   580   580 E SELinux : avc:  denied  { add } for interface=vendor.oplus.hardware.cammidasservice::IMIDASService sid=u:r:mtk_hal_camera:s0 pid=6104 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:hal_camera_oplus_hwservice:s0 tclass=hwservice_manager permissive=0
07-20 13:58:36.572  2761  2761 W highpool[9]: type=1400 audit(0.0:274): avc:  denied  { search } for  name="checkin" dev="dm-38" ino=2716 scontext=u:r:priv_app:s0:c512,c768 tcontext=u:object_r:checkin_data_file:s0:c512,c768 tclass=dir permissive=0 app=com.google.android.gms
07-20 13:58:38.061   580   580 E SELinux : avc:  denied  { add } for interface=vendor.oplus.hardware.cammidasservice::IMIDASService sid=u:r:mtk_hal_camera:s0 pid=6117 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:hal_camera_oplus_hwservice:s0 tclass=hwservice_manager permissive=0
07-20 13:58:43.076   580   580 E SELinux : avc:  denied  { add } for interface=vendor.oplus.hardware.cammidasservice::IMIDASService sid=u:r:mtk_hal_camera:s0 pid=6126 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:hal_camera_oplus_hwservice:s0 tclass=hwservice_manager permissive=0
07-20 13:58:48.091   580   580 E SELinux : avc:  denied  { add } for interface=vendor.oplus.hardware.cammidasservice::IMIDASService sid=u:r:mtk_hal_camera:s0 pid=6136 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:hal_camera_oplus_hwservice:s0 tclass=hwservice_manager permissive=0
07-20 13:58:53.064   580   580 E SELinux : avc:  denied  { add } for interface=vendor.oplus.hardware.cammidasservice::IMIDASService sid=u:r:mtk_hal_camera:s0 pid=6170 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:hal_camera_oplus_hwservice:s0 tclass=hwservice_manager permissive=0
07-20 13:58:58.065   580   580 E SELinux : avc:  denied  { add } for interface=vendor.oplus.hardware.cammidasservice::IMIDASService sid=u:r:mtk_hal_camera:s0 pid=6206 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:hal_camera_oplus_hwservice:s0 tclass=hwservice_manager permissive=0
07-20 13:59:03.112   580   580 E SELinux : avc:  denied  { add } for interface=vendor.oplus.hardware.cammidasservice::IMIDASService sid=u:r:mtk_hal_camera:s0 pid=6218 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:hal_camera_oplus_hwservice:s0 tclass=hwservice_manager permissive=0
07-20 13:59:08.125   580   580 E SELinux : avc:  denied  { add } for interface=vendor.oplus.hardware.cammidasservice::IMIDASService sid=u:r:mtk_hal_camera:s0 pid=6227 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:hal_camera_oplus_hwservice:s0 tclass=hwservice_manager permissive=0
07-20 13:59:08.360  2664  2664 W lowpool[32]: type=1400 audit(0.0:275): avc:  denied  { search } for  name="checkin" dev="dm-38" ino=2716 scontext=u:r:priv_app:s0:c512,c768 tcontext=u:object_r:checkin_data_file:s0:c512,c768 tclass=dir permissive=0 app=com.google.android.gms
07-20 13:59:13.097   580   580 E SELinux : avc:  denied  { add } for interface=vendor.oplus.hardware.cammidasservice::IMIDASService sid=u:r:mtk_hal_camera:s0 pid=6250 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:hal_camera_oplus_hwservice:s0 tclass=hwservice_manager permissive=0
07-20 13:59:18.138   580   580 E SELinux : avc:  denied  { add } for interface=vendor.oplus.hardware.cammidasservice::IMIDASService sid=u:r:mtk_hal_camera:s0 pid=6259 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:hal_camera_oplus_hwservice:s0 tclass=hwservice_manager permissive=0
07-20 13:59:23.156   580   580 E SELinux : avc:  denied  { add } for interface=vendor.oplus.hardware.cammidasservice::IMIDASService sid=u:r:mtk_hal_camera:s0 pid=6268 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:hal_camera_oplus_hwservice:s0 tclass=hwservice_manager permissive=0
07-20 13:59:28.170   580   580 E SELinux : avc:  denied  { add } for interface=vendor.oplus.hardware.cammidasservice::IMIDASService sid=u:r:mtk_hal_camera:s0 pid=6277 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:hal_camera_oplus_hwservice:s0 tclass=hwservice_manager permissive=0
07-20 13:59:33.186   580   580 E SELinux : avc:  denied  { add } for interface=vendor.oplus.hardware.cammidasservice::IMIDASService sid=u:r:mtk_hal_camera:s0 pid=6286 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:hal_camera_oplus_hwservice:s0 tclass=hwservice_manager permissive=0
07-20 13:59:38.190   580   580 E SELinux : avc:  denied  { add } for interface=vendor.oplus.hardware.cammidasservice::IMIDASService sid=u:r:mtk_hal_camera:s0 pid=6295 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:hal_camera_oplus_hwservice:s0 tclass=hwservice_manager permissive=0
07-20 13:59:43.208   580   580 E SELinux : avc:  denied  { add } for interface=vendor.oplus.hardware.cammidasservice::IMIDASService sid=u:r:mtk_hal_camera:s0 pid=6304 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:hal_camera_oplus_hwservice:s0 tclass=hwservice_manager permissive=0
07-20 13:59:48.213   580   580 E SELinux : avc:  denied  { add } for interface=vendor.oplus.hardware.cammidasservice::IMIDASService sid=u:r:mtk_hal_camera:s0 pid=6317 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:hal_camera_oplus_hwservice:s0 tclass=hwservice_manager permissive=0
07-20 13:59:53.235   580   580 E SELinux : avc:  denied  { add } for interface=vendor.oplus.hardware.cammidasservice::IMIDASService sid=u:r:mtk_hal_camera:s0 pid=6327 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:hal_camera_oplus_hwservice:s0 tclass=hwservice_manager permissive=0
07-20 13:59:58.247   580   580 E SELinux : avc:  denied  { add } for interface=vendor.oplus.hardware.cammidasservice::IMIDASService sid=u:r:mtk_hal_camera:s0 pid=6339 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:hal_camera_oplus_hwservice:s0 tclass=hwservice_manager permissive=0
07-20 14:00:03.246   580   580 E SELinux : avc:  denied  { add } for interface=vendor.oplus.hardware.cammidasservice::IMIDASService sid=u:r:mtk_hal_camera:s0 pid=6348 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:hal_camera_oplus_hwservice:s0 tclass=hwservice_manager permissive=0
07-20 14:00:08.275   580   580 E SELinux : avc:  denied  { add } for interface=vendor.oplus.hardware.cammidasservice::IMIDASService sid=u:r:mtk_hal_camera:s0 pid=6357 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:hal_camera_oplus_hwservice:s0 tclass=hwservice_manager permissive=0
07-20 14:00:13.290   580   580 E SELinux : avc:  denied  { add } for interface=vendor.oplus.hardware.cammidasservice::IMIDASService sid=u:r:mtk_hal_camera:s0 pid=6369 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:hal_camera_oplus_hwservice:s0 tclass=hwservice_manager permissive=0
07-20 14:00:18.307   580   580 E SELinux : avc:  denied  { add } for interface=vendor.oplus.hardware.cammidasservice::IMIDASService sid=u:r:mtk_hal_camera:s0 pid=6378 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:hal_camera_oplus_hwservice:s0 tclass=hwservice_manager permissive=0
07-20 14:00:23.322   580   580 E SELinux : avc:  denied  { add } for interface=vendor.oplus.hardware.cammidasservice::IMIDASService sid=u:r:mtk_hal_camera:s0 pid=6389 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:hal_camera_oplus_hwservice:s0 tclass=hwservice_manager permissive=0
07-20 14:00:28.328   580   580 E SELinux : avc:  denied  { add } for interface=vendor.oplus.hardware.cammidasservice::IMIDASService sid=u:r:mtk_hal_camera:s0 pid=6398 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:hal_camera_oplus_hwservice:s0 tclass=hwservice_manager permissive=0
07-20 14:00:33.349   580   580 E SELinux : avc:  denied  { add } for interface=vendor.oplus.hardware.cammidasservice::IMIDASService sid=u:r:mtk_hal_camera:s0 pid=6407 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:hal_camera_oplus_hwservice:s0 tclass=hwservice_manager permissive=0
07-20 14:00:38.359   580   580 E SELinux : avc:  denied  { add } for interface=vendor.oplus.hardware.cammidasservice::IMIDASService sid=u:r:mtk_hal_camera:s0 pid=6424 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:hal_camera_oplus_hwservice:s0 tclass=hwservice_manager permissive=0
07-20 14:00:43.374   580   580 E SELinux : avc:  denied  { add } for interface=vendor.oplus.hardware.cammidasservice::IMIDASService sid=u:r:mtk_hal_camera:s0 pid=6433 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:hal_camera_oplus_hwservice:s0 tclass=hwservice_manager permissive=0
07-20 14:00:48.388   580   580 E SELinux : avc:  denied  { add } for interface=vendor.oplus.hardware.cammidasservice::IMIDASService sid=u:r:mtk_hal_camera:s0 pid=6446 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:hal_camera_oplus_hwservice:s0 tclass=hwservice_manager permissive=0
07-20 14:00:53.402   580   580 E SELinux : avc:  denied  { add } for interface=vendor.oplus.hardware.cammidasservice::IMIDASService sid=u:r:mtk_hal_camera:s0 pid=6455 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:hal_camera_oplus_hwservice:s0 tclass=hwservice_manager permissive=0
07-20 14:00:58.421   580   580 E SELinux : avc:  denied  { add } for interface=vendor.oplus.hardware.cammidasservice::IMIDASService sid=u:r:mtk_hal_camera:s0 pid=6465 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:hal_camera_oplus_hwservice:s0 tclass=hwservice_manager permissive=0
07-20 14:01:03.433   580   580 E SELinux : avc:  denied  { add } for interface=vendor.oplus.hardware.cammidasservice::IMIDASService sid=u:r:mtk_hal_camera:s0 pid=6477 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:hal_camera_oplus_hwservice:s0 tclass=hwservice_manager permissive=0
07-20 14:01:08.435   580   580 E SELinux : avc:  denied  { add } for interface=vendor.oplus.hardware.cammidasservice::IMIDASService sid=u:r:mtk_hal_camera:s0 pid=6490 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:hal_camera_oplus_hwservice:s0 tclass=hwservice_manager permissive=0
07-20 14:01:13.455   580   580 E SELinux : avc:  denied  { add } for interface=vendor.oplus.hardware.cammidasservice::IMIDASService sid=u:r:mtk_hal_camera:s0 pid=6502 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:hal_camera_oplus_hwservice:s0 tclass=hwservice_manager permissive=0
07-20 14:01:18.470   580   580 E SELinux : avc:  denied  { add } for interface=vendor.oplus.hardware.cammidasservice::IMIDASService sid=u:r:mtk_hal_camera:s0 pid=6511 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:hal_camera_oplus_hwservice:s0 tclass=hwservice_manager permissive=0
07-20 14:01:23.487   580   580 E SELinux : avc:  denied  { add } for interface=vendor.oplus.hardware.cammidasservice::IMIDASService sid=u:r:mtk_hal_camera:s0 pid=6521 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:hal_camera_oplus_hwservice:s0 tclass=hwservice_manager permissive=0
07-20 14:01:28.503   580   580 E SELinux : avc:  denied  { add } for interface=vendor.oplus.hardware.cammidasservice::IMIDASService sid=u:r:mtk_hal_camera:s0 pid=6530 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:hal_camera_oplus_hwservice:s0 tclass=hwservice_manager permissive=0
07-20 14:01:33.519   580   580 E SELinux : avc:  denied  { add } for interface=vendor.oplus.hardware.cammidasservice::IMIDASService sid=u:r:mtk_hal_camera:s0 pid=6539 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:hal_camera_oplus_hwservice:s0 tclass=hwservice_manager permissive=0
07-20 14:01:38.527   580   580 E SELinux : avc:  denied  { add } for interface=vendor.oplus.hardware.cammidasservice::IMIDASService sid=u:r:mtk_hal_camera:s0 pid=6549 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:hal_camera_oplus_hwservice:s0 tclass=hwservice_manager permissive=0
07-20 14:01:43.544   580   580 E SELinux : avc:  denied  { add } for interface=vendor.oplus.hardware.cammidasservice::IMIDASService sid=u:r:mtk_hal_camera:s0 pid=6559 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:hal_camera_oplus_hwservice:s0 tclass=hwservice_manager permissive=0
07-20 14:01:48.384  2664  2664 W lowpool[36]: type=1400 audit(0.0:276): avc:  denied  { search } for  name="checkin" dev="dm-38" ino=2716 scontext=u:r:priv_app:s0:c512,c768 tcontext=u:object_r:checkin_data_file:s0:c512,c768 tclass=dir permissive=0 app=com.google.android.gms
07-20 14:01:48.452  2664  2664 W lowpool[36]: type=1400 audit(0.0:277): avc:  denied  { search } for  name="checkin" dev="dm-38" ino=2716 scontext=u:r:priv_app:s0:c512,c768 tcontext=u:object_r:checkin_data_file:s0:c512,c768 tclass=dir permissive=0 app=com.google.android.gms
07-20 14:01:48.524   580   580 E SELinux : avc:  denied  { add } for interface=vendor.oplus.hardware.cammidasservice::IMIDASService sid=u:r:mtk_hal_camera:s0 pid=6586 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:hal_camera_oplus_hwservice:s0 tclass=hwservice_manager permissive=0
07-20 14:01:48.560  2664  2664 W lowpool[37]: type=1400 audit(0.0:278): avc:  denied  { search } for  name="checkin" dev="dm-38" ino=2716 scontext=u:r:priv_app:s0:c512,c768 tcontext=u:object_r:checkin_data_file:s0:c512,c768 tclass=dir permissive=0 app=com.google.android.gms
07-20 14:01:48.604  2761  2761 W highpool[9]: type=1400 audit(0.0:279): avc:  denied  { search } for  name="checkin" dev="dm-38" ino=2716 scontext=u:r:priv_app:s0:c512,c768 tcontext=u:object_r:checkin_data_file:s0:c512,c768 tclass=dir permissive=0 app=com.google.android.gms
07-20 14:01:48.616  2761  2761 W highpool[9]: type=1400 audit(0.0:280): avc:  denied  { search } for  name="checkin" dev="dm-38" ino=2716 scontext=u:r:priv_app:s0:c512,c768 tcontext=u:object_r:checkin_data_file:s0:c512,c768 tclass=dir permissive=0 app=com.google.android.gms
07-20 14:01:53.554   580   580 E SELinux : avc:  denied  { add } for interface=vendor.oplus.hardware.cammidasservice::IMIDASService sid=u:r:mtk_hal_camera:s0 pid=6601 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:hal_camera_oplus_hwservice:s0 tclass=hwservice_manager permissive=0
07-20 14:01:58.573   580   580 E SELinux : avc:  denied  { add } for interface=vendor.oplus.hardware.cammidasservice::IMIDASService sid=u:r:mtk_hal_camera:s0 pid=6612 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:hal_camera_oplus_hwservice:s0 tclass=hwservice_manager permissive=0
07-20 14:02:03.550   580   580 E SELinux : avc:  denied  { add } for interface=vendor.oplus.hardware.cammidasservice::IMIDASService sid=u:r:mtk_hal_camera:s0 pid=6622 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:hal_camera_oplus_hwservice:s0 tclass=hwservice_manager permissive=0
07-20 14:02:08.554   580   580 E SELinux : avc:  denied  { add } for interface=vendor.oplus.hardware.cammidasservice::IMIDASService sid=u:r:mtk_hal_camera:s0 pid=6630 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:hal_camera_oplus_hwservice:s0 tclass=hwservice_manager permissive=0
07-20 14:02:13.594   580   580 E SELinux : avc:  denied  { add } for interface=vendor.oplus.hardware.cammidasservice::IMIDASService sid=u:r:mtk_hal_camera:s0 pid=6641 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:hal_camera_oplus_hwservice:s0 tclass=hwservice_manager permissive=0
07-20 14:02:18.569   580   580 E SELinux : avc:  denied  { add } for interface=vendor.oplus.hardware.cammidasservice::IMIDASService sid=u:r:mtk_hal_camera:s0 pid=6652 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:hal_camera_oplus_hwservice:s0 tclass=hwservice_manager permissive=0
07-20 14:02:23.575   580   580 E SELinux : avc:  denied  { add } for interface=vendor.oplus.hardware.cammidasservice::IMIDASService sid=u:r:mtk_hal_camera:s0 pid=6660 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:hal_camera_oplus_hwservice:s0 tclass=hwservice_manager permissive=0
07-20 14:02:28.608   580   580 E SELinux : avc:  denied  { add } for interface=vendor.oplus.hardware.cammidasservice::IMIDASService sid=u:r:mtk_hal_camera:s0 pid=6670 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:hal_camera_oplus_hwservice:s0 tclass=hwservice_manager permissive=0
07-20 14:02:33.603   580   580 E SELinux : avc:  denied  { add } for interface=vendor.oplus.hardware.cammidasservice::IMIDASService sid=u:r:mtk_hal_camera:s0 pid=6679 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:hal_camera_oplus_hwservice:s0 tclass=hwservice_manager permissive=0
07-20 14:02:38.648   580   580 E SELinux : avc:  denied  { add } for interface=vendor.oplus.hardware.cammidasservice::IMIDASService sid=u:r:mtk_hal_camera:s0 pid=6688 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:hal_camera_oplus_hwservice:s0 tclass=hwservice_manager permissive=0
07-20 14:02:43.661   580   580 E SELinux : avc:  denied  { add } for interface=vendor.oplus.hardware.cammidasservice::IMIDASService sid=u:r:mtk_hal_camera:s0 pid=6697 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:hal_camera_oplus_hwservice:s0 tclass=hwservice_manager permissive=0
07-20 14:02:48.664   580   580 E SELinux : avc:  denied  { add } for interface=vendor.oplus.hardware.cammidasservice::IMIDASService sid=u:r:mtk_hal_camera:s0 pid=6706 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:hal_camera_oplus_hwservice:s0 tclass=hwservice_manager permissive=0
07-20 14:02:53.682   580   580 E SELinux : avc:  denied  { add } for interface=vendor.oplus.hardware.cammidasservice::IMIDASService sid=u:r:mtk_hal_camera:s0 pid=6715 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:hal_camera_oplus_hwservice:s0 tclass=hwservice_manager permissive=0
07-20 14:02:58.651   580   580 E SELinux : avc:  denied  { add } for interface=vendor.oplus.hardware.cammidasservice::IMIDASService sid=u:r:mtk_hal_camera:s0 pid=6724 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:hal_camera_oplus_hwservice:s0 tclass=hwservice_manager permissive=0
07-20 14:03:03.697   580   580 E SELinux : avc:  denied  { add } for interface=vendor.oplus.hardware.cammidasservice::IMIDASService sid=u:r:mtk_hal_camera:s0 pid=6733 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:hal_camera_oplus_hwservice:s0 tclass=hwservice_manager permissive=0
07-20 14:03:08.707   580   580 E SELinux : avc:  denied  { add } for interface=vendor.oplus.hardware.cammidasservice::IMIDASService sid=u:r:mtk_hal_camera:s0 pid=6742 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:hal_camera_oplus_hwservice:s0 tclass=hwservice_manager permissive=0
07-20 14:03:13.728   580   580 E SELinux : avc:  denied  { add } for interface=vendor.oplus.hardware.cammidasservice::IMIDASService sid=u:r:mtk_hal_camera:s0 pid=6751 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:hal_camera_oplus_hwservice:s0 tclass=hwservice_manager permissive=0
07-20 14:03:18.733   580   580 E SELinux : avc:  denied  { add } for interface=vendor.oplus.hardware.cammidasservice::IMIDASService sid=u:r:mtk_hal_camera:s0 pid=6760 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:hal_camera_oplus_hwservice:s0 tclass=hwservice_manager permissive=0
07-20 14:03:23.746   580   580 E SELinux : avc:  denied  { add } for interface=vendor.oplus.hardware.cammidasservice::IMIDASService sid=u:r:mtk_hal_camera:s0 pid=6769 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:hal_camera_oplus_hwservice:s0 tclass=hwservice_manager permissive=0
07-20 14:03:28.758   580   580 E SELinux : avc:  denied  { add } for interface=vendor.oplus.hardware.cammidasservice::IMIDASService sid=u:r:mtk_hal_camera:s0 pid=6778 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:hal_camera_oplus_hwservice:s0 tclass=hwservice_manager permissive=0
07-20 14:03:33.771   580   580 E SELinux : avc:  denied  { add } for interface=vendor.oplus.hardware.cammidasservice::IMIDASService sid=u:r:mtk_hal_camera:s0 pid=6787 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:hal_camera_oplus_hwservice:s0 tclass=hwservice_manager permissive=0
07-20 14:03:38.777   580   580 E SELinux : avc:  denied  { add } for interface=vendor.oplus.hardware.cammidasservice::IMIDASService sid=u:r:mtk_hal_camera:s0 pid=6796 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:hal_camera_oplus_hwservice:s0 tclass=hwservice_manager permissive=0
07-20 14:03:43.788   580   580 E SELinux : avc:  denied  { add } for interface=vendor.oplus.hardware.cammidasservice::IMIDASService sid=u:r:mtk_hal_camera:s0 pid=6805 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:hal_camera_oplus_hwservice:s0 tclass=hwservice_manager permissive=0
07-20 14:03:48.801   580   580 E SELinux : avc:  denied  { add } for interface=vendor.oplus.hardware.cammidasservice::IMIDASService sid=u:r:mtk_hal_camera:s0 pid=6814 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:hal_camera_oplus_hwservice:s0 tclass=hwservice_manager permissive=0
07-20 14:03:53.776   580   580 E SELinux : avc:  denied  { add } for interface=vendor.oplus.hardware.cammidasservice::IMIDASService sid=u:r:mtk_hal_camera:s0 pid=6823 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:hal_camera_oplus_hwservice:s0 tclass=hwservice_manager permissive=0
07-20 14:03:58.824   580   580 E SELinux : avc:  denied  { add } for interface=vendor.oplus.hardware.cammidasservice::IMIDASService sid=u:r:mtk_hal_camera:s0 pid=6832 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:hal_camera_oplus_hwservice:s0 tclass=hwservice_manager permissive=0
07-20 14:04:03.825   580   580 E SELinux : avc:  denied  { add } for interface=vendor.oplus.hardware.cammidasservice::IMIDASService sid=u:r:mtk_hal_camera:s0 pid=6841 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:hal_camera_oplus_hwservice:s0 tclass=hwservice_manager permissive=0
07-20 14:04:08.850   580   580 E SELinux : avc:  denied  { add } for interface=vendor.oplus.hardware.cammidasservice::IMIDASService sid=u:r:mtk_hal_camera:s0 pid=6850 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:hal_camera_oplus_hwservice:s0 tclass=hwservice_manager permissive=0
07-20 14:04:13.870   580   580 E SELinux : avc:  denied  { add } for interface=vendor.oplus.hardware.cammidasservice::IMIDASService sid=u:r:mtk_hal_camera:s0 pid=6859 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:hal_camera_oplus_hwservice:s0 tclass=hwservice_manager permissive=0
07-20 14:04:18.871   580   580 E SELinux : avc:  denied  { add } for interface=vendor.oplus.hardware.cammidasservice::IMIDASService sid=u:r:mtk_hal_camera:s0 pid=6868 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:hal_camera_oplus_hwservice:s0 tclass=hwservice_manager permissive=0
07-20 14:04:23.898   580   580 E SELinux : avc:  denied  { add } for interface=vendor.oplus.hardware.cammidasservice::IMIDASService sid=u:r:mtk_hal_camera:s0 pid=6877 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:hal_camera_oplus_hwservice:s0 tclass=hwservice_manager permissive=0
07-20 14:04:28.915   580   580 E SELinux : avc:  denied  { add } for interface=vendor.oplus.hardware.cammidasservice::IMIDASService sid=u:r:mtk_hal_camera:s0 pid=6886 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:hal_camera_oplus_hwservice:s0 tclass=hwservice_manager permissive=0
07-20 14:04:33.933   580   580 E SELinux : avc:  denied  { add } for interface=vendor.oplus.hardware.cammidasservice::IMIDASService sid=u:r:mtk_hal_camera:s0 pid=6895 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:hal_camera_oplus_hwservice:s0 tclass=hwservice_manager permissive=0
07-20 14:04:38.944   580   580 E SELinux : avc:  denied  { add } for interface=vendor.oplus.hardware.cammidasservice::IMIDASService sid=u:r:mtk_hal_camera:s0 pid=6904 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:hal_camera_oplus_hwservice:s0 tclass=hwservice_manager permissive=0
07-20 14:04:43.956   580   580 E SELinux : avc:  denied  { add } for interface=vendor.oplus.hardware.cammidasservice::IMIDASService sid=u:r:mtk_hal_camera:s0 pid=6915 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:hal_camera_oplus_hwservice:s0 tclass=hwservice_manager permissive=0
07-20 14:04:48.971   580   580 E SELinux : avc:  denied  { add } for interface=vendor.oplus.hardware.cammidasservice::IMIDASService sid=u:r:mtk_hal_camera:s0 pid=6928 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:hal_camera_oplus_hwservice:s0 tclass=hwservice_manager permissive=0
07-20 14:04:53.990   580   580 E SELinux : avc:  denied  { add } for interface=vendor.oplus.hardware.cammidasservice::IMIDASService sid=u:r:mtk_hal_camera:s0 pid=6938 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:hal_camera_oplus_hwservice:s0 tclass=hwservice_manager permissive=0
07-20 14:04:58.995   580   580 E SELinux : avc:  denied  { add } for interface=vendor.oplus.hardware.cammidasservice::IMIDASService sid=u:r:mtk_hal_camera:s0 pid=6949 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:hal_camera_oplus_hwservice:s0 tclass=hwservice_manager permissive=0
07-20 14:05:00.092  2664  2664 W lowpool[37]: type=1400 audit(0.0:281): avc:  denied  { search } for  name="checkin" dev="dm-38" ino=2716 scontext=u:r:priv_app:s0:c512,c768 tcontext=u:object_r:checkin_data_file:s0:c512,c768 tclass=dir permissive=0 app=com.google.android.gms
07-20 14:05:04.011   580   580 E SELinux : avc:  denied  { add } for interface=vendor.oplus.hardware.cammidasservice::IMIDASService sid=u:r:mtk_hal_camera:s0 pid=6962 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:hal_camera_oplus_hwservice:s0 tclass=hwservice_manager permissive=0
07-20 14:05:09.025   580   580 E SELinux : avc:  denied  { add } for interface=vendor.oplus.hardware.cammidasservice::IMIDASService sid=u:r:mtk_hal_camera:s0 pid=6972 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:hal_camera_oplus_hwservice:s0 tclass=hwservice_manager permissive=0
07-20 14:05:14.039   580   580 E SELinux : avc:  denied  { add } for interface=vendor.oplus.hardware.cammidasservice::IMIDASService sid=u:r:mtk_hal_camera:s0 pid=6982 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:hal_camera_oplus_hwservice:s0 tclass=hwservice_manager permissive=0
07-20 14:05:19.056   580   580 E SELinux : avc:  denied  { add } for interface=vendor.oplus.hardware.cammidasservice::IMIDASService sid=u:r:mtk_hal_camera:s0 pid=6991 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:hal_camera_oplus_hwservice:s0 tclass=hwservice_manager permissive=0
07-20 14:05:24.071   580   580 E SELinux : avc:  denied  { add } for interface=vendor.oplus.hardware.cammidasservice::IMIDASService sid=u:r:mtk_hal_camera:s0 pid=7001 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:hal_camera_oplus_hwservice:s0 tclass=hwservice_manager permissive=0
07-20 14:05:29.090   580   580 E SELinux : avc:  denied  { add } for interface=vendor.oplus.hardware.cammidasservice::IMIDASService sid=u:r:mtk_hal_camera:s0 pid=7010 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:hal_camera_oplus_hwservice:s0 tclass=hwservice_manager permissive=0
07-20 14:05:34.104   580   580 E SELinux : avc:  denied  { add } for interface=vendor.oplus.hardware.cammidasservice::IMIDASService sid=u:r:mtk_hal_camera:s0 pid=7019 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:hal_camera_oplus_hwservice:s0 tclass=hwservice_manager permissive=0
07-20 14:05:39.119   580   580 E SELinux : avc:  denied  { add } for interface=vendor.oplus.hardware.cammidasservice::IMIDASService sid=u:r:mtk_hal_camera:s0 pid=7028 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:hal_camera_oplus_hwservice:s0 tclass=hwservice_manager permissive=0
07-20 14:05:44.133   580   580 E SELinux : avc:  denied  { add } for interface=vendor.oplus.hardware.cammidasservice::IMIDASService sid=u:r:mtk_hal_camera:s0 pid=7039 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:hal_camera_oplus_hwservice:s0 tclass=hwservice_manager permissive=0
07-20 14:05:49.145   580   580 E SELinux : avc:  denied  { add } for interface=vendor.oplus.hardware.cammidasservice::IMIDASService sid=u:r:mtk_hal_camera:s0 pid=7048 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:hal_camera_oplus_hwservice:s0 tclass=hwservice_manager permissive=0
07-20 14:05:54.155   580   580 E SELinux : avc:  denied  { add } for interface=vendor.oplus.hardware.cammidasservice::IMIDASService sid=u:r:mtk_hal_camera:s0 pid=7057 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:hal_camera_oplus_hwservice:s0 tclass=hwservice_manager permissive=0
07-20 14:05:59.164   580   580 E SELinux : avc:  denied  { add } for interface=vendor.oplus.hardware.cammidasservice::IMIDASService sid=u:r:mtk_hal_camera:s0 pid=7066 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:hal_camera_oplus_hwservice:s0 tclass=hwservice_manager permissive=0
07-20 14:06:04.181   580   580 E SELinux : avc:  denied  { add } for interface=vendor.oplus.hardware.cammidasservice::IMIDASService sid=u:r:mtk_hal_camera:s0 pid=7075 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:hal_camera_oplus_hwservice:s0 tclass=hwservice_manager permissive=0
07-20 14:06:09.188   580   580 E SELinux : avc:  denied  { add } for interface=vendor.oplus.hardware.cammidasservice::IMIDASService sid=u:r:mtk_hal_camera:s0 pid=7083 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:hal_camera_oplus_hwservice:s0 tclass=hwservice_manager permissive=0
07-20 14:06:14.203   580   580 E SELinux : avc:  denied  { add } for interface=vendor.oplus.hardware.cammidasservice::IMIDASService sid=u:r:mtk_hal_camera:s0 pid=7091 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:hal_camera_oplus_hwservice:s0 tclass=hwservice_manager permissive=0
07-20 14:06:19.176   580   580 E SELinux : avc:  denied  { add } for interface=vendor.oplus.hardware.cammidasservice::IMIDASService sid=u:r:mtk_hal_camera:s0 pid=7099 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:hal_camera_oplus_hwservice:s0 tclass=hwservice_manager permissive=0
07-20 14:06:24.220   580   580 E SELinux : avc:  denied  { add } for interface=vendor.oplus.hardware.cammidasservice::IMIDASService sid=u:r:mtk_hal_camera:s0 pid=7107 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:hal_camera_oplus_hwservice:s0 tclass=hwservice_manager permissive=0
07-20 14:06:29.242   580   580 E SELinux : avc:  denied  { add } for interface=vendor.oplus.hardware.cammidasservice::IMIDASService sid=u:r:mtk_hal_camera:s0 pid=7116 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:hal_camera_oplus_hwservice:s0 tclass=hwservice_manager permissive=0
07-20 14:06:34.248   580   580 E SELinux : avc:  denied  { add } for interface=vendor.oplus.hardware.cammidasservice::IMIDASService sid=u:r:mtk_hal_camera:s0 pid=7125 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:hal_camera_oplus_hwservice:s0 tclass=hwservice_manager permissive=0
07-20 14:06:39.268   580   580 E SELinux : avc:  denied  { add } for interface=vendor.oplus.hardware.cammidasservice::IMIDASService sid=u:r:mtk_hal_camera:s0 pid=7135 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:hal_camera_oplus_hwservice:s0 tclass=hwservice_manager permissive=0
07-20 14:06:44.285   580   580 E SELinux : avc:  denied  { add } for interface=vendor.oplus.hardware.cammidasservice::IMIDASService sid=u:r:mtk_hal_camera:s0 pid=7144 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:hal_camera_oplus_hwservice:s0 tclass=hwservice_manager permissive=0
07-20 14:06:49.294   580   580 E SELinux : avc:  denied  { add } for interface=vendor.oplus.hardware.cammidasservice::IMIDASService sid=u:r:mtk_hal_camera:s0 pid=7154 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:hal_camera_oplus_hwservice:s0 tclass=hwservice_manager permissive=0
07-20 14:06:54.310   580   580 E SELinux : avc:  denied  { add } for interface=vendor.oplus.hardware.cammidasservice::IMIDASService sid=u:r:mtk_hal_camera:s0 pid=7163 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:hal_camera_oplus_hwservice:s0 tclass=hwservice_manager permissive=0
07-20 14:06:59.321   580   580 E SELinux : avc:  denied  { add } for interface=vendor.oplus.hardware.cammidasservice::IMIDASService sid=u:r:mtk_hal_camera:s0 pid=7172 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:hal_camera_oplus_hwservice:s0 tclass=hwservice_manager permissive=0
07-20 14:07:04.334   580   580 E SELinux : avc:  denied  { add } for interface=vendor.oplus.hardware.cammidasservice::IMIDASService sid=u:r:mtk_hal_camera:s0 pid=7181 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:hal_camera_oplus_hwservice:s0 tclass=hwservice_manager permissive=0
07-20 14:07:08.416  2664  2664 W lowpool[36]: type=1400 audit(0.0:282): avc:  denied  { search } for  name="checkin" dev="dm-38" ino=2716 scontext=u:r:priv_app:s0:c512,c768 tcontext=u:object_r:checkin_data_file:s0:c512,c768 tclass=dir permissive=0 app=com.google.android.gms
07-20 14:07:08.584  2761  2761 W highpool[9]: type=1400 audit(0.0:283): avc:  denied  { search } for  name="checkin" dev="dm-38" ino=2716 scontext=u:r:priv_app:s0:c512,c768 tcontext=u:object_r:checkin_data_file:s0:c512,c768 tclass=dir permissive=0 app=com.google.android.gms
07-20 14:07:08.588  2664  2664 W lowpool[36]: type=1400 audit(0.0:284): avc:  denied  { search } for  name="checkin" dev="dm-38" ino=2716 scontext=u:r:priv_app:s0:c512,c768 tcontext=u:object_r:checkin_data_file:s0:c512,c768 tclass=dir permissive=0 app=com.google.android.gms
07-20 14:07:08.636  2761  2761 W highpool[9]: type=1400 audit(0.0:285): avc:  denied  { search } for  name="checkin" dev="dm-38" ino=2716 scontext=u:r:priv_app:s0:c512,c768 tcontext=u:object_r:checkin_data_file:s0:c512,c768 tclass=dir permissive=0 app=com.google.android.gms
07-20 14:07:09.337   580   580 E SELinux : avc:  denied  { add } for interface=vendor.oplus.hardware.cammidasservice::IMIDASService sid=u:r:mtk_hal_camera:s0 pid=7209 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:hal_camera_oplus_hwservice:s0 tclass=hwservice_manager permissive=0
07-20 14:07:14.346   580   580 E SELinux : avc:  denied  { add } for interface=vendor.oplus.hardware.cammidasservice::IMIDASService sid=u:r:mtk_hal_camera:s0 pid=7218 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:hal_camera_oplus_hwservice:s0 tclass=hwservice_manager permissive=0
07-20 14:07:19.355   580   580 E SELinux : avc:  denied  { add } for interface=vendor.oplus.hardware.cammidasservice::IMIDASService sid=u:r:mtk_hal_camera:s0 pid=7227 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:hal_camera_oplus_hwservice:s0 tclass=hwservice_manager permissive=0
07-20 14:07:24.363   580   580 E SELinux : avc:  denied  { add } for interface=vendor.oplus.hardware.cammidasservice::IMIDASService sid=u:r:mtk_hal_camera:s0 pid=7236 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:hal_camera_oplus_hwservice:s0 tclass=hwservice_manager permissive=0
07-20 14:07:29.381   580   580 E SELinux : avc:  denied  { add } for interface=vendor.oplus.hardware.cammidasservice::IMIDASService sid=u:r:mtk_hal_camera:s0 pid=7246 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:hal_camera_oplus_hwservice:s0 tclass=hwservice_manager permissive=0
07-20 14:07:34.394   580   580 E SELinux : avc:  denied  { add } for interface=vendor.oplus.hardware.cammidasservice::IMIDASService sid=u:r:mtk_hal_camera:s0 pid=7255 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:hal_camera_oplus_hwservice:s0 tclass=hwservice_manager permissive=0
07-20 14:07:39.399   580   580 E SELinux : avc:  denied  { add } for interface=vendor.oplus.hardware.cammidasservice::IMIDASService sid=u:r:mtk_hal_camera:s0 pid=7264 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:hal_camera_oplus_hwservice:s0 tclass=hwservice_manager permissive=0
07-20 14:07:44.418   580   580 E SELinux : avc:  denied  { add } for interface=vendor.oplus.hardware.cammidasservice::IMIDASService sid=u:r:mtk_hal_camera:s0 pid=7273 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:hal_camera_oplus_hwservice:s0 tclass=hwservice_manager permissive=0
07-20 14:07:49.427   580   580 E SELinux : avc:  denied  { add } for interface=vendor.oplus.hardware.cammidasservice::IMIDASService sid=u:r:mtk_hal_camera:s0 pid=7282 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:hal_camera_oplus_hwservice:s0 tclass=hwservice_manager permissive=0
07-20 14:07:54.444   580   580 E SELinux : avc:  denied  { add } for interface=vendor.oplus.hardware.cammidasservice::IMIDASService sid=u:r:mtk_hal_camera:s0 pid=7291 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:hal_camera_oplus_hwservice:s0 tclass=hwservice_manager permissive=0
07-20 14:07:59.461   580   580 E SELinux : avc:  denied  { add } for interface=vendor.oplus.hardware.cammidasservice::IMIDASService sid=u:r:mtk_hal_camera:s0 pid=7300 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:hal_camera_oplus_hwservice:s0 tclass=hwservice_manager permissive=0
07-20 14:08:04.479   580   580 E SELinux : avc:  denied  { add } for interface=vendor.oplus.hardware.cammidasservice::IMIDASService sid=u:r:mtk_hal_camera:s0 pid=7309 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:hal_camera_oplus_hwservice:s0 tclass=hwservice_manager permissive=0
07-20 14:08:09.492   580   580 E SELinux : avc:  denied  { add } for interface=vendor.oplus.hardware.cammidasservice::IMIDASService sid=u:r:mtk_hal_camera:s0 pid=7318 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:hal_camera_oplus_hwservice:s0 tclass=hwservice_manager permissive=0
07-20 14:08:14.514   580   580 E SELinux : avc:  denied  { add } for interface=vendor.oplus.hardware.cammidasservice::IMIDASService sid=u:r:mtk_hal_camera:s0 pid=7327 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:hal_camera_oplus_hwservice:s0 tclass=hwservice_manager permissive=0
07-20 14:08:19.523   580   580 E SELinux : avc:  denied  { add } for interface=vendor.oplus.hardware.cammidasservice::IMIDASService sid=u:r:mtk_hal_camera:s0 pid=7336 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:hal_camera_oplus_hwservice:s0 tclass=hwservice_manager permissive=0
07-20 14:08:24.551   580   580 E SELinux : avc:  denied  { add } for interface=vendor.oplus.hardware.cammidasservice::IMIDASService sid=u:r:mtk_hal_camera:s0 pid=7347 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:hal_camera_oplus_hwservice:s0 tclass=hwservice_manager permissive=0
07-20 14:08:29.569   580   580 E SELinux : avc:  denied  { add } for interface=vendor.oplus.hardware.cammidasservice::IMIDASService sid=u:r:mtk_hal_camera:s0 pid=7356 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:hal_camera_oplus_hwservice:s0 tclass=hwservice_manager permissive=0
07-20 14:08:34.584   580   580 E SELinux : avc:  denied  { add } for interface=vendor.oplus.hardware.cammidasservice::IMIDASService sid=u:r:mtk_hal_camera:s0 pid=7365 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:hal_camera_oplus_hwservice:s0 tclass=hwservice_manager permissive=0
07-20 14:08:39.604   580   580 E SELinux : avc:  denied  { add } for interface=vendor.oplus.hardware.cammidasservice::IMIDASService sid=u:r:mtk_hal_camera:s0 pid=7375 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:hal_camera_oplus_hwservice:s0 tclass=hwservice_manager permissive=0
07-20 14:08:44.616   580   580 E SELinux : avc:  denied  { add } for interface=vendor.oplus.hardware.cammidasservice::IMIDASService sid=u:r:mtk_hal_camera:s0 pid=7384 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:hal_camera_oplus_hwservice:s0 tclass=hwservice_manager permissive=0
07-20 14:08:49.620   580   580 E SELinux : avc:  denied  { add } for interface=vendor.oplus.hardware.cammidasservice::IMIDASService sid=u:r:mtk_hal_camera:s0 pid=7393 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:hal_camera_oplus_hwservice:s0 tclass=hwservice_manager permissive=0
07-20 14:08:54.643   580   580 E SELinux : avc:  denied  { add } for interface=vendor.oplus.hardware.cammidasservice::IMIDASService sid=u:r:mtk_hal_camera:s0 pid=7402 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:hal_camera_oplus_hwservice:s0 tclass=hwservice_manager permissive=0
07-20 14:08:59.660   580   580 E SELinux : avc:  denied  { add } for interface=vendor.oplus.hardware.cammidasservice::IMIDASService sid=u:r:mtk_hal_camera:s0 pid=7411 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:hal_camera_oplus_hwservice:s0 tclass=hwservice_manager permissive=0
07-20 14:09:04.636   580   580 E SELinux : avc:  denied  { add } for interface=vendor.oplus.hardware.cammidasservice::IMIDASService sid=u:r:mtk_hal_camera:s0 pid=7420 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:hal_camera_oplus_hwservice:s0 tclass=hwservice_manager permissive=0
07-20 14:09:09.650   580   580 E SELinux : avc:  denied  { add } for interface=vendor.oplus.hardware.cammidasservice::IMIDASService sid=u:r:mtk_hal_camera:s0 pid=7429 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:hal_camera_oplus_hwservice:s0 tclass=hwservice_manager permissive=0
07-20 14:09:14.667   580   580 E SELinux : avc:  denied  { add } for interface=vendor.oplus.hardware.cammidasservice::IMIDASService sid=u:r:mtk_hal_camera:s0 pid=7438 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:hal_camera_oplus_hwservice:s0 tclass=hwservice_manager permissive=0
07-20 14:09:19.678   580   580 E SELinux : avc:  denied  { add } for interface=vendor.oplus.hardware.cammidasservice::IMIDASService sid=u:r:mtk_hal_camera:s0 pid=7447 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:hal_camera_oplus_hwservice:s0 tclass=hwservice_manager permissive=0
07-20 14:09:24.730   580   580 E SELinux : avc:  denied  { add } for interface=vendor.oplus.hardware.cammidasservice::IMIDASService sid=u:r:mtk_hal_camera:s0 pid=7456 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:hal_camera_oplus_hwservice:s0 tclass=hwservice_manager permissive=0
07-20 14:09:29.747   580   580 E SELinux : avc:  denied  { add } for interface=vendor.oplus.hardware.cammidasservice::IMIDASService sid=u:r:mtk_hal_camera:s0 pid=7465 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:hal_camera_oplus_hwservice:s0 tclass=hwservice_manager permissive=0
07-20 14:09:34.752   580   580 E SELinux : avc:  denied  { add } for interface=vendor.oplus.hardware.cammidasservice::IMIDASService sid=u:r:mtk_hal_camera:s0 pid=7474 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:hal_camera_oplus_hwservice:s0 tclass=hwservice_manager permissive=0
07-20 14:09:39.773   580   580 E SELinux : avc:  denied  { add } for interface=vendor.oplus.hardware.cammidasservice::IMIDASService sid=u:r:mtk_hal_camera:s0 pid=7483 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:hal_camera_oplus_hwservice:s0 tclass=hwservice_manager permissive=0
07-20 14:09:44.750   580   580 E SELinux : avc:  denied  { add } for interface=vendor.oplus.hardware.cammidasservice::IMIDASService sid=u:r:mtk_hal_camera:s0 pid=7494 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:hal_camera_oplus_hwservice:s0 tclass=hwservice_manager permissive=0
07-20 14:09:49.760   580   580 E SELinux : avc:  denied  { add } for interface=vendor.oplus.hardware.cammidasservice::IMIDASService sid=u:r:mtk_hal_camera:s0 pid=7505 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:hal_camera_oplus_hwservice:s0 tclass=hwservice_manager permissive=0
07-20 14:09:54.815   580   580 E SELinux : avc:  denied  { add } for interface=vendor.oplus.hardware.cammidasservice::IMIDASService sid=u:r:mtk_hal_camera:s0 pid=7514 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:hal_camera_oplus_hwservice:s0 tclass=hwservice_manager permissive=0
07-20 14:09:59.826   580   580 E SELinux : avc:  denied  { add } for interface=vendor.oplus.hardware.cammidasservice::IMIDASService sid=u:r:mtk_hal_camera:s0 pid=7523 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:hal_camera_oplus_hwservice:s0 tclass=hwservice_manager permissive=0
07-20 14:10:04.840   580   580 E SELinux : avc:  denied  { add } for interface=vendor.oplus.hardware.cammidasservice::IMIDASService sid=u:r:mtk_hal_camera:s0 pid=7532 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:hal_camera_oplus_hwservice:s0 tclass=hwservice_manager permissive=0
07-20 14:10:09.855   580   580 E SELinux : avc:  denied  { add } for interface=vendor.oplus.hardware.cammidasservice::IMIDASService sid=u:r:mtk_hal_camera:s0 pid=7540 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:hal_camera_oplus_hwservice:s0 tclass=hwservice_manager permissive=0
07-20 14:10:14.871   580   580 E SELinux : avc:  denied  { add } for interface=vendor.oplus.hardware.cammidasservice::IMIDASService sid=u:r:mtk_hal_camera:s0 pid=7548 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:hal_camera_oplus_hwservice:s0 tclass=hwservice_manager permissive=0
07-20 14:10:19.883   580   580 E SELinux : avc:  denied  { add } for interface=vendor.oplus.hardware.cammidasservice::IMIDASService sid=u:r:mtk_hal_camera:s0 pid=7556 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:hal_camera_oplus_hwservice:s0 tclass=hwservice_manager permissive=0
07-20 14:10:24.895   580   580 E SELinux : avc:  denied  { add } for interface=vendor.oplus.hardware.cammidasservice::IMIDASService sid=u:r:mtk_hal_camera:s0 pid=7564 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:hal_camera_oplus_hwservice:s0 tclass=hwservice_manager permissive=0
07-20 14:10:28.624  2664  2664 W lowpool[37]: type=1400 audit(0.0:286): avc:  denied  { search } for  name="checkin" dev="dm-38" ino=2716 scontext=u:r:priv_app:s0:c512,c768 tcontext=u:object_r:checkin_data_file:s0:c512,c768 tclass=dir permissive=0 app=com.google.android.gms
07-20 14:10:29.052  2664  2664 W lowpool[37]: type=1400 audit(0.0:287): avc:  denied  { search } for  name="checkin" dev="dm-38" ino=2716 scontext=u:r:priv_app:s0:c512,c768 tcontext=u:object_r:checkin_data_file:s0:c512,c768 tclass=dir permissive=0 app=com.google.android.gms
07-20 14:10:29.868   580   580 E SELinux : avc:  denied  { add } for interface=vendor.oplus.hardware.cammidasservice::IMIDASService sid=u:r:mtk_hal_camera:s0 pid=7597 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:hal_camera_oplus_hwservice:s0 tclass=hwservice_manager permissive=0
07-20 14:10:34.879   580   580 E SELinux : avc:  denied  { add } for interface=vendor.oplus.hardware.cammidasservice::IMIDASService sid=u:r:mtk_hal_camera:s0 pid=7632 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:hal_camera_oplus_hwservice:s0 tclass=hwservice_manager permissive=0
07-20 14:10:39.897   580   580 E SELinux : avc:  denied  { add } for interface=vendor.oplus.hardware.cammidasservice::IMIDASService sid=u:r:mtk_hal_camera:s0 pid=7641 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:hal_camera_oplus_hwservice:s0 tclass=hwservice_manager permissive=0
07-20 14:10:44.912   580   580 E SELinux : avc:  denied  { add } for interface=vendor.oplus.hardware.cammidasservice::IMIDASService sid=u:r:mtk_hal_camera:s0 pid=7650 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:hal_camera_oplus_hwservice:s0 tclass=hwservice_manager permissive=0
07-20 14:10:49.921   580   580 E SELinux : avc:  denied  { add } for interface=vendor.oplus.hardware.cammidasservice::IMIDASService sid=u:r:mtk_hal_camera:s0 pid=7659 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:hal_camera_oplus_hwservice:s0 tclass=hwservice_manager permissive=0
07-20 14:10:54.942   580   580 E SELinux : avc:  denied  { add } for interface=vendor.oplus.hardware.cammidasservice::IMIDASService sid=u:r:mtk_hal_camera:s0 pid=7668 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:hal_camera_oplus_hwservice:s0 tclass=hwservice_manager permissive=0
07-20 14:10:59.956   580   580 E SELinux : avc:  denied  { add } for interface=vendor.oplus.hardware.cammidasservice::IMIDASService sid=u:r:mtk_hal_camera:s0 pid=7677 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:hal_camera_oplus_hwservice:s0 tclass=hwservice_manager permissive=0
07-20 14:11:04.969   580   580 E SELinux : avc:  denied  { add } for interface=vendor.oplus.hardware.cammidasservice::IMIDASService sid=u:r:mtk_hal_camera:s0 pid=7686 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:hal_camera_oplus_hwservice:s0 tclass=hwservice_manager permissive=0
07-20 14:11:09.981   580   580 E SELinux : avc:  denied  { add } for interface=vendor.oplus.hardware.cammidasservice::IMIDASService sid=u:r:mtk_hal_camera:s0 pid=7695 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:hal_camera_oplus_hwservice:s0 tclass=hwservice_manager permissive=0
07-20 14:11:14.994   580   580 E SELinux : avc:  denied  { add } for interface=vendor.oplus.hardware.cammidasservice::IMIDASService sid=u:r:mtk_hal_camera:s0 pid=7704 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:hal_camera_oplus_hwservice:s0 tclass=hwservice_manager permissive=0
07-20 14:11:20.001   580   580 E SELinux : avc:  denied  { add } for interface=vendor.oplus.hardware.cammidasservice::IMIDASService sid=u:r:mtk_hal_camera:s0 pid=7714 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:hal_camera_oplus_hwservice:s0 tclass=hwservice_manager permissive=0
07-20 14:11:25.018   580   580 E SELinux : avc:  denied  { add } for interface=vendor.oplus.hardware.cammidasservice::IMIDASService sid=u:r:mtk_hal_camera:s0 pid=7723 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:hal_camera_oplus_hwservice:s0 tclass=hwservice_manager permissive=0
07-20 14:11:30.032   580   580 E SELinux : avc:  denied  { add } for interface=vendor.oplus.hardware.cammidasservice::IMIDASService sid=u:r:mtk_hal_camera:s0 pid=7732 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:hal_camera_oplus_hwservice:s0 tclass=hwservice_manager permissive=0
07-20 14:11:35.044   580   580 E SELinux : avc:  denied  { add } for interface=vendor.oplus.hardware.cammidasservice::IMIDASService sid=u:r:mtk_hal_camera:s0 pid=7743 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:hal_camera_oplus_hwservice:s0 tclass=hwservice_manager permissive=0
07-20 14:11:40.055   580   580 E SELinux : avc:  denied  { add } for interface=vendor.oplus.hardware.cammidasservice::IMIDASService sid=u:r:mtk_hal_camera:s0 pid=7752 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:hal_camera_oplus_hwservice:s0 tclass=hwservice_manager permissive=0
07-20 14:11:45.068   580   580 E SELinux : avc:  denied  { add } for interface=vendor.oplus.hardware.cammidasservice::IMIDASService sid=u:r:mtk_hal_camera:s0 pid=7761 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:hal_camera_oplus_hwservice:s0 tclass=hwservice_manager permissive=0
07-20 14:11:50.076   580   580 E SELinux : avc:  denied  { add } for interface=vendor.oplus.hardware.cammidasservice::IMIDASService sid=u:r:mtk_hal_camera:s0 pid=7770 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:hal_camera_oplus_hwservice:s0 tclass=hwservice_manager permissive=0
07-20 14:11:55.122   580   580 E SELinux : avc:  denied  { add } for interface=vendor.oplus.hardware.cammidasservice::IMIDASService sid=u:r:mtk_hal_camera:s0 pid=7780 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:hal_camera_oplus_hwservice:s0 tclass=hwservice_manager permissive=0
07-20 14:12:00.135   580   580 E SELinux : avc:  denied  { add } for interface=vendor.oplus.hardware.cammidasservice::IMIDASService sid=u:r:mtk_hal_camera:s0 pid=7789 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:hal_camera_oplus_hwservice:s0 tclass=hwservice_manager permissive=0
07-20 14:12:05.149   580   580 E SELinux : avc:  denied  { add } for interface=vendor.oplus.hardware.cammidasservice::IMIDASService sid=u:r:mtk_hal_camera:s0 pid=7798 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:hal_camera_oplus_hwservice:s0 tclass=hwservice_manager permissive=0
07-20 14:12:10.159   580   580 E SELinux : avc:  denied  { add } for interface=vendor.oplus.hardware.cammidasservice::IMIDASService sid=u:r:mtk_hal_camera:s0 pid=7807 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:hal_camera_oplus_hwservice:s0 tclass=hwservice_manager permissive=0
07-20 14:12:15.173   580   580 E SELinux : avc:  denied  { add } for interface=vendor.oplus.hardware.cammidasservice::IMIDASService sid=u:r:mtk_hal_camera:s0 pid=7816 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:hal_camera_oplus_hwservice:s0 tclass=hwservice_manager permissive=0
07-20 14:12:20.155   580   580 E SELinux : avc:  denied  { add } for interface=vendor.oplus.hardware.cammidasservice::IMIDASService sid=u:r:mtk_hal_camera:s0 pid=7825 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:hal_camera_oplus_hwservice:s0 tclass=hwservice_manager permissive=0
07-20 14:12:25.198   580   580 E SELinux : avc:  denied  { add } for interface=vendor.oplus.hardware.cammidasservice::IMIDASService sid=u:r:mtk_hal_camera:s0 pid=7834 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:hal_camera_oplus_hwservice:s0 tclass=hwservice_manager permissive=0
07-20 14:12:30.211   580   580 E SELinux : avc:  denied  { add } for interface=vendor.oplus.hardware.cammidasservice::IMIDASService sid=u:r:mtk_hal_camera:s0 pid=7845 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:hal_camera_oplus_hwservice:s0 tclass=hwservice_manager permissive=0
07-20 14:12:35.226   580   580 E SELinux : avc:  denied  { add } for interface=vendor.oplus.hardware.cammidasservice::IMIDASService sid=u:r:mtk_hal_camera:s0 pid=7854 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:hal_camera_oplus_hwservice:s0 tclass=hwservice_manager permissive=0
07-20 14:12:40.238   580   580 E SELinux : avc:  denied  { add } for interface=vendor.oplus.hardware.cammidasservice::IMIDASService sid=u:r:mtk_hal_camera:s0 pid=7863 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:hal_camera_oplus_hwservice:s0 tclass=hwservice_manager permissive=0
07-20 14:12:45.239   580   580 E SELinux : avc:  denied  { add } for interface=vendor.oplus.hardware.cammidasservice::IMIDASService sid=u:r:mtk_hal_camera:s0 pid=7872 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:hal_camera_oplus_hwservice:s0 tclass=hwservice_manager permissive=0
07-20 14:12:50.258   580   580 E SELinux : avc:  denied  { add } for interface=vendor.oplus.hardware.cammidasservice::IMIDASService sid=u:r:mtk_hal_camera:s0 pid=7881 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:hal_camera_oplus_hwservice:s0 tclass=hwservice_manager permissive=0
07-20 14:12:55.267   580   580 E SELinux : avc:  denied  { add } for interface=vendor.oplus.hardware.cammidasservice::IMIDASService sid=u:r:mtk_hal_camera:s0 pid=7890 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:hal_camera_oplus_hwservice:s0 tclass=hwservice_manager permissive=0
07-20 14:13:00.286   580   580 E SELinux : avc:  denied  { add } for interface=vendor.oplus.hardware.cammidasservice::IMIDASService sid=u:r:mtk_hal_camera:s0 pid=7899 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:hal_camera_oplus_hwservice:s0 tclass=hwservice_manager permissive=0
07-20 14:13:05.303   580   580 E SELinux : avc:  denied  { add } for interface=vendor.oplus.hardware.cammidasservice::IMIDASService sid=u:r:mtk_hal_camera:s0 pid=7908 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:hal_camera_oplus_hwservice:s0 tclass=hwservice_manager permissive=0
07-20 14:13:10.315   580   580 E SELinux : avc:  denied  { add } for interface=vendor.oplus.hardware.cammidasservice::IMIDASService sid=u:r:mtk_hal_camera:s0 pid=7917 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:hal_camera_oplus_hwservice:s0 tclass=hwservice_manager permissive=0
07-20 14:13:15.306   580   580 E SELinux : avc:  denied  { add } for interface=vendor.oplus.hardware.cammidasservice::IMIDASService sid=u:r:mtk_hal_camera:s0 pid=7929 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:hal_camera_oplus_hwservice:s0 tclass=hwservice_manager permissive=0
07-20 14:13:20.341   580   580 E SELinux : avc:  denied  { add } for interface=vendor.oplus.hardware.cammidasservice::IMIDASService sid=u:r:mtk_hal_camera:s0 pid=7938 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:hal_camera_oplus_hwservice:s0 tclass=hwservice_manager permissive=0
07-20 14:13:25.360   580   580 E SELinux : avc:  denied  { add } for interface=vendor.oplus.hardware.cammidasservice::IMIDASService sid=u:r:mtk_hal_camera:s0 pid=7947 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:hal_camera_oplus_hwservice:s0 tclass=hwservice_manager permissive=0
07-20 14:13:26.296  2664  2664 W lowpool[8]: type=1400 audit(0.0:288): avc:  denied  { search } for  name="checkin" dev="dm-38" ino=2716 scontext=u:r:priv_app:s0:c512,c768 tcontext=u:object_r:checkin_data_file:s0:c512,c768 tclass=dir permissive=0 app=com.google.android.gms
07-20 14:13:30.369   580   580 E SELinux : avc:  denied  { add } for interface=vendor.oplus.hardware.cammidasservice::IMIDASService sid=u:r:mtk_hal_camera:s0 pid=7963 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:hal_camera_oplus_hwservice:s0 tclass=hwservice_manager permissive=0
07-20 14:13:35.346   580   580 E SELinux : avc:  denied  { add } for interface=vendor.oplus.hardware.cammidasservice::IMIDASService sid=u:r:mtk_hal_camera:s0 pid=7972 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:hal_camera_oplus_hwservice:s0 tclass=hwservice_manager permissive=0
07-20 14:13:40.367   580   580 E SELinux : avc:  denied  { add } for interface=vendor.oplus.hardware.cammidasservice::IMIDASService sid=u:r:mtk_hal_camera:s0 pid=7981 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:hal_camera_oplus_hwservice:s0 tclass=hwservice_manager permissive=0
