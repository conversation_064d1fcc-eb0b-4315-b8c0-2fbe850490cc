# ==============================================
# Common SEPolicy Rule
# ==============================================

# system_internal_prop      -- Properties used only in /system
# system_restricted_prop    -- Properties which can't be written outside system
# system_public_prop        -- Properties with no restrictions
# system_vendor_config_prop -- Properties which can be written only by vendor_init
# vendor_internal_prop      -- Properties used only in /vendor
# vendor_restricted_prop    -- Properties which can't be written outside vendor
# vendor_public_prop        -- Properties with no restrictions

# Properties used only in /system
system_internal_prop(system_mtk_audio_prop)
system_internal_prop(system_mtk_bgdata_disabled_prop)
system_internal_prop(system_mtk_bootani_prop)
system_internal_prop(system_mtk_connsysfw_prop)
system_internal_prop(system_mtk_debug_bq_dump_prop)
system_internal_prop(system_mtk_debug_mdlogger_prop)
system_internal_prop(system_mtk_debug_mtklog_prop)
system_internal_prop(system_mtk_debug_netlog_prop)
system_internal_prop(system_mtk_gprs_attach_type_prop)
system_internal_prop(system_mtk_mdl_prop)
system_internal_prop(system_mtk_mdl_pulllog_prop)
system_internal_prop(system_mtk_mdl_start_prop)
system_internal_prop(system_mtk_mobile_log_post_prop)
system_internal_prop(system_mtk_mobile_log_prop)
system_internal_prop(system_mtk_persist_mdlog_prop)
system_internal_prop(system_mtk_persist_mtklog_prop)
system_internal_prop(system_mtk_persist_xcap_rawurl_prop)
system_internal_prop(system_mtk_power_off_md_prop)
system_internal_prop(system_mtk_sim_system_prop)
system_internal_prop(system_mtk_vendor_bluetooth_prop)
system_internal_prop(system_mtk_wifisa_log_prop)
system_internal_prop(system_mtk_rcs_single_reg_support_prop)
system_internal_prop(system_mtk_sf_debug_prop)
system_internal_prop(system_mtk_pco_prop)

# Properties which can't be written outside system
system_restricted_prop(system_mtk_amslog_prop)

# Properties with can't be accessed by device-sepcific domains
typeattribute system_mtk_amslog_prop                extended_core_property_type;
typeattribute system_mtk_audio_prop                 extended_core_property_type;
typeattribute system_mtk_bgdata_disabled_prop       extended_core_property_type;
typeattribute system_mtk_bootani_prop               extended_core_property_type;
typeattribute system_mtk_connsysfw_prop             extended_core_property_type;
typeattribute system_mtk_debug_bq_dump_prop         extended_core_property_type;
typeattribute system_mtk_debug_mdlogger_prop        extended_core_property_type;
typeattribute system_mtk_debug_mtklog_prop          extended_core_property_type;
typeattribute system_mtk_debug_netlog_prop          extended_core_property_type;
typeattribute system_mtk_gprs_attach_type_prop      extended_core_property_type;
typeattribute system_mtk_mdl_prop                   extended_core_property_type;
typeattribute system_mtk_mdl_pulllog_prop           extended_core_property_type;
typeattribute system_mtk_mdl_start_prop             extended_core_property_type;
typeattribute system_mtk_mobile_log_prop            extended_core_property_type;
typeattribute system_mtk_persist_mdlog_prop         extended_core_property_type;
typeattribute system_mtk_persist_mtklog_prop        extended_core_property_type;
typeattribute system_mtk_persist_xcap_rawurl_prop   extended_core_property_type;
typeattribute system_mtk_power_off_md_prop          extended_core_property_type;
typeattribute system_mtk_sim_system_prop            extended_core_property_type;
typeattribute system_mtk_vendor_bluetooth_prop      extended_core_property_type;
typeattribute system_mtk_rcs_single_reg_support_prop extended_core_property_type;
typeattribute system_mtk_sf_debug_prop              extended_core_property_type;
typeattribute system_mtk_pco_prop extended_core_property_type;
