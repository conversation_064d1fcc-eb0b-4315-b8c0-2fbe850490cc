# ==============================================
# Common SEPolicy Rule
# ==============================================

type hal_keymaster_attestation, domain;
type hal_keymaster_attestation_exec, exec_type, vendor_file_type, file_type;

init_daemon_domain(hal_keymaster_attestation)

hal_server_domain(hal_keymaster_attestation, hal_mtk_keyattestation)

allow hal_keymaster_attestation tee_device:chr_file rw_file_perms;

# Date : WK17.42 2017/10/19
# Operation: Keymaster 3.0
# Purpose: Access attestation key in persist partition
allow hal_keymaster_attestation mnt_vendor_file:dir search;
allow hal_keymaster_attestation persist_data_file:dir w_dir_perms;
allow hal_keymaster_attestation persist_data_file:file create_file_perms;
