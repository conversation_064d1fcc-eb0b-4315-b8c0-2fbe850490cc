# ==============================================
# Policy File of /system/bin/connsyslogger Executable File

# ==============================================
# Common SEPolicy Rule
# ==============================================

#for logging sdcard access
allow connsyslogger fuse:dir create_dir_perms;
allow connsyslogger fuse:file create_file_perms;

#consys logger access on /data/consyslog
allow connsyslogger consyslog_data_file:dir { create_dir_perms relabelto };
allow connsyslogger consyslog_data_file:fifo_file create_file_perms;
allow connsyslogger consyslog_data_file:file create_file_perms;

allow connsyslogger tmpfs:lnk_file create_file_perms;

# purpose: avc: denied { read } for name="plat_file_contexts"
allow connsyslogger file_contexts_file:file r_file_perms;

#logger SD logging in factory mode
allow connsyslogger vfat:dir create_dir_perms;
allow connsyslogger vfat:file create_file_perms;

#logger permission in storage in android M version
allow connsyslogger mnt_user_file:dir search;
allow connsyslogger mnt_user_file:lnk_file r_file_perms;
allow connsyslogger storage_file:lnk_file r_file_perms;

#permission for use SELinux API
allow connsyslogger rootfs:file r_file_perms;

#permission for storage access storage
allow connsyslogger storage_file:dir create_dir_perms;
allow connsyslogger storage_file:file create_file_perms;

#permission for read boot mode
allow connsyslogger sysfs_boot_mode:file r_file_perms;

allow connsyslogger fw_log_wifi_device:chr_file rw_file_perms;
allow connsyslogger fw_log_bt_device:chr_file rw_file_perms;
allow connsyslogger fw_log_gps_device:chr_file rw_file_perms;
allow connsyslogger fw_log_wmt_device:chr_file rw_file_perms;
allow connsyslogger fw_log_ics_device:chr_file rw_file_perms;
allow connsyslogger fw_log_wifimcu_device:chr_file rw_file_perms_no_map;
allow connsyslogger fw_log_btmcu_device:chr_file rw_file_perms_no_map;

allow connsyslogger sdcardfs:dir create_dir_perms;
allow connsyslogger sdcardfs:file create_file_perms;
allow connsyslogger rootfs:lnk_file getattr;

allow connsyslogger media_rw_data_file:file create_file_perms;
allow connsyslogger media_rw_data_file:dir create_dir_perms;

#permission to get driver ready status
get_prop(connsyslogger, vendor_mtk_wmt_prop)

#Date:2019/03/25
# purpose: allow connsyslogger to access persist.meta.connecttype
get_prop(connsyslogger, vendor_mtk_meta_connecttype_prop)


#Date:2019/03/25
# purpose: allow emdlogger to create socket
allow connsyslogger port:tcp_socket { name_connect name_bind };
allow connsyslogger connsyslogger:tcp_socket create_stream_socket_perms;
allow connsyslogger node:tcp_socket node_bind;

#Date:2019/03/25
# usb device ttyGSx for modem logger usb logging
allow connsyslogger ttyGS_device:chr_file rw_file_perms;

# Add permission to access new bootmode file
allow connsyslogger sysfs_boot_info:file r_file_perms;
