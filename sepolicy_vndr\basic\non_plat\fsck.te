# ==============================================
# Common SEPolicy Rule
# ==============================================

# Date : WK15.29
# Operation : Migration
# Purpose : file system check for protect1/protect2/nvdata/persist/nvcfg block devices.
allow fsck protect1_block_device:blk_file rw_file_perms;
allow fsck protect2_block_device:blk_file rw_file_perms;
allow fsck nvdata_device:blk_file rw_file_perms;
allow fsck persist_block_device:blk_file rw_file_perms;
allow fsck nvcfg_block_device:blk_file rw_file_perms;
allow fsck odm_block_device:blk_file rw_file_perms;
allow fsck oem_block_device:blk_file rw_file_perms;

# Date : WK17.12
# Purpose: Fix bootup fail
allow fsck system_block_device:blk_file getattr;
