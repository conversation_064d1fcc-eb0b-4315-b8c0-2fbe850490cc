# ==============================================
# Policy File of /system/bin/em_svr Executable File

# ==============================================
# Common SEPolicy Rule
# ==============================================

# Date: WK1823
# Purpose: Rsc switch
allow em_svr sysfs_dt_firmware_android:dir r_dir_perms;
allow em_svr sysfs_dt_firmware_android:file r_file_perms;

# Date: 2020/03/25
# Purpose : EM Power PMU reading/setting
allow em_svr sysfs_pmu:dir search;
allow em_svr sysfs_pmu:file rw_file_perms;
allow em_svr sysfs_pmu:lnk_file r_file_perms;

# Date: 2020/03/25
# Purpose: EM battery temprature setting
allow em_svr sysfs_batteryinfo:dir search;
allow em_svr sysfs_battery_temp:file w_file_perms;

# Date: 2020/03/25
# Purpose: EM power ChargeBattery
allow em_svr sysfs_battery_consumption:file r_file_perms;
allow em_svr sysfs_power_on_vol:file r_file_perms;
allow em_svr sysfs_power_off_vol:file r_file_perms;
allow em_svr sysfs_fg_disable:file rw_file_perms;
allow em_svr sysfs_dis_nafg:file rw_file_perms;

# Date: 2020/03/25
# Purpose : EM flash reading
allow em_svr proc_flash:file r_file_perms;
allow em_svr proc_partition:file r_file_perms;

# Date: WK2013
# Purpose: add for power battery charge/PMU
allow em_svr toolbox_exec:file rx_file_perms;

# Date: WK1812
# Purpose: add for battery log
allow em_svr proc_battery_cmd:dir search;
allow em_svr proc_battery_cmd:file create_file_perms;

# Date: WK2016
# Purpose: add seolicy for em_svr write phone storage
allow em_svr mnt_user_file:dir search;
allow em_svr fuse:file create_file_perms;
allow em_svr fuse:dir w_dir_perms;

# Date: 2020/06/03
# Operation: Telephony->Bypass
# Purpose: Allow EM read usb_rawbulk
allow em_svr sys_usb_rawbulk:dir r_dir_perms;
allow em_svr sys_usb_rawbulk:file r_file_perms;

# Date 2021/04/26
# Purpose: Rsc usage
allow em_svr gsi_metadata_file:dir search;
allow em_svr metadata_file:dir search;
