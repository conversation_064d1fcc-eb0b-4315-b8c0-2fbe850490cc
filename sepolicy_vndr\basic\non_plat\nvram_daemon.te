# ==============================================
# Policy File of /vendor/bin/nvram_daemon Executable File

# ==============================================
# Type Declaration
# ==============================================

type nvram_daemon_exec, exec_type, file_type, vendor_file_type;
type nvram_daemon, domain;

# ==============================================
# Common SEPolicy Rule
# ==============================================

init_daemon_domain(nvram_daemon)

# Date : WK14.31
# Operation : Migration
# Purpose : the device is used to store Nvram backup data that can not be lost.
allow nvram_daemon nvram_device:blk_file rw_file_perms;
allow nvram_daemon nvdata_device:blk_file rw_file_perms;

# Date : WK14.35
# Operation : chown folder and file permission
# Purpose : ensure nvram user can access nvram file normally when upgrade from KK/KK.AOSP to L.
allow nvram_daemon nvram_data_file:dir create_dir_perms;
allow nvram_daemon nvram_data_file:file create_file_perms;
allow nvram_daemon nvram_data_file:lnk_file { r_file_perms unlink };
allow nvram_daemon nvdata_file:lnk_file r_file_perms;
allow nvram_daemon nvdata_file:dir create_dir_perms;
allow nvram_daemon nvdata_file:file create_file_perms;

allow nvram_daemon als_ps_device:chr_file r_file_perms;
allow nvram_daemon mtk-adc-cali_device:chr_file rw_file_perms;
allow nvram_daemon gsensor_device:chr_file r_file_perms;
allow nvram_daemon gyroscope_device:chr_file r_file_perms;

# Purpose: for property set
allow nvram_daemon self:capability { fowner chown fsetid };

# Purpose: for backup
allow nvram_daemon nvram_device:chr_file rw_file_perms;
allow nvram_daemon pro_info_device:chr_file rw_file_perms;

allow nvram_daemon block_device:dir search;

# Purpose: for nand project
allow nvram_daemon mtd_device:dir search;
allow nvram_daemon mtd_device:chr_file rw_file_perms;

# Purpose: for fstab parser
allow nvram_daemon kmsg_device:chr_file w_file_perms;
allow nvram_daemon proc_lk_env:file rw_file_perms;

# Purpose: property set
set_prop(nvram_daemon, vendor_mtk_service_nvram_init_prop)

# Purpose: copy /fstab*
allow nvram_daemon rootfs:dir r_dir_perms;
allow nvram_daemon rootfs:file r_file_perms;

# Date : WK18.16
# Operation: P migration
# Purpose: Allow nvram_daemon to get vendor_mtk_tel_switch_prop
get_prop(nvram_daemon, vendor_mtk_tel_switch_prop)
get_prop(nvram_daemon, vendor_mtk_rat_config_prop)

# Date : WK18.21
# Operation: P migration
# Purpose: Allow nvram_daemon to search /mnt/vendor/nvdata for fstab
allow nvram_daemon mnt_vendor_file:dir search;

allow nvram_daemon sysfs_boot_mode:file r_file_perms;

# Allow ReadDefaultFstab().
read_fstab(nvram_daemon)

# Purpose: Wifi NVRAM ConnFem Kernel node access
allow nvram_daemon connfem_device:chr_file rw_file_perms;

# Purpose: Allow nvram_daemon to search /mnt/vendor/nvdata for fstab
allow nvram_daemon mnt_vendor_file:dir search;
allow nvram_daemon self:capability { fowner chown fsetid };
allow nvram_daemon sysfs_boot_mode:file r_file_perms;
allow nvram_daemon proc_cmdline:file r_file_perms;
allow nvram_daemon sysfs_dt_firmware_android:dir r_dir_perms;
allow nvram_daemon sysfs_dt_firmware_android:file r_file_perms;

# Purpose: Allow to search metadata files
allow nvram_daemon metadata_file:dir search;

# Allow nvram_daemon to search gsi_metadata
allow nvram_daemon gsi_metadata_file:dir search;
