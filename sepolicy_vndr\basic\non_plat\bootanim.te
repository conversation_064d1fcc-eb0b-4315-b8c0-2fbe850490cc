
# ==============================================
# Common SEPolicy Rule
# ==============================================

# Date : WK14.46
# Operation : Migration
# Purpose : For MTK Emulator HW GPU
allow bootanim qemu_pipe_device:chr_file rw_file_perms;

# Date : WK16.33
# Purpose: Allow to access ged for gralloc_extra functions
allow bootanim proc_ged:file rw_file_perms;

# Date : WK17.43
# Operation : Migration
# Purpose : For MTK perfmgr
allow bootanim proc_perfmgr:dir r_dir_perms;
allow bootanim proc_perfmgr:file r_file_perms;

# Date : WK19.11
# Operation : Migration
# Purpose : Allow to access ged for ioctl related functions
allowxperm bootanim proc_ged:file ioctl { proc_ged_ioctls };
allowxperm bootanim proc_perfmgr:file ioctl {
  PERFMGR_FPSGO_QUEUE
  PERFMGR_FPSGO_DEQUEUE
  PERFMGR_FPSGO_QUEUE_CONNECT
  PERFMGR_FPSGO_BQID
};

# Date : WK19.48
# Operation : Migration
# Purpose : Allow to access gpu device search
allow bootanim gpu_device:dir search;

# Date : WK21.26
# Operation : Migration
# Purpose : donotaudit data directory search
dontaudit bootanim system_data_file:dir search;
