# ==============================================
# Policy File of /vendor/bin/vpud Executable File

# ==============================================
# Common SEPolicy Rule
# ==============================================

type vpud_native_exec, exec_type, file_type, vendor_file_type;
type vpud_native, domain;

init_daemon_domain(vpud_native)

allow vpud_native ion_device:chr_file rw_file_perms;
allow vpud_native vcu_device:chr_file rw_file_perms;
allow vpud_native MTK_SMI_device:chr_file r_file_perms;
allow vpud_native thermal_manager_data_file:file rw_file_perms;
allow vpud_native thermalloadalgod:unix_stream_socket connectto;
allow vpud_native proc_mtktz:dir search;
allow vpud_native proc_mtktz:file rw_file_perms;
allow vpud_native proc_thermal:file rw_file_perms;
set_prop(vpud_native, vendor_mtk_thermal_config_prop)
allowxperm vpud_native proc_m4u:file ioctl MTK_M4U_GZ_SEC_INIT;
allowxperm vpud_native proc_m4u:file ioctl MTK_M4U_T_SEC_INIT;
allow vpud_native vcodec_file:file create_file_perms;
allow vpud_native vcodec_file:dir create_dir_perms;
allow vpud_native sysfs_device_tree_model:file r_file_perms;

allow vpud_native gz_device:chr_file rw_file_perms;
allow vpud_native proc_m4u:file rw_file_perms;
allow vpud_native tee_device:chr_file rw_file_perms;

# call PowerHal
hal_client_domain(vpud_native, hal_power)

allow vpud_native mediaserver:fd use;
allow vpud_native debugfs_ion:dir search;

not_full_treble(`
  allow vpud_native shell_exec:file { execute read open execute_no_trans getattr };
  allow vpud_native toolbox_exec:file { getattr execute read open execute_no_trans };
')

full_treble_only(`
  allow vpud_native vendor_shell_exec:file rx_file_perms;
  allow vpud_native vendor_toolbox_exec:file rx_file_perms;
')

# add power config
allow vpud_native sysfs_boot_mode:file r_file_perms;

allow vpud_native proc_mtkcooler:dir search;
allow vpud_native proc_mtkcooler:file rw_file_perms;
allow vpud_native thermal_manager_data_file:dir w_dir_perms;
allow vpud_native thermal_manager_data_file:file create_file_perms;
