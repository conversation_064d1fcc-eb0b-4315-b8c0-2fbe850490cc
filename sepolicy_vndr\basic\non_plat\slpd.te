# ==============================================
# Policy File of /vendor/bin/slpd Executable File

# ==============================================
# Type Declaration
# ==============================================
type slpd_exec, exec_type, file_type, vendor_file_type;
type slpd, domain;

# ==============================================
# Common SEPolicy Rule
# ==============================================
init_daemon_domain(slpd)

net_domain(slpd)

# mtk_agpsd will send the current SUPL profile to SLPD
allow slpd mtk_agpsd:unix_dgram_socket sendto;
