# ==============================================
# Common SEPolicy Rule
# ==============================================

# Purpose: data/dumpsys/*
allow dumpstate aee_dumpsys_data_file:dir w_dir_perms;
allow dumpstate aee_dumpsys_data_file:file create_file_perms;

# Purpose: debugfs files
allow dumpstate procfs_blockio:file r_file_perms;

# Purpose: /sys/kernel/ccci/md_chn
allow dumpstate sysfs_ccci:dir search;
allow dumpstate sysfs_ccci:file r_file_perms;

# Purpose: leds status
allow dumpstate sysfs_leds:lnk_file r_file_perms;

# Purpose: /sys/module/lowmemorykiller/parameters/adj
allow dumpstate sysfs_lowmemorykiller:file r_file_perms;
allow dumpstate sysfs_lowmemorykiller:dir search;

# Purpose: /dev/block/mmcblk0p10
allow dumpstate expdb_block_device:blk_file rw_file_perms;

#/data/anr/SF_RTT
allow dumpstate sf_rtt_file:dir { search getattr };

allow dumpstate sysfs_leds:dir r_dir_perms;

# Data : WK17.03
# Purpose: Allow to access gpu
allow dumpstate gpu_device:dir search;

# Purpose: Allow dumpstate to read /proc/ufs_debug
allow dumpstate proc_ufs_debug:file rw_file_perms;

# Purpose: Allow dumpstate to read /proc/msdc_debug
allow dumpstate proc_msdc_debug:file r_file_perms;

# Purpose: Allow dumpstate to r/w /proc/pidmap
allow dumpstate proc_pidmap:file rw_file_perms;

# Purpose: Allow dumpstate to read /sys/power/vcorefs/vcore_debug
allow dumpstate sysfs_vcore_debug:file r_file_perms;

# Purpose: Allow dumpstate to read /data/anr/SF_RTT/rtt_dump.txt
allow dumpstate sf_rtt_file:file r_file_perms;

#Purpose: Allow dumpstate to read/write /sys/mtk_memcfg/slabtrace
allow dumpstate proc_slabtrace:file r_file_perms;

#Purpose: Allow dumpstate to read /proc/mtk_cmdq_debug/status
allow dumpstate proc_cmdq_debug:file r_file_perms;

#Purpose: Allow dumpstate to read /proc/cpuhvfs/dbg_repo
allow dumpstate proc_dbg_repo:file r_file_perms;

#Purpose: Allow dumpstate to read /proc/isp_p2/isp_p2_dump
allow dumpstate proc_isp_p2_dump:file r_file_perms;

#Purpose: Allow dumpstate to read /proc/isp_p2/isp_p2_kedump
allow dumpstate proc_isp_p2_kedump:file r_file_perms;

#Purpose: Allow dumpstate to read /proc/mali/memory_usage
allow dumpstate proc_memory_usage:file r_file_perms;

#Purpose: Allow dumpstate to read /proc/mtk_es_reg_dump
allow dumpstate proc_mtk_es_reg_dump:file r_file_perms;

#Purpose: Allow dumpstate to read /sys/power/mtkpasr/execstate
allow dumpstate sysfs_execstate:file r_file_perms;

allow dumpstate proc_isp_p2:dir r_dir_perms;
allow dumpstate proc_isp_p2:file r_file_perms;

# Data : WK16.42
# Operator: Whitney bring up
# Purpose: call surfaceflinger due to powervr
allow dumpstate surfaceflinger:fifo_file rw_file_perms;

# Date : W19.26
# Operation : Migration
# Purpose : fix google dumpstate avc error in xTS
allow dumpstate debugfs_mmc:dir search;
allow dumpstate mnt_media_rw_file:dir getattr;

# Date: 19/07/15
# Purpose: fix google dumpstate avc error in xTs
allow dumpstate sysfs_devices_block:file r_file_perms;
allow dumpstate proc_last_kmsg:file r_file_perms;

#Purpose: Allow dumpstate to read /sys/class/misc/adsp/adsp_last_log
allow dumpstate sysfs_adsp:file r_file_perms;

# MTEE Trusty
allow dumpstate mtee_trusty_file:file rw_file_perms;

# 09-05 15:58:31.552000  9693  9693 W df      : type=1400 audit(0.0:990):
# avc: denied { search } for name="expand" dev="tmpfs" ino=10779 scontext=u:r:dumpstate:s0
# tcontext=u:object_r:mnt_expand_file:s0 tclass=dir permissive=0
allow dumpstate mnt_expand_file:dir { search getattr };

#Purpose: Allow dumpstate to read /dev/usb-ffs
allow dumpstate functionfs:file getattr;

#Purpose: Allow dumpstate to read /sys/bus/platform/drivers/cache_parity/cache_status
allow dumpstate sysfs_cache_status:file r_file_perms;

hal_client_domain(dumpstate, hal_light)

#Purpose: Allow dumpstate to read /sys/kernel/tracing/instances/mmstat/trace
allow dumpstate debugfs_tracing_instances:dir r_dir_perms;
allow dumpstate debugfs_tracing_instances:file r_file_perms;

allow dumpstate proc_ion:dir r_dir_perms;
allow dumpstate proc_ion:file r_file_perms;
allow dumpstate proc_m4u_dbg:dir r_dir_perms;
allow dumpstate proc_m4u_dbg:file r_file_perms;
allow dumpstate proc_mtkfb:file r_file_perms;

allow dumpstate proc_ccci_dump:file r_file_perms;
