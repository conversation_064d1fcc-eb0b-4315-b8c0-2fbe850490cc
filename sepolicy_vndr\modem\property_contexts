#=============allow wifi offload deamon  ==============
vendor.wo.   u:object_r:vendor_mtk_wod_prop:s0
persist.vendor.wo.   u:object_r:vendor_mtk_persist_wod_prop:s0

#=============allow volte deamon  ==============
ctl.vendor.volte_imcb          u:object_r:vendor_mtk_ctl_volte_imcb_prop:s0
ctl.vendor.volte_stack         u:object_r:vendor_mtk_ctl_volte_stack_prop:s0
ctl.vendor.volte_ua            u:object_r:vendor_mtk_ctl_volte_ua_prop:s0
vendor.ril.volte.              u:object_r:vendor_mtk_md_volte_prop:s0

#=============allow MD APP==============
ro.vendor.md_apps.             u:object_r:vendor_mtk_default_prop:s0
vendor.md_apps.                u:object_r:vendor_mtk_default_prop:s0

#=============allow MD status==============
vendor.volte_md_status         u:object_r:vendor_mtk_md_status_prop:s0
