# ==============================================
# Common SEPolicy Rule
# ==============================================

##########################
# System files
#
# MTK Adv Camera Server
/system/bin/mtk_advcamserver u:object_r:mtk_advcamserver_exec:s0

/system/bin/kpoc_charger u:object_r:kpoc_charger_exec:s0

# MTK Thermald
/system/bin/thermald u:object_r:thermald_exec:s0

# MTK VTService
/system/bin/vtservice u:object_r:vtservice_exec:s0

# MTK ATCI
/system/bin/atci_service_sys u:object_r:atci_service_sys_exec:s0

# MTK Postalgo
/system/bin/camerapostalgo u:object_r:camerapostalgo_exec:s0

# MTK AAL
/system/bin/aal u:object_r:aal_exec:s0

# MTK Carrier express
/system/bin/usp_service     u:object_r:usp_service_exec:s0
/system/bin/batterywarning  u:object_r:batterywarning_exec:s0
/system/bin/mmp             u:object_r:mmp_exec:s0
/system/bin/GoogleOtaBinder u:object_r:GoogleOtaBinder_exec:s0

# MTK MET
/system/bin/met_log_d u:object_r:met_log_d_exec:s0

# MTK TerService
/system/bin/terservice u:object_r:terservice_exec:s0

# MTK MAPI (Modem Diagnostic Public Interface)
/system/bin/mdi_redirector u:object_r:mdi_redirector_exec:s0

# MTK MDMI test tool (Modem Diagnostic Monitoring Interface)
/system/bin/mdmi_redirector u:object_r:mdmi_redirector_exec:s0

# resize userdata's filesystem size
/system/bin/resize.f2fs u:object_r:resize_exec:s0

# VSIM 3.0
/system/bin/osi u:object_r:osi_exec:s0

##########################
# Devices
#
# MTK touchll hal
/dev/tll u:object_r:tll_device:s0

##########################
# Others
#
/eng u:object_r:rootfs:s0

