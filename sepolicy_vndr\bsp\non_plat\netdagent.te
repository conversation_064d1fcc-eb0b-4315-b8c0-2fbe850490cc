# ==============================================
# Policy File of /vendor/bin/netdagent Executable File

# ==============================================
# Common SEPolicy Rule
# ==============================================

type netdagent_exec, exec_type, file_type, vendor_file_type;

init_daemon_domain(netdagent)
domain_auto_trans(netdagent, netutils_wrapper_exec, netutils_wrapper)

allow netdagent devpts:chr_file rw_file_perms;
allow netdagent usermodehelper:file r_file_perms;

allow netdagent self:netlink_route_socket { connect create setopt bind getattr nlmsg_read nlmsg_write read write };

set_prop(netdagent, vendor_mtk_netdagent_prop)
allow netdagent proc_net:file rw_file_perms;
allow netdagent kernel:system module_request;

hal_server_domain(netdagent, mtk_hal_netdagent)

