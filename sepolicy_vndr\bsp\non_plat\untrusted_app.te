# ==============================================
# Common SEPolicy Rule
# ==============================================

# TODO:: Security Issue
# Date : W1452
# Operation : WVL1 Modular DRM IT
# Purpose : Allow svp client alloc sec mem
allow untrusted_app proc_secmem:file r_file_perms;

# TrustKernel add
userdebug_or_eng(`
	allow untrusted_app tkcore_admin_device:chr_file rw_file_perms;
')

# Date : 2016/07/12
# Operation : SQC
# Purpose : allow untrusted_app access ntfs device
allow untrusted_app fuseblk:dir search;
allow untrusted_app fuseblk:file rw_file_perms;

# Date : 2016/7/22
# Operation: Migration
# Purpose : Move from tk sepolicy for ViLTE
allow untrusted_app vtservice:dir search;
allow untrusted_app mediaserver:dir search;

# Date: 2018/04/18
# Purpose: Allow untrusted_app to use HIDL and access mtk_hal_neuralnetworks
allow untrusted_app mtk_hal_neuralnetworks:binder { call transfer };
allow untrusted_app debugfs_ion:dir search;

# Date: 2020/06/29
# Operation : eMBMS Migration
# Purpose :allow EXPWAY middleware to access the socket
allow untrusted_app radio:unix_stream_socket connectto;

# Purpose: Allow untrusted_app to access mdlactl_device and vpu_device
allow untrusted_app mdla_device:chr_file { rw_file_perms };
allow untrusted_app vpu_device:chr_file { rw_file_perms };

# Purpose: Allow untrusted_app to access mcdi device
allow untrusted_app proc_mcdi:dir search;
allow untrusted_app proc_mcdi:file rw_file_perms;
allow untrusted_app proc_mcdi:chr_file rw_file_perms;

# Purpose: Netflix Widevine
allow untrusted_app proc_atf_log:dir search;
allow untrusted_app proc_m4u:dir search;
get_prop(untrusted_app, vendor_mtk_microtrust_tee_prop)
get_prop(untrusted_app, vendor_mtk_trustonic_tee_prop)
