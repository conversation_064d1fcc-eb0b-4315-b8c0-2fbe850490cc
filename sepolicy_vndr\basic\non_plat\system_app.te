# ==============================================
# Common SEPolicy Rule
# ==============================================

typeattribute system_app mlstrustedsubject;

# Date : 2017/07/21
# Purpose :[CdsInfo] read/ write WI-FI MAC address by NVRAM API
# Package Name: com.mediatek.connectivity
hal_client_domain(system_app, hal_mtk_nvramagent)

hal_client_domain(system_app, hal_mtk_lbs)

# Dat: 2017/02/14
# Purpose: allow set telephony Sensitive property
get_prop(system_app, vendor_mtk_telephony_sensitive_prop)

# Date : WK17.12
# Operation : MT6799 SQC
# Purpose : Change thermal config
get_prop(system_app, vendor_mtk_thermal_config_prop)

# Date: 2019/07/16
# Operation : Migration
# Purpose : system_app need use hdmi service and create socktet
hal_client_domain(system_app, hal_mtk_hdmi)
allow system_app self:netlink_kobject_uevent_socket { read bind create setopt };

# system_app need to read from sysfs /sys/class/switch/hdmi/state
r_dir_file(system_app, sysfs_switch)

# Date: 2020/06/08
# Purpose: Allow system app to access mtk jpeg
allow system_app proc_mtk_jpeg:file rw_file_perms;
allowxperm system_app proc_mtk_jpeg:file ioctl {
      JPG_BRIDGE_DEC_IO_LOCK
      JPG_BRIDGE_DEC_IO_WAIT
      JPG_BRIDGE_DEC_IO_UNLOCK
};

# Date: 2020/06/29
# Purpose: Allow system app to access mtk fpsgo
allow system_app sysfs_fpsgo:dir search;
allow system_app sysfs_fpsgo:file r_file_perms;

# MTK FM Radio
allow system_app fm_device:chr_file rw_file_perms;
