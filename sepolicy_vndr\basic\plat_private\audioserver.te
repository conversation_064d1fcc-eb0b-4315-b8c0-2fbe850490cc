# ==============================================
# Common SEPolicy Rule
# ==============================================

# Date : WK14.32
# Operation : Migration
# Purpose : For audio dump and log
allow audioserver sdcard_type:dir create_dir_perms;

# Data : WK14.38
# Operation : Migration
# Purpose : for boot animation.
binder_call(audioserver, bootanim)
binder_call(audioserver, mtkbootanimation)

# Data : WK14.46
# Operation : Migration
# Purpose : for SMS app
allow audioserver radio_data_file:dir search;
allow audioserver radio_data_file:file open;

# Data : WK14.47
# Operation : Audio playback
# Purpose : Music as ringtone
allow audioserver radio:dir r_dir_perms;
allow audioserver radio:file r_file_perms;

# Data : WK14.47
# Operation : CTS
# Purpose : cts search strange app
allow audioserver untrusted_app:dir search;

# Date : WK15.34
# Operation : Migration
# Purpose: for camera middleware dump image buffer to sdcard & audio frameworks dump
allow audioserver storage_file:lnk_file rw_file_perms;
allow audioserver mnt_user_file:dir rw_dir_perms;
allow audioserver mnt_user_file:lnk_file rw_file_perms;

# Purpose: Dump debug info
allow audioserver kmsg_device:chr_file w_file_perms;
allow audioserver media_rw_data_file:dir create_dir_perms;

# Date : WK16.27
# Operation : Migration
# Purpose: tunning tool update parameters
allow audioserver media_rw_data_file:file create_file_perms;

# Date : WK16.28
# Operation : Migration
# Purpose: Write audio dump files to external SDCard.
allow audioserver sdcard_type:file create_file_perms;
allow audioserver storage_file:dir r_dir_perms;

# Date : W18.01
# Add for turn on SElinux in enforcing mode
allow audioserver self:netlink_kobject_uevent_socket create_socket_perms_no_ioctl;

# Date : WK20.14
# Operation : Migartion
# Purpose : MTK Audio debug use
set_prop(audioserver, system_mtk_audio_prop)
