# ==============================================
# Common SEPolicy Rule
# ==============================================

type mdlogger_exec , system_file_type, exec_type, file_type;
typeattribute mdlogger coredomain;
typeattribute mdlogger mlstrustedsubject;

init_daemon_domain(mdlogger)

binder_use(mdlogger)

binder_service(mdlogger)

# modem logger socket access
allow mdlogger platform_app:unix_stream_socket connectto;
allow mdlogger shell_exec:file rx_file_perms;
allow mdlogger system_file:file x_file_perms;
allow mdlogger zygote_exec:file r_file_perms;
allow mdlogger node:tcp_socket node_bind;
allow mdlogger port:tcp_socket name_bind;
allow mdlogger self:tcp_socket create_stream_socket_perms;

#modem logger SD logging in factory mode
allow mdlogger vfat:dir create_dir_perms;
allow mdlogger vfat:file create_file_perms;

allow mdlogger tmpfs:lnk_file r_file_perms;
allow mdlogger storage_file:lnk_file rw_file_perms;
allow mdlogger mnt_user_file:dir search;
allow mdlogger mnt_user_file:lnk_file rw_file_perms;
allow mdlogger sdcard_type:file create_file_perms;
allow mdlogger sdcard_type:dir create_dir_perms;

# purpose: allow mdlogger to access storage in new version
allow mdlogger media_rw_data_file:file create_file_perms;
allow mdlogger media_rw_data_file:dir create_dir_perms;

allow mdlogger storage_file:dir create_dir_perms;
allow mdlogger storage_file:file create_file_perms;

## purpose: avc: denied { read } for name="plat_file_contexts"
allow mdlogger file_contexts_file:file r_file_perms;

# Allow read avc: denied { read } for name="mddb" dev="mmcblk0p25" ino=681
# scontext=u:r:mdlogger:s0 tcontext=u:object_r:system_file:s0 tclass=dir permissive=0
allow mdlogger system_file:dir r_dir_perms;

# Android P migration
set_prop(mdlogger, system_mtk_mdl_prop)
set_prop(mdlogger, system_mtk_persist_mdlog_prop)
set_prop(mdlogger, system_mtk_persist_mtklog_prop)

## Android Q migration
## purpose:  read modem db and filter folder and file
allow mdlogger mddb_filter_data_file:dir r_dir_perms;
allow mdlogger mddb_filter_data_file:file r_file_perms;

## Save modem log into data
allow mdlogger debuglog_data_file:dir {relabelto create_dir_perms};
allow mdlogger debuglog_data_file:file create_file_perms;

#allow mdlogger to set property
set_prop(mdlogger, system_mtk_debug_mdlogger_prop)
set_prop(mdlogger, debug_prop)
