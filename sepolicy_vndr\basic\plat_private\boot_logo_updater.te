# ==============================================
# Policy File of /system/bin/boot_logo_updater Executable File

typeattribute boot_logo_updater coredomain;
type boot_logo_updater_exec, system_file_type, exec_type, file_type;

# ==============================================
# Common SEPolicy Rule
# ==============================================

init_daemon_domain(boot_logo_updater)

# Date : WK14.32
# Operation : Migration
# Puration : set boot reason
set_prop(boot_logo_updater, system_prop)

allow boot_logo_updater graphics_device:chr_file rw_file_perms;

# To access directory /dev/block/mmcblk0 or /dev/block/sdc
allow boot_logo_updater block_device:dir search;
allow boot_logo_updater graphics_device:dir search;

# to access file at  /dev/block/mtd
allow boot_logo_updater mtd_device:chr_file r_file_perms;
allow boot_logo_updater mtd_device:dir search;

#To access the file at   /dev/kmsg
allow boot_logo_updater kmsg_device:chr_file w_file_perms;

#To the access /fstab mount point
allow boot_logo_updater rootfs:file r_file_perms;

#To access linux filesystem
allow boot_logo_updater sysfs:dir r_dir_perms;

# sanity fail for ALPS03604686:
# for path="/sys/firmware/devicetree/base/firmware/android/fstab" andfor name = "cmdline" and "mtdblock14"
allow boot_logo_updater mtd_device:blk_file r_file_perms;
