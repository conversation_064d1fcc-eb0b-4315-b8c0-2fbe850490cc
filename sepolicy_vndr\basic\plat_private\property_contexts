# ==============================================
# Common SEPolicy Rule
# ==============================================

persist.adb.nonblocking_ffs u:object_r:exported_default_prop:s0 exact int

vendor.MB.logpost         u:object_r:system_mtk_mobile_log_post_prop:s0
vendor.MB.logpost.        u:object_r:system_mtk_mobile_log_post_prop:s0
persist.vendor.MB.logpost u:object_r:system_mtk_mobile_log_post_prop:s0

vendor.mtklog         u:object_r:system_mtk_debug_mtklog_prop:s0
persist.vendor.mtklog u:object_r:system_mtk_persist_mtklog_prop:s0
vendor.netlog         u:object_r:system_mtk_debug_netlog_prop:s0

vendor.mdlogger      u:object_r:system_mtk_debug_mdlogger_prop:s0
vendor.mdl           u:object_r:system_mtk_mdl_prop:s0
vendor.starting.mode u:object_r:system_mtk_mdl_start_prop:s0
persist.vendor.mdl   u:object_r:system_mtk_persist_mdlog_prop:s0
vendor.pullmdlog     u:object_r:system_mtk_mdl_pulllog_prop:s0

vendor.debug.bq.dump u:object_r:system_mtk_debug_bq_dump_prop:s0

persist.vendor.bootanim. u:object_r:system_mtk_bootani_prop:s0

# mobile log property
vendor.MB. u:object_r:system_mtk_mobile_log_prop:s0

persist.vendor.radio.bgdata.disabled u:object_r:system_mtk_bgdata_disabled_prop:s0

persist.vendor.radio.gprs.attach.type u:object_r:system_mtk_gprs_attach_type_prop:s0

persist.vendor.mtk_rcs_single_reg_support u:object_r:system_mtk_rcs_single_reg_support_prop:s0

persist.vendor.pco5.radio.ctrl u:object_r:system_mtk_pco_prop:s0

vendor.ril.test.poweroffmd u:object_r:system_mtk_power_off_md_prop:s0
vendor.ril.testmode        u:object_r:system_mtk_power_off_md_prop:s0

# sim config property
vendor.gsm.sim.operator.default-name u:object_r:system_mtk_sim_system_prop:s0

vendor.connsysfw u:object_r:system_mtk_connsysfw_prop:s0

vendor.bthcisnoop u:object_r:system_mtk_vendor_bluetooth_prop:s0

# xcap rawurl config
persist.vendor.mtk.xcap.rawurl u:object_r:system_mtk_persist_xcap_rawurl_prop:s0

ctl.mdlogger   u:object_r:system_mtk_ctl_mdlogger_prop:s0
ctl.emdlogger1 u:object_r:system_mtk_ctl_emdlogger1_prop:s0
ctl.emdlogger2 u:object_r:system_mtk_ctl_emdlogger2_prop:s0
ctl.emdlogger3 u:object_r:system_mtk_ctl_emdlogger3_prop:s0

init.svc.emdlogger1 u:object_r:system_mtk_init_svc_emdlogger1_prop:s0

#=============mtk wifi driver log property====================
persist.vendor.wlan.standalone.log        u:object_r:system_mtk_wifisa_log_prop:s0

# mtk audio log and dump property
vendor.af.mixer.pcm           u:object_r:system_mtk_audio_prop:s0
vendor.af.track.pcm           u:object_r:system_mtk_audio_prop:s0
vendor.af.offload.write.raw   u:object_r:system_mtk_audio_prop:s0
vendor.af.resampler.pcm       u:object_r:system_mtk_audio_prop:s0
vendor.af.mixer.end.pcm       u:object_r:system_mtk_audio_prop:s0
vendor.af.record.dump.pcm     u:object_r:system_mtk_audio_prop:s0
vendor.af.effect.pcm          u:object_r:system_mtk_audio_prop:s0
vendor.af.mixer.drc.pcm       u:object_r:system_mtk_audio_prop:s0
vendor.af.dumplog             u:object_r:system_mtk_audio_prop:s0
vendor.aaudio.pcm             u:object_r:system_mtk_audio_prop:s0
vendor.af.audioflinger.log    u:object_r:system_mtk_audio_prop:s0
vendor.af.track.log           u:object_r:system_mtk_audio_prop:s0
vendor.af.policy.debug        u:object_r:system_mtk_audio_prop:s0
vendor.af.audioserver.restart u:object_r:system_mtk_audio_prop:s0

# mtk display driver log property
vendor.debug.sf.log_repaint      u:object_r:system_mtk_sf_debug_prop:s0
vendor.debug.sf.log_transaction  u:object_r:system_mtk_sf_debug_prop:s0
vendor.debug.sf.restart          u:object_r:system_mtk_sf_debug_prop:s0
