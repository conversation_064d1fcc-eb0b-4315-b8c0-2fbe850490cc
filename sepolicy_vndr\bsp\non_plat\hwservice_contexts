# ==============================================
# Common SEPolicy Rule
# ==============================================

# Date : 2017/06/1
vendor.mediatek.hardware.camera.advcam::IAdvCamControl u:object_r:hal_camera_hwservice:s0

# Date : 2017/06/15
vendor.mediatek.hardware.wfo::IWifiOffload u:object_r:mtk_hal_wfo_hwservice:s0

# Date: 2017/06/22
vendor.mediatek.hardware.camera.lomoeffect::ILomoEffect u:object_r:hal_camera_hwservice:s0

# Date : 2017/07/11
vendor.mediatek.hardware.videotelephony::IVideoTelephony u:object_r:mtk_hal_videotelephony_hwservice:s0

# Date : 2017/07/20
vendor.mediatek.hardware.presence::IPresence u:object_r:volte_uce_ua_hwservice:s0

# Date: 2017/09/06
vendor.mediatek.hardware.netdagent::INetdagent u:object_r:mtk_hal_netdagent_hwservice:s0

# Date : 2017/08/4
vendor.mediatek.hardware.rcs::IRcs u:object_r:volte_rcs_ua_hwservice:s0

# Date: 2017/06/22
vendor.mediatek.hardware.camera.ccap::ICCAPControl u:object_r:hal_camera_hwservice:s0

# Date : 2017/10/22
vendor.mediatek.hardware.dfps::IFpsPolicyService u:object_r:mtk_hal_dfps_hwservice:s0

# Date : 2018/11/13
vendor.mediatek.hardware.dplanner::IDPlanner u:object_r:mtk_hal_dplanner_hwservice:s0

# Date : 2018/01/04
# tablet DRM Key Manage HIDL
vendor.mediatek.hardware.keymanage::IKeymanage u:object_r:mtk_hal_keymanage_hwservice:s0

# DRM Key Installation HIDL
vendor.mediatek.hardware.keyinstall::IKeyinstall u:object_r:mtk_hal_keyinstall_hwservice:s0

# Date: 2018/05/07
vendor.mediatek.hardware.pplagent::IPplAgent u:object_r:mtk_hal_pplagent_hwservice:s0

# Date : 2019/05/14
# Android Q diagnostic framework migration
vendor.mediatek.hardware.dmc::IDmcService u:object_r:mtk_hal_dmc_hwservice:s0

# Date : 2019/05/14
# Android Q diagnostic framework migration
vendor.mediatek.hardware.apmonitor::IApmService u:object_r:mtk_hal_apm_hwservice:s0

# MICROTRUST SEPolicy Rule
# microtrust THH hidl
vendor.microtrust.hardware.thh::IThhDevice u:object_r:teei_hal_thh_hwservice:s0

# microtrust TUI hidl
vendor.microtrust.hardware.tui::ITuiDevice u:object_r:teei_hal_tui_hwservice:s0

# microtrust IFAA hidl
vendor.microtrust.hardware.ifaa::IIFAADevice u:object_r:teei_hal_ifaa_hwservice:s0

# microtrust Client Api hidl
vendor.microtrust.hardware.capi::IClientApiDevice u:object_r:teei_hal_capi_hwservice:s0

# microtrust wechat hidl
vendor.microtrust.hardware.soter::ISoter u:object_r:teei_hal_wechat_hwservice:s0

# Date : 2018/05/14
# IMtkSupplicant hidl, to export Mediatek supplicant hidl interface to framework
vendor.mediatek.hardware.wifi.supplicant::ISupplicant u:object_r:hal_wifi_supplicant_hwservice:s0

# Date : 2018/07/16
vendor.mediatek.hardware.camera.security::ISecureCamera u:object_r:hal_camera_hwservice:s0

# Date : 2018/08/27
vendor.mediatek.hardware.camera.frhandler::IFRHandler u:object_r:hal_camera_hwservice:s0

# Date : 2019/05/16
# Operation : IT
# Purpose : Add for HIDL service
vendor.mediatek.hardware.mdmonitor::IMDMonitorService u:object_r:mtk_mdm_hidl_server:s0

# omadm hidl
vendor.mediatek.hardware.omadm::IOmadm u:object_r:mtk_hal_omadm_hwservice:s0

# nwk opt HIDL
vendor.mediatek.hardware.nwk_opt::INwkOpt u:object_r:mtk_hal_nwk_opt_hwservice:s0

# Date : 2018/10/25
vendor.mediatek.hardware.clientapi::IClientapi u:object_r:volte_clientapi_ua_hwservice:s0

# Date: 2019/09/25
vendor.mediatek.hardware.touchll::ITouchll u:object_r:mtk_hal_touchll_hwservice:s0

# Date: 2019/11/07
vendor.mediatek.hardware.thp::ITHP u:object_r:mtk_hal_thp_hwservice:s0

# Trustonic SEPolicy Rule
vendor.trustonic.tee::ITee                 u:object_r:hal_tee_hwservice:s0
vendor.trustonic.tee.tui::ITui             u:object_r:hal_tee_hwservice:s0
vendor.trustonic.teeregistry::ITeeRegistry u:object_r:hal_teeregistry_hwservice:s0

# HDCP SEPolicy Rule
vendor.tesiai.hardware.hdcpconnection::IHDCPConnection u:object_r:tesiai_hal_hdcp_hwservice:s0

# Date : 2020/06/10
# Define apuware context
vendor.mediatek.hardware.apuware.apusys::INeuronApusys u:object_r:hal_neuralnetworks_hwservice:s0
vendor.mediatek.hardware.apuware.xrp::INeuronXrp       u:object_r:hal_neuralnetworks_hwservice:s0

# Date : 2020/07/03
# Define uitls hidl context for ann
vendor.mediatek.hardware.apuware.utils::IApuwareUtils u:object_r:hal_neuralnetworks_hwservice:s0

# Date : 2021/04/23
# Define hml hidl context
vendor.mediatek.hardware.apuware.hmp::IApuwareHmp u:object_r:hal_neuralnetworks_hwservice:s0

# Date : 2021/06/28
# Define NpAgent hidl context for ann
vendor.mediatek.hardware.neuropilot.agent::IAgent u:object_r:hal_neuralnetworks_hwservice:s0
