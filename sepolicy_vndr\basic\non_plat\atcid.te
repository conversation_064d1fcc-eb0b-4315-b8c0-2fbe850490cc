# ==============================================
# Policy File of /vendor/bin/atcid Executable File
# ==============================================

# ==============================================
# Common SEPolicy Rule
# ==============================================
type atcid, domain;
type atcid_exec, exec_type, file_type, vendor_file_type;

init_daemon_domain(atcid)
set_prop(atcid, vendor_mtk_persist_service_atci_prop)
allow atcid block_device:dir search;
allow atcid gsmrild_socket:sock_file w_file_perms;

# Date : WK17.21
# Purpose: Allow to use HIDL
hal_client_domain(atcid, hal_telephony)

allow atcid ttyGS_device:chr_file rw_file_perms;
allow atcid wmtWifi_device:chr_file w_file_perms;
allow atcid misc2_block_device:blk_file rw_file_perms;
allow atcid self:capability sys_time;

# Date : WK16.33
# Purpose: Allow to access ged for gralloc_extra functions
allow atcid proc_ged:file rw_file_perms;

# Date : WK17.23
# Stage: O Migration, SQC
# Purpose: Allow to use HAL PQ
hal_client_domain(atcid, hal_mtk_pq)

# Date : WK17.34
# Purpose: Allow to access meta_tst
allow atcid meta_tst:unix_stream_socket connectto;

# Date : WK18.15
# Purpose: Allow to access power_supply in sysfs
allow atcid sysfs_batteryinfo:file r_file_perms;

# Date : WK18.16
# Operation: P migration
# Purpose: Allow atcid to get vendor_mtk_tel_switch_prop
get_prop(atcid, vendor_mtk_tel_switch_prop)

# Date : WK18.21
# Purpose: Allow to use HIDL
vndbinder_use(atcid)
hal_server_domain(atcid, hal_mtk_atci)

# Date : WK18.21
# Purpose: For special command for customer
set_prop(atcid, vendor_mtk_atci_prop)
set_prop(atcid, powerctl_prop)
allow atcid mnt_vendor_file:dir search;
allow atcid nvdata_file:dir rw_dir_perms;
allow atcid nvdata_file:file create_file_perms;
allow atcid nvram_device:blk_file rw_file_perms;
allow atcid proc_meminfo:file r_file_perms;
allow atcid sysfs_batteryinfo:dir search;
allow atcid sysfs_devices_block:dir search;
allow atcid sysfs_devices_block:file r_file_perms;

# Date : WK18.35
# Purpose: Add socket for TelephonyWare ATCI
unix_socket_connect(atcid, rild_atci, rild)
unix_socket_connect(atcid, rilproxy_atci, rild)
unix_socket_connect(atcid, atci_service, atci_service)

# Date : WK19.42
# Purpose: Add policy to access ATCI sockets
unix_socket_connect(atcid, atci-audio, audiocmdservice_atci)
unix_socket_connect(atcid, meta_atci, meta_tst)
allow atcid adb_atci_socket:sock_file w_file_perms;

# Date : WK21.13
# Purpose: Add policy to access CCCI
allow atcid sysfs_ccci:dir search;
allow atcid sysfs_ccci:file r_file_perms;
allow atcid gsm0710muxd_device:chr_file rw_file_perms;

# Date : WK21.22
unix_socket_connect(atcid, factory_atci, factory);
set_prop(atcid, vendor_mtk_factory_start_prop)

# Date : WK21.31
# Purpose: Add policy to support uart
allow atcid sysfs_boot_info:file r_file_perms;
allow atcid sysfs_meta_info:file r_file_perms;
allow atcid ttyS_device:chr_file rw_file_perms;

