# ==============================================
# Policy File of /vendor/bin/ccci_fsd Executable File

# ==============================================
# Common SEPolicy Rule
# ==============================================

type ccci_rpcd_exec, exec_type, file_type, vendor_file_type;

init_daemon_domain(ccci_rpcd)

wakelock_use(ccci_rpcd)

allow ccci_rpcd ccci_device:chr_file rw_file_perms;
allow ccci_rpcd block_device:dir search;
allow ccci_rpcd misc2_block_device:blk_file rw_file_perms;
allow ccci_rpcd bootdevice_block_device:blk_file rw_file_perms;

allow ccci_rpcd sysfs_ccci:dir search;
allow ccci_rpcd sysfs_ccci:file r_file_perms;

allow ccci_rpcd md_block_device:blk_file r_file_perms;
