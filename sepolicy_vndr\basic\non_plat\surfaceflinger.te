# ==============================================
# Common SEPolicy Rule
# ==============================================

# Data : WK14.42
# Operation : Migration
# Purpose : Video playback
allow surfaceflinger sw_sync_device:chr_file rw_file_perms;

# Date : WK16.33
# Purpose: Allow to access ged for gralloc_extra functions
allow surfaceflinger proc_ged:file rw_file_perms;
allowxperm surfaceflinger proc_ged:file ioctl { proc_ged_ioctls };

# Date : W16.42
# Operation : Integration
# Purpose : DRM / DRI GPU driver required
allow surfaceflinger gpu_device:dir search;

# Date : WK17.12
# Purpose: Fix bootup fail
allow surfaceflinger proc_bootprof:file r_file_perms;

allow surfaceflinger debugfs_ion:dir search;
allow surfaceflinger kernel:dir search;

# Date : WK17.30
# Operation : O Migration
# Purpose: Allow to access cmdq driver
allow surfaceflinger mtk_cmdq_device:chr_file r_file_perms;
allow surfaceflinger mtk_mdp_device:chr_file r_file_perms;
allow surfaceflinger mtk_mdp_sync_device:chr_file r_file_perms;
allow surfaceflinger sysfs_boot_mode:file r_file_perms;

# Date : W17.39
# Perform Binder IPC.
binder_use(surfaceflinger)
binder_call(surfaceflinger, binderservicedomain)
binder_call(surfaceflinger, appdomain)
binder_call(surfaceflinger, mtkbootanimation)

binder_call(surfaceflinger, mtk_hal_camera)

binder_service(surfaceflinger)

allow surfaceflinger mtkbootanimation:dir search;
allow surfaceflinger mtkbootanimation:file r_file_perms;

# Date : W17.43
# Operation : Migration
# Purpose: Allow to access perfmgr
allow surfaceflinger proc_perfmgr:dir r_dir_perms;
allowxperm surfaceflinger proc_perfmgr:file ioctl {
  PERFMGR_FPSGO_QUEUE
  PERFMGR_FPSGO_DEQUEUE
  PERFMGR_FPSGO_QUEUE_CONNECT
  PERFMGR_FPSGO_BQID
  PERFMGR_FPSGO_VSYNC
  PERFMGR_XGFFRAME_START
  PERFMGR_XGFFRAME_END
};

# Date : WK17.43
# Operation : Debug
# Purpose: Allow to dump HWC backtrace
get_prop(surfaceflinger, vendor_mtk_graphics_hwc_pid_prop)
get_prop(surfaceflinger, vendor_mtk_graphics_hwc_validate_separate_prop)
get_prop(surfaceflinger, vendor_mtk_graphics_hwc_latch_unsignaled_prop)
allow surfaceflinger hal_graphics_composer_default:dir search;
allow surfaceflinger hal_graphics_composer_default:lnk_file r_file_perms;
dontaudit surfaceflinger hal_graphics_composer_default:file r_file_perms;

# Date : WK19.4
# Operation : P Migration
# Purpose: Allow to access /dev/mdp_device driver
allow surfaceflinger mdp_device:chr_file rw_file_perms;

# Date : WK18.43
# Operation : HDR
# Purpose: Allow to skip aosp hdr solution
get_prop(surfaceflinger, vendor_mtk_graphics_hwc_hdr_prop)

# Date: WK21.14
# Purpose: allow to check AppGamePQ is supported or not
get_prop(surfaceflinger, vendor_mtk_pq_ro_prop);

# Date: WK21.33
# Purpose: allow surfaceflinger to use PowerHAL API
allow surfaceflinger proc_perfmgr:file rw_file_perms;

# Data: 2021/09/18
# Purpose: adjust the uclamp for HWComposer
allow surfaceflinger hal_graphics_composer_default:process { getsched setsched };

# Data: 2021/10/12
# Purpose: adjust the cpu policy for HWComposer
allow surfaceflinger hal_graphics_composer_default:file w_file_perms;

# Data: 2021/12/07
# Purpose: adjust the cpu policy config
get_prop(surfaceflinger, vendor_mtk_debug_sf_cpupolicy_prop)
