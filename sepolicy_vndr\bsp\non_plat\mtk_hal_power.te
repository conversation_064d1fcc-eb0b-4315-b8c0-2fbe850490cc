# ==============================================
# Common SEPolicy Rule
# ==============================================

# Date : 2017/10/24
# Operation: SQC
# Purpose : Allow powerHAL to access dfps
allow mtk_hal_power mtk_hal_dfps:binder call;
hal_client_domain(mtk_hal_power, hal_dfps)

# Date : 2018/9/10
# Operation: netdagnt
allow mtk_hal_power mtk_hal_netdagent_hwservice:hwservice_manager find;
allow mtk_hal_power netdagent:binder call;

# Date : 2019/07/19
# Operation: NwkOpt
allow mtk_hal_power mtk_hal_nwk_opt:binder call;
hal_client_domain(mtk_hal_power, hal_nwk_opt)

# Date : 2019/09/19
# Operation: touchll
allow mtk_hal_power mtk_hal_touchll:binder call;
hal_client_domain(mtk_hal_power, hal_mtk_touchll)

# Date : 2020/05/19
# Operation: CapabilityTest
allow mtk_hal_power capability_app:dir { getattr search };
allow mtk_hal_power capability_app:file r_file_perms;

# Date : 2020/06/03
# Purpose : Allow PowerHAL to access Neuralnetworks HAL
allow mtk_hal_power mtk_hal_neuralnetworks:dir r_dir_perms;
allow mtk_hal_power mtk_hal_neuralnetworks:file r_file_perms;

# Allow Power to alter DT2W node
allow mtk_hal_power sysfs_keypad_file:file rw_file_perms;
