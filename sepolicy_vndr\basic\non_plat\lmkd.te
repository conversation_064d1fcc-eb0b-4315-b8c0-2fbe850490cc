# ==============================================
# Common SEPolicy Rule
# ==============================================

# Data : 2015/01/14
# Operation : MT6735 SQC bug fix
# Purpose : ALPS01905960 - selinux_warning: audit(1420845354.752:91): avc:  denied  { search }
#           for  pid=194 comm="lmkd" name="23573" dev="proc"
#           ino=915740 scontext=u:r:lmkd:s0 tcontext=u:r:zygote:s0 tclass=dir permissive=0
dontaudit lmkd zygote:dir rw_dir_perms;

# Data : 2015/04/17
# Operation : tb8163p1 low memory selinux warning
# Purpose : ALPS02038466 audit(1429079840.646:7): avc:  denied  { use }
#           for  pid=170 comm="lmkd"
#           path=2F6465762F6173686D656D2F4469736361726461626C654D656D6F72794173686D656D416C6C6F6361746F72202864656C6574656429
#           dev="tmpfs" ino=14475 scontext=u:r:lmkd:s0 tcontext=u:r:platform_app:s0 tclass=fd permissive=0
dontaudit lmkd platform_app:fd use;

# Data : 2018/05/25
# Operation : Add for duraSpeed socket
allow lmkd system_server:unix_stream_socket connectto;

# Data : 2021/04/20
# Operation : Add for powerhal mem index
hal_client_domain(lmkd, hal_power)
