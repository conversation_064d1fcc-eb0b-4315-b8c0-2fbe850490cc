# ==============================================
# Common SEPolicy Rule
# ==============================================

type lbs_hidl_service, domain;
type lbs_hidl_service_exec, exec_type, vendor_file_type, file_type;

init_daemon_domain(lbs_hidl_service)
hal_server_domain(lbs_hidl_service, hal_mtk_lbs)
vndbinder_use(lbs_hidl_service)

unix_socket_connect(lbs_hidl_service, agpsd, mtk_agpsd)
allow lbs_hidl_service mtk_agpsd:unix_dgram_socket sendto;
allow lbs_hidl_service mnld:unix_dgram_socket sendto;
