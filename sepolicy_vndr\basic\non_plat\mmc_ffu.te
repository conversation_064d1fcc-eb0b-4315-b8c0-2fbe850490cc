# ==============================================
# Policy File of /system/bin/mmc_ffu Executable File

# ==============================================
# Type Declaration
# ==============================================
type mmc_ffu, domain;
type mmc_ffu_exec, exec_type, file_type, vendor_file_type;

# ==============================================
# Common SEPolicy Rule
# ==============================================
init_daemon_domain(mmc_ffu)

# Purpose: ioctl to /dev/misc-sd and for obtaining emmc vendor id and firmware revision
allow mmc_ffu misc_sd_device:chr_file r_file_perms;

#Purpose: Write eMMC firmware data to /dev/block/mmcblk0 for upgrade firmware
allow mmc_ffu bootdevice_block_device:blk_file rw_file_perms;
