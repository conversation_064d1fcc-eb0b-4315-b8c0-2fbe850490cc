# ==============================================
# Common SEPolicy Rule
# ==============================================

# proc_ged ioctls
define(`proc_ged_ioctls', `{
  GED_BRIDGE_IO_LOG_BUF_GET
  GED_BRIDGE_IO_LOG_BUF_WRITE
  GED_BRIDGE_IO_LOG_BUF_RESET
  GED_BRIDGE_IO_BOOST_GPU_FREQ
  GED_BRIDGE_IO_MONITOR_3D_FENCE
  GED_BRIDGE_IO_QUERY_INFO
  GED_BRIDGE_IO_NOTIFY_VSYNC
  GED_BRIDGE_IO_DVFS_PROBE
  GED_BRIDGE_IO_DVFS_UM_RETURN
  GED_BRIDGE_IO_EVENT_NOTIFY
  GED_BRIDGE_IO_WAIT_HW_VSYNC
  GED_BRIDGE_IO_QUERY_TARGET_FPS
  GED_BRIDGE_IO_VSYNC_WAIT
  GED_BRIDGE_IO_GPU_HINT_TO_CPU
  GED_BRIDGE_IO_HINT_FORCE_MDP
  GED_BRIDGE_IO_QUERY_DVFS_FREQ_PRED
  GED_BRIDGE_IO_QUERY_GPU_DVFS_INFO
  GED_BRIDGE_IO_GE_ALLOC
  GED_BRIDGE_IO_GE_GET
  GED_BRIDGE_IO_GE_SET
  GED_BRIDGE_IO_GPU_TIMESTAMP
  GED_BRIDGE_IO_TARGET_FPS
  GED_BRIDGE_IO_GE_INFO
  GED_BRIDGE_IO_GPU_TUNER_STATUS
  GED_BRIDGE_IO_DMABUF_SET_NAME
  GED_BRIDGE_IO_CREATE_TIMELINE
}')
