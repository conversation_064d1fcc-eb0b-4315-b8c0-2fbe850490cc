# ==============================================
# Policy File of /system/bin/mtk_advcamserver Executable File

# ==============================================
# Common SEPolicy Rule
# ==============================================

type mtk_advcamserver_exec, system_file_type, exec_type, file_type;
typeattribute mtk_advcamserver coredomain;

init_daemon_domain(mtk_advcamserver)

binder_use(mtk_advcamserver)
hwbinder_use(mtk_advcamserver)
binder_call(mtk_advcamserver, mtk_advcamserver)
binder_service(mtk_advcamserver)
binder_call(mtk_advcamserver, binderservicedomain)
binder_call(mtk_advcamserver, appdomain)

add_service(mtk_advcamserver, mtk_advcamserver_service)
get_prop(mtk_advcamserver, hwservicemanager_prop)

allow mtk_advcamserver ion_device:chr_file r_file_perms;
