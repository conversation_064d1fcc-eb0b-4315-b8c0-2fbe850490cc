# ==============================================
# Common SEPolicy Rule
# ==============================================

type mtk_hal_gnss, domain;
type mtk_hal_gnss_exec, exec_type, vendor_file_type, file_type;

init_daemon_domain(mtk_hal_gnss)

hal_server_domain(mtk_hal_gnss, hal_gnss)

#TODO:: work around solution, wait for correct solution from google
vndbinder_use(mtk_hal_gnss)

# Communicate over a socket created by mnld process.
allow mtk_hal_gnss mnld_data_file:sock_file create_file_perms;
allow mtk_hal_gnss mnld_data_file:dir create_dir_perms;

allow mtk_hal_gnss mnld:unix_dgram_socket sendto;
#get waks_alarm timer create prop
allow mtk_hal_gnss mtk_hal_gnss:capability2 wake_alarm;

