# ==============================================
# Common SEPolicy Rule
# ==============================================

# Purpose: access for SYS_MEMORY_INFO
allow dumpstate fuse:dir w_dir_perms;
allow dumpstate fuse:file create_file_perms;

# Purpose: mnt/user/*
allow dumpstate mnt_user_file:dir search;
allow dumpstate mnt_user_file:lnk_file r_file_perms;

# Purpose: /storage/*
allow dumpstate storage_file:lnk_file r_file_perms;

# Purpose: timer_intval. this is neverallow
allow dumpstate kmsg_device:chr_file r_file_perms;

# Data : WK17.03
# Purpose: Allow to access gpu
allow dumpstate gpu_device:dir search;

# Date: 2017/07/11
# Purpose: 01-01 08:30:57.474   286   286 E SELinux : avc:  denied  { find } for interface=
# android.hardware.camera.provider::ICameraProvider pid=3133 scontext=u:r:dumpstate:s0 tcontext=
# u:object_r:hal_camera_hwservice:s0 tclass=hwservice_manager
hal_client_domain(dumpstate, hal_camera)

#Purpose: Allow dumpstate to read/write /sys/kernel/debug/tracing/buffer_total_size_kb
userdebug_or_eng(`allow dumpstate debugfs_tracing_debug:file rw_file_perms;')

# Purpose: Allow dumpstate to write /sys/devices/virtual/timed_output/vibrator/enable
allow dumpstate sysfs_vibrator:file w_file_perms;

# Purpose : Allow dumpstate self to sys_nice sys_admin
allow dumpstate self:capability { sys_nice sys_admin };
