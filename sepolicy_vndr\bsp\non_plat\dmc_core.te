# ==============================================
# Policy File of /vendor/bin/dmc_core Executable File

# ==============================================
# Common SEPolicy Rule
# ==============================================

type dmc_core_exec, exec_type, file_type, vendor_file_type;

init_daemon_domain(dmc_core)

# ==============================================
# Date : W1920
# Operation : Diagnostic framework Q migration
# ==============================================

# Purpose : allow dmc_core to start DMC and APM HIDL server
hal_server_domain(dmc_core, hal_mtk_dmc)
hal_server_domain(dmc_core, hal_mtk_apm)

# Purpose : for dmc_core to connenct to md_monitor
hal_client_domain(dmc_core, md_monitor_hal)

# Purpose : for dmc_core to access /data/md_mon/
allow dmc_core md_monitor_vendor_file:dir r_dir_perms;
allow dmc_core md_monitor_vendor_file:file r_file_perms;

# Purpose : Allow dmc_core to start/stop md_monitor
set_prop(dmc_core, ctl_start_prop)
set_prop(dmc_core, ctl_stop_prop)

# Purpose : Allow dmc_core to set DMC control property (vendor.dmc.apm.active)
set_prop(dmc_core, vendor_mtk_dmc_prop)

# Add policy read property for init.svc.md_monitor
get_prop(dmc_core, system_mtk_init_svc_md_monitor_prop)

# Add policy read property for init.svc.mtk_pkm_service
get_prop(dmc_core, system_mtk_pkm_init_prop)
