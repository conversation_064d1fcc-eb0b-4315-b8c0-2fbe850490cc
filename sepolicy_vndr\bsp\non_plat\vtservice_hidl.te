# ==============================================
# Policy File of /vendor/bin/hw/vtservice_hidl Executable File

# ==============================================
# Common SEPolicy Rule
# ==============================================
type vtservice_hidl_exec, exec_type, file_type, vendor_file_type;

init_daemon_domain(vtservice_hidl)

unix_socket_connect(vtservice_hidl, rild_oem, mtkrild)
allow vtservice_hidl mtkrild:unix_stream_socket connectto;

# Date: 2015/09/22
# Purpose: for unix domain socket access /dev/socket/volte_vt
allow vtservice_hidl MTK_SMI_device:chr_file { read write ioctl open };
allow vtservice_hidl fwmarkd_socket:sock_file write;
allow vtservice_hidl netd:unix_stream_socket connectto;
allow vtservice_hidl untrusted_app:binder call;

# For socket path between vt_service and volte_ua
allow vtservice_hidl self:udp_socket { create bind connect read write setopt getattr getopt shutdown };
allow vtservice_hidl node:udp_socket { node_bind };
allow vtservice_hidl volte_imsvt1_socket:sock_file write;

# 2017/07/
# HiDL porting
# Permission to use hwbinder functionality for communication:
#    1. add_hwservice(server_domain, service_name)
add_hwservice(vtservice_hidl, mtk_hal_videotelephony_hwservice)
#    2. also permission to access to /dev/hwbinder
hwbinder_use(vtservice_hidl)
#    3. For binder transaction. HwBinder IPC from clients into server, and callbacks
binder_call(vtservice, vtservice_hidl)
binder_call(vtservice_hidl, vtservice)

get_prop(vtservice_hidl, hwservicemanager_prop)

allow vtservice_hidl debugfs_tracing:file w_file_perms;
allow vtservice_hidl system_file:dir r_file_perms;
allow vtservice_hidl rild:unix_stream_socket connectto;

net_domain(vtservice_hidl)

# ViLTE
allow vtservice_hidl mtkimsmddomain:udp_socket { setopt getattr read write };
