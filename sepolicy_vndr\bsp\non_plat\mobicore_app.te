app_domain(mobicore_app)
net_domain(mobicore_app)

# ==============================================
# Rules between mobicore_app and mobicore
# ==============================================

# For RootPA (TA installation OTAv1) + RSU daemon
allow mobicore_app mobicore_user_device:chr_file { getattr read write ioctl open };
allow mobicore_app mobicore_admin_device:chr_file { getattr };

allow mobicore_app mobicore_data_file:dir { read getattr open search};
allow mobicore_app mobicore_data_file:file { read getattr open };
allow mobicore_app mobicore_tui_device:chr_file { ioctl open read};
allow mobicore_app mobicore:unix_stream_socket { connectto };

