# ==============================================
# Policy File of /system/bin/ccci_fsd Executable File

# ==============================================
# Type Declaration
# ==============================================
type ccci_fsd_exec, exec_type, file_type, vendor_file_type;
type ccci_fsd, domain;

# ==============================================
# Common SEPolicy Rule
# ==============================================
init_daemon_domain(ccci_fsd)

wakelock_use(ccci_fsd)

#============= ccci_fsd MD NVRAM==============
allow ccci_fsd nvram_data_file:dir create_dir_perms;
allow ccci_fsd nvram_data_file:file create_file_perms;
allow ccci_fsd nvram_data_file:lnk_file read;
allow ccci_fsd nvdata_file:lnk_file read;
allow ccci_fsd nvdata_file:dir create_dir_perms;
allow ccci_fsd nvdata_file:file create_file_perms;
allow ccci_fsd nvram_device:chr_file rw_file_perms;
allow ccci_fsd vendor_configs_file:file r_file_perms;
allow ccci_fsd vendor_configs_file:dir r_dir_perms;

#============= ccci_fsd device/path/data access==============
allow ccci_fsd ccci_device:chr_file rw_file_perms;
allow ccci_fsd ccci_cfg_file:dir create_dir_perms;
allow ccci_fsd ccci_cfg_file:file create_file_perms;
#============= ccci_fsd MD Data==============
allow ccci_fsd protect_f_data_file:dir create_dir_perms;
allow ccci_fsd protect_f_data_file:file create_file_perms;

allow ccci_fsd protect_s_data_file:dir create_dir_perms;
allow ccci_fsd protect_s_data_file:file create_file_perms;
#============= ccci_fsd MD3 related==============
allow ccci_fsd c2k_file:dir create_dir_perms;
allow ccci_fsd c2k_file:file create_file_perms;
allow ccci_fsd otp_part_block_device:blk_file rw_file_perms;
allow ccci_fsd otp_device:chr_file rw_file_perms;
allow ccci_fsd sysfs_boot_type:file { read open };
#============= ccci_fsd MD block data==============
#restore>NVM_GetDeviceInfo>open  /dev/block/by-name/nvram
allow ccci_fsd block_device:dir search;
allow ccci_fsd nvram_device:blk_file rw_file_perms;
allow ccci_fsd nvdata_device:blk_file rw_file_perms;
allow ccci_fsd nvcfg_file:dir create_dir_perms;
allow ccci_fsd nvcfg_file:file create_file_perms;
#============= ccci_fsd cryption related ==============
allow ccci_fsd rawfs:dir create_dir_perms;
allow ccci_fsd rawfs:file create_file_perms;
#============= ccci_fsd sysfs related ==============
allow ccci_fsd sysfs_ccci:dir search;
allow ccci_fsd sysfs_ccci:file r_file_perms;

#============= ccci_fsd ==============
allow ccci_fsd mnt_vendor_file:dir search;

# Purpose: for fstab parser
allow ccci_fsd kmsg_device:chr_file w_file_perms;
allow ccci_fsd proc_lk_env:file rw_file_perms;

#============= ccci_fsd MD Low Power Monitor Related ==============
allow ccci_fsd ccci_data_md1_file:dir create_dir_perms;
allow ccci_fsd ccci_data_md1_file:file create_file_perms;
allow ccci_fsd sysfs_devices_block:dir search;
allow ccci_fsd sysfs_devices_block:file { read getattr open };

#============= ccci_fsd access vendor/etc/md file ==============
allow ccci_fsd vendor_etc_md_file:dir search;
allow ccci_fsd vendor_etc_md_file:file r_file_perms;

#============= ccci_fsd access data/vendor_de/md file ==============
allow ccci_fsd data_vendor_de_md_file:dir create_dir_perms;
allow ccci_fsd data_vendor_de_md_file:file create_file_perms;
