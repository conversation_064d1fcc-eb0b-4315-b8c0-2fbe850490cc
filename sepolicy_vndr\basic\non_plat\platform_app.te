# ==============================================
# Common SEPolicy Rule
# ==============================================

# Date : WK18.21
# Operation : Migration
# Purpose : Do FM operation via hal_mtk_fm
hal_client_domain(platform_app, hal_mtk_fm)

# Date: 2019/07/04
# Stage: Migration
# Purpose: Allow to use lomo effect
# Package: com.mediatek.camera
allow platform_app sw_sync_device:chr_file rw_file_perms;

# Date: 2019/07/04
# Purpose: Allow platform app to use BGService HIDL and access mtk_hal_camera
hal_client_domain(platform_app, hal_mtk_bgs)
binder_call(platform_app, mtk_hal_camera)

# Date: 2020/06/08
# Purpose: Allow platform app to access mtk jpeg
allow platform_app proc_mtk_jpeg:file rw_file_perms;
allowxperm platform_app proc_mtk_jpeg:file ioctl {
      JPG_BRIDGE_DEC_IO_LOCK
      JPG_BRIDGE_DEC_IO_WAIT
      JPG_BRIDGE_DEC_IO_UNLOCK
};

# Date : 2021/04/22
# Operation : Migration
# Purpose : DebugLoggerUI need copy & delete /data/vendor/vcodec/ folder
# Package: com.debug.loggerui
allow platform_app vcodec_file:dir create_dir_perms;
allow platform_app vcodec_file:file create_file_perms;
# DebugLoggerUI can get c2 log properties
get_prop(platform_app, vendor_mtk_c2_log_prop)

# Allow FMRadio to talk to /dev/fm
allow platform_app fm_device:chr_file rw_file_perms;
