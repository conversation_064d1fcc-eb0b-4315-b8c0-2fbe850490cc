# ==============================================
# Policy File of /vendor/bin/md_monitor Executable File


# ==============================================
# Type Declaration
# ==============================================

type md_monitor_exec ,exec_type, vendor_file_type, file_type;
init_daemon_domain(md_monitor)

typeattribute md_monitor mlstrustedsubject;

# ==============================================
# Common SEPolicy Rule
# ==============================================

# Date : 2019/05/16
# Operation : IT
# Purpose : access /data/vendor/md_mon/, write filter bin and layout file to this dir
allow md_monitor md_monitor_vendor_file:dir { create_dir_perms relabelto relabelfrom};
allow md_monitor md_monitor_vendor_file:file create_file_perms;

# Date : 2017/10/20
# Operation : IT
# Purpose : Allow md_monitor to dump raw data to file in /sdcard/MdmDump/
allow md_monitor mnt_user_file:dir search;
allow md_monitor mnt_user_file:lnk_file read;
allow md_monitor sdcardfs:dir create_dir_perms;
allow md_monitor sdcardfs:file create_file_perms;
allow md_monitor storage_file:lnk_file read;

# Date : 2019/05/16
# Operation : IT
# Purpose : Add for HIDL service
add_hwservice(md_monitor, mtk_mdm_hidl_server)
get_prop(md_monitor, hwservicemanager_prop)
hwbinder_use(md_monitor)
hal_server_domain(md_monitor, md_monitor_hal)

# Date : 2015/10/12
# Operation : IT
# Purpose : Allow md_monitor to set
allow md_monitor ccci_mdmonitor_device:chr_file rw_file_perms;
allow md_monitor ccci_ccb_device:chr_file rw_file_perms;
allow md_monitor sysfs_ccci:dir search;
allow md_monitor sysfs_ccci:file r_file_perms;
allow md_monitor file_contexts_file:file r_file_perms;

# Date : 2017/10/16
# Operation : IT
# Purpose : Allow md_monitor to use restore_image_from_pt()
allow md_monitor block_device:dir search;
allow md_monitor md_block_device:blk_file r_file_perms;
allow md_monitor self:capability chown;
allow md_monitor storage_file:dir search;
allow md_monitor tmpfs:lnk_file read;
