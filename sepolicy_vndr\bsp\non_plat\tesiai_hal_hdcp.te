# ==============================================
# Policy File of HDCP HAL service

# ==============================================
# Type Declaration
# ==============================================
type tesiai_hal_hdcp_exec , exec_type, file_type, vendor_file_type;

# ==============================================
# Common SEPolicy Rule
# ==============================================

# Setup for domain transition
init_daemon_domain(tesiai_hal_hdcp)


# Allow a set of permissions required for a domain to be a server which provides a HAL implementation over HWBinder.
hal_server_domain(tesiai_hal_hdcp, hal_tesiai_hdcp)

# Allow HDCP to access Trustzone
allow tesiai_hal_hdcp mobicore_user_device:chr_file rw_file_perms;
allow tesiai_hal_hdcp mobicore_vendor_file:file lock;


# Allow HDCP to access HDCP key folder
allow tesiai_hal_hdcp mnt_vendor_file:dir { search getattr };
allow tesiai_hal_hdcp persist_data_file:dir search;
allow tesiai_hal_hdcp persist_data_file:file rw_file_perms;

# Allow HDCP to access codec FD
allow tesiai_hal_hdcp mtk_hal_c2:fd use;
