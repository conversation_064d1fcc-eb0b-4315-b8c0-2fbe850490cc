/(system\/vendor|vendor)/bin/epdg_wod u:object_r:epdg_wod_exec:s0
/(system\/vendor|vendor)/bin/wfca u:object_r:wfca_exec:s0
/(system\/vendor|vendor)/bin/ipsec u:object_r:ipsec_exec:s0
/(system\/vendor|vendor)/bin/charon u:object_r:charon_exec:s0
/(system\/vendor|vendor)/bin/starter u:object_r:starter_exec:s0
/(system\/vendor|vendor)/bin/stroke u:object_r:stroke_exec:s0
/(system\/vendor|vendor)/bin/bip u:object_r:bip_exec:s0
/data/vendor/ipsec(/.*)? u:object_r:wod_ipsec_conf_file:s0
/data/vendor/ipsec/wo(/.*)? u:object_r:wod_apn_conf_file:s0
/dev/socket/wod_action(/.*)? u:object_r:wod_action_socket:s0
/dev/socket/wod_sim(/.*)? u:object_r:wod_sim_socket:s0
/dev/socket/wod_ipsec(/.*)? u:object_r:wod_ipsec_socket:s0
/dev/socket/wod_dns(/.*)? u:object_r:wod_dns_socket:s0

/dev/socket/volte_imsm(/.*)? u:object_r:rild_imsm_socket:s0
/dev/socket/volte_imsa[0-9](/.*)? u:object_r:volte_imsa_socket:s0
/dev/socket/volte_imsvt[0-9](/.*)? u:object_r:volte_imsvt_socket:s0
/dev/socket/volte_imcb(/.*)? u:object_r:volte_imcb_socket:s0
/dev/socket/volte_ut(/.*)? u:object_r:volte_ut_socket:s0
/dev/socket/volte_ua(/.*)? u:object_r:volte_ua_socket:s0
/dev/socket/volte_stack(/.*)? u:object_r:volte_stack_socket:s0
/dev/socket/wfca(/.*)? u:object_r:wfca_socket:s0
/dev/socket/wfca_rds(/.*)? u:object_r:wfca_socket:s0
/dev/socket/bip(/.*)? u:object_r:bip_socket:s0
/dev/socket/vendor\.bip(/.*)? u:object_r:vendor_bip_socket:s0

/(system\/vendor|vendor)/bin/volte_imcb u:object_r:volte_imcb_exec:s0
/(system\/vendor|vendor)/bin/volte_stack u:object_r:volte_stack_exec:s0
/(system\/vendor|vendor)/bin/volte_ua u:object_r:volte_ua_exec:s0
/(system\/vendor|vendor)/bin/volte_imsm_93 u:object_r:volte_imsm_93_exec:s0

/(system\/vendor|vendor)/bin/volte_md_status u:object_r:volte_md_status_exec:s0
