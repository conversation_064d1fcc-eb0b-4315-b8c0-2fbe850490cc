# ==============================================
# Policy File of /vendor/bin/em_hidi Executable File

# ==============================================
# Common SEPolicy Rule
# ==============================================

# Date :  2018/07/10
# Operation : EM DEBUG
# Purpose: EM set Moms property
set_prop(em_hidl, vendor_mtk_moms_prop)

# Date : 2019/2/11
# Operation: EM for ViLTE
# Purpose: Allow EM HIDL to get/set vendor_mtk_vendor_vt_prop
set_prop(em_hidl, vendor_mtk_vendor_vt_prop)

# Date : 2019/08/20
# Operation : DMC Q Migration
# Purpose : allow EM to set vendor_mtk_dmc_prop
set_prop(em_hidl, vendor_mtk_dmc_prop)

# Date : 2020/04/24
# Purpos : allow EM set dynamic debug control property
set_prop(em_hidl, vendor_mtk_em_dy_debug_ctrl_prop)

# Date : 2020/05/04
# Purpose : allow EM to access vendor_nfc_socket_file
allow em_hidl vendor_nfc_socket_file:dir w_dir_perms;
allow em_hidl nfcstackp_vendor:unix_stream_socket connectto;

# Date : 2020/05/05
# Purpose : Add availablities to set property
set_prop(em_hidl, vendor_mtk_nfc_nfcstackp_enable_prop)

# Date : 2020/05/07
# Purpose : allow EM to access MNL config file
allow em_hidl gps_data_file:file rw_file_perms;
allow em_hidl gps_data_file:dir search;

# Date : 2020/05/09
# Purpose: allow IMS SS to set radio prop
set_prop(em_hidl,vendor_mtk_radio_prop)

# Date : 2020/11/18
# Purpos : allow EM set mtu property
set_prop(em_hidl, vendor_mtk_em_mtu_prop)

# Date: 2021/01/27
# Purpose: Allow EM to set persist.vendor.connsys.xx
set_prop(em_hidl, vendor_mtk_wmt_prop)

# Date: 2021/04/28
# Purpose: Allow EM to access NVRAM for BT
allow em_hidl metadata_file:dir search;
allow em_hidl gsi_metadata_file:dir search;

