# ==============================================
# Policy File of /vendor/bin/wlan_assistant Executable File

# ==============================================
# Type Declaration
# ==============================================
type wlan_assistant_exec, exec_type, file_type, vendor_file_type;
type wlan_assistant, domain;

# ==============================================
# Common SEPolicy Rule
# ==============================================
init_daemon_domain(wlan_assistant)

# Date : WK14.34
# Operation : Migration
# Purpose : for mtk debug mechanism. agpsd_data_file, mtk_agpsd are used
# to share wifi scan results with AGPS module. netlink_socket is used to
# listen events of wlan driver. udp_socket is used to do ioctl with wlan driver
# kernel-3.18 uses netlink_socket, but kernel-4.4 uses generic netlink_socket
allow wlan_assistant agpsd_data_file:sock_file w_file_perms;
allow wlan_assistant mtk_agpsd:unix_dgram_socket sendto;
allow wlan_assistant agpsd_data_file:dir search;
allow wlan_assistant self:netlink_generic_socket create_socket_perms_no_ioctl;
allow wlan_assistant self:udp_socket create_socket_perms;

# Date : WK18.17
# Operation : Migration
# Purpose : To allow wlan_assistant monitor /vendor/nvdata/APCFG/APRDEB,
# /storage/sdcard0, /vendor/firmware. Which can help to check if nvram,
# driver config or firmware config file are changed, if yes, will write it
# to wlan driver in time.
allow wlan_assistant nvdata_file:dir r_dir_perms;
allow wlan_assistant nvdata_file:file r_file_perms;
allow wlan_assistant wmtWifi_device:chr_file rw_file_perms;

allow wlan_assistant mnt_vendor_file:dir search;

set_prop(wlan_assistant, vendor_mtk_nvram_ready_prop)
