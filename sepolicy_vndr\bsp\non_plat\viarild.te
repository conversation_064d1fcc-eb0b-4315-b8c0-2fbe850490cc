# ==============================================
# Policy File of /vendor/bin/viarild Executable File

# ==============================================
# Common SEPolicy Rule
# =============================================
type viarild_exec, exec_type, file_type, vendor_file_type;
typeattribute viarild mtkimsapdomain;

init_daemon_domain(viarild)
net_domain(viarild)
allow viarild self:netlink_route_socket nlmsg_write;
allow viarild kernel:system module_request;
allow viarild self:capability { setuid net_admin net_raw };
allow viarild cgroup:dir create_dir_perms;
allow viarild radio_device:chr_file rw_file_perms;
allow viarild radio_device:blk_file r_file_perms;
allow viarild mtd_device:dir search;
allow viarild efs_file:dir create_dir_perms;
allow viarild efs_file:file create_file_perms;

allow viarild bluetooth_efs_file:file r_file_perms;
allow viarild bluetooth_efs_file:dir r_dir_perms;
allow viarild sdcardfs:dir r_dir_perms;

set_prop(viarild, vendor_mtk_cdma_prop)
set_prop(viarild, vendor_mtk_ril_cdma_report_prop)
set_prop(viarild, vendor_mtk_ril_mux_report_case_prop)
set_prop(viarild, vendor_mtk_radio_prop)
set_prop(viarild, vendor_mtk_ril_ipo_prop)

# Dat: 2017/02/14
# Purpose: allow set telephony Sensitive property
set_prop(viarild, vendor_mtk_telephony_sensitive_prop)

allow viarild tty_device:chr_file rw_file_perms;

# Allow viarild to create and use netlink sockets.
allow viarild self:netlink_socket create_socket_perms_no_ioctl;
allow viarild self:netlink_kobject_uevent_socket create_socket_perms_no_ioctl;

# Access to wake locks
wakelock_use(viarild)

allow viarild self:socket create_socket_perms_no_ioctl;

allow viarild Vcodec_device:chr_file { read write open };
allow viarild devmap_device:chr_file { read ioctl open };
allow viarild devpts:chr_file { read write open };

allow viarild ccci_device:chr_file { read write ioctl open };
allow viarild devpts:chr_file ioctl;
allow viarild misc_device:chr_file { read write open };
allow viarild proc_lk_env:file { read ioctl open };
allow viarild sysfs_vcorefs_pwrctrl:file { open write };
set_prop(viarild, vendor_mtk_ril_active_md_prop)

# set for mux
allow viarild devpts:chr_file setattr;
allow viarild self:capability chown;
allow viarild self:capability fowner;
allow viarild self:capability setuid;

# For MAL MFI
allow viarild mal_mfi_socket:sock_file write;

# For Vzw Phone CCP - Set IPV6 RS
allow viarild proc_net:file write;

# If viarild(which belongs to vendor partition) want to open binder dev node(e.g. Parcel) will be
# denied for no permission. Should use vndbinder dev node in vendor domain.
# Using the following sepolicy rule to allow viarild to use vendor binder.
vndbinder_use(viarild)

# Allow to trigger IPv6 RS
allow viarild node:rawip_socket node_bind;

# Allow to config network
allowxperm viarild self:udp_socket ioctl {SIOCDELRT SIOCSIFFLAGS SIOCSIFADDR SIOCKILLADDR SIOCDEVPRIVATE SIOCDEVPRIVATE_1};
allow viarild sysfs_ccci:dir search;
allow viarild sysfs_ccci:file r_file_perms;
