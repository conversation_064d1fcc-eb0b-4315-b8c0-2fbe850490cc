# ==============================================
# Policy File of /vendor/bin/muxreport Executable File

# ==============================================
# Type Declaration
# ==============================================
type muxreport_exec, exec_type, file_type, vendor_file_type;
type muxreport, domain;

# ==============================================
# Common SEPolicy Rule
# ==============================================
init_daemon_domain(muxreport)

# Property service
# allow set muxreport control properties
set_prop(muxreport, vendor_mtk_ril_mux_report_case_prop)

# Allow read/write to devices/files
allow muxreport ccci_device:chr_file rw_file_perms;
allow muxreport devpts:chr_file rw_file_perms;
allow muxreport eemcs_device:chr_file rw_file_perms;
allow muxreport emd_device:chr_file rw_file_perms;

# Allow read to sys/kernel/ccci/* files
allow muxreport sysfs_ccci:dir search;
allow muxreport sysfs_ccci:file r_file_perms;

# Date : WK18.16
# Operation: P migration
# Purpose: Allow muxreport to get vendor_mtk_tel_switch_prop
get_prop(muxreport, vendor_mtk_tel_switch_prop)

#Date: W1824
#Purpose: allow muxreport access property of vendor_mtk_radio_prop
set_prop(muxreport, vendor_mtk_radio_prop)
