# ==============================================
# Common SEPolicy Rule
# ==============================================

# boot_mdoe file access
allow mobile_log_d sysfs_boot_mode:file r_file_perms;

#proc/ access
allow mobile_log_d proc_kmsg:file r_file_perms;
allow mobile_log_d proc_cmdline:file r_file_perms;
allow mobile_log_d proc_atf_log:dir search;
allow mobile_log_d proc_atf_log:file r_file_perms;
allow mobile_log_d proc_gz_log:file r_file_perms;
allow mobile_log_d proc_last_kmsg:file r_file_perms;
allow mobile_log_d proc_bootprof:file r_file_perms;
allow mobile_log_d proc_pl_lk:file r_file_perms;

#apusys
allow mobile_log_d proc_apusys_up_seq_logl:file r_file_perms;

#scp
allow mobile_log_d sysfs_scp:file w_file_perms;
allow mobile_log_d sysfs_scp:dir search;
allow mobile_log_d scp_device:chr_file r_file_perms;

#vcp
allow mobile_log_d sysfs_vcp:file w_file_perms;
allow mobile_log_d sysfs_vcp:dir search;
allow mobile_log_d vcp_device:chr_file r_file_perms_no_map;

#adsp
allow mobile_log_d sysfs_adsp:file w_file_perms;
allow mobile_log_d sysfs_adsp:dir search;
allow mobile_log_d adsp_device:chr_file r_file_perms;

#sspm
allow mobile_log_d sysfs_sspm:file w_file_perms;
allow mobile_log_d sysfs_sspm:dir search;
allow mobile_log_d sspm_device:chr_file r_file_perms;

#data/misc/mblog
allow mobile_log_d logmisc_data_file:dir { relabelto create_dir_perms };
allow mobile_log_d logmisc_data_file:file create_file_perms;

#data/log_temp
allow mobile_log_d logtemp_data_file:dir { relabelto create_dir_perms };
allow mobile_log_d logtemp_data_file:file create_file_perms;

#data/data_tmpfs_log
allow mobile_log_d data_tmpfs_log_file:dir create_dir_perms;
allow mobile_log_d data_tmpfs_log_file:file create_file_perms;

# purpose: send log to com port
allow mobile_log_d ttyGS_device:chr_file rw_file_perms;

# purpose: allow mobile_log_d to access persist.meta.connecttype
get_prop(mobile_log_d, vendor_mtk_meta_connecttype_prop)

# purpose: allow mobile_log_d to create socket
allow mobile_log_d port:tcp_socket { name_connect name_bind };
allow mobile_log_d mobile_log_d:tcp_socket create_stream_socket_perms;
allow mobile_log_d node:tcp_socket node_bind;

# purpose: allow mobile_log_d to write dev/wmtWifi.
allow mobile_log_d wmtWifi_device:chr_file rw_file_perms;

# Date: 2016/11/11
# purpose: allow MobileLog to access aee socket
allow mobile_log_d crash_dump:unix_stream_socket connectto;

# Date : WK21.31
# Purpose: Add permission to access new bootmode file
allow mobile_log_d sysfs_boot_info:file r_file_perms;
