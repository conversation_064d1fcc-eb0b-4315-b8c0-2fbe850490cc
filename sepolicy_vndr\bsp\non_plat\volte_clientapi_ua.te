# ==============================================
# Policy File of /vendor/bin/volte_clientapi_ua Executable File

# ==============================================
# Common SEPolicy Rule
# ==============================================
type volte_clientapi_ua_exec, exec_type, file_type, vendor_file_type;

init_daemon_domain(volte_clientapi_ua)

# hwbinder access
hal_server_domain(volte_clientapi_ua, hal_clientapi)

# call into system_app process (callbacks)
binder_call(volte_clientapi_ua, system_app)
binder_call(volte_clientapi_ua, platform_app)

# Date : W18.43
# Operation : IT
# Purpose: clientapi HIDL Migration
get_prop(volte_clientapi_ua, hwservicemanager_prop)
allow volte_clientapi_ua debugfs_tracing:file w_file_perms;
