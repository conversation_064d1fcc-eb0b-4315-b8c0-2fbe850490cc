# ==============================================
# Common SEPolicy Rule
# ==============================================

type mobile_log_d_exec, system_file_type, exec_type, file_type;
typeattribute mobile_log_d coredomain;
typeattribute mobile_log_d mlstrustedsubject;

init_daemon_domain(mobile_log_d)

#syslog module
allow mobile_log_d kernel:system syslog_mod;

#GMO project
dontaudit mobile_log_d untrusted_app:fd use;
dontaudit mobile_log_d isolated_app_all:fd use;

#debug property set
set_prop(mobile_log_d, debug_prop)

#socket connect and write
unix_socket_connect(mobile_log_d, logdr, logd);

#capability
allow mobile_log_d self:capability { setuid setgid chown fowner fsetid };
allow mobile_log_d self:capability2 syslog;

#aee mode switch
allow mobile_log_d system_file:file x_file_perms;

#shell command
allow mobile_log_d shell_exec:file rx_file_perms;

# execute logcat command
allow mobile_log_d logcat_exec:file rx_file_perms;

# execute 'logcat -L' via dumpstate
domain_auto_trans(mobile_log_d, logcat_exec, dumpstate)

#general storage access
allow mobile_log_d storage_file:dir create_dir_perms;
allow mobile_log_d storage_file:file create_file_perms;
allow mobile_log_d storage_file:lnk_file create_file_perms;
allow mobile_log_d mnt_user_file:dir create_dir_perms;
allow mobile_log_d mnt_user_file:lnk_file create_file_perms;
allow mobile_log_d sdcard_type:dir create_dir_perms;
allow mobile_log_d sdcard_type:file create_file_perms;

#factory mode vfat access
allow mobile_log_d vfat:dir create_dir_perms;
allow mobile_log_d vfat:file create_file_perms;

#chiptest mode storage access
allow mobile_log_d mnt_media_rw_file:dir create_dir_perms;
allow mobile_log_d mnt_media_rw_file:lnk_file create_file_perms;

#system/bin/toybox for using 'sh' command
allow mobile_log_d toolbox_exec:file rx_file_perms;

#selinux_version access
allow mobile_log_d rootfs:file r_file_perms;

#dev/__properties__ access
get_prop(mobile_log_d, device_logging_prop)
get_prop(mobile_log_d, mmc_prop)
get_prop(mobile_log_d, safemode_prop)

# purpose: allow MobileLog to access storage in N version
allow mobile_log_d media_rw_data_file:file create_file_perms;
allow mobile_log_d media_rw_data_file:dir create_dir_perms;

# access debugfs/tracing/instances/
allow mobile_log_d debugfs_tracing:dir create_dir_perms;
allow mobile_log_d debugfs_tracing_instances:dir create_dir_perms;
allow mobile_log_d debugfs_tracing_instances:file create_file_perms;

#data/debuglog
allow mobile_log_d debuglog_data_file:dir {relabelto create_dir_perms};
allow mobile_log_d debuglog_data_file:file create_file_perms;

#mcupm
allow mobile_log_d mcupm_device:chr_file r_file_perms;
allow mobile_log_d sysfs_mcupm:file w_file_perms;
allow mobile_log_d sysfs_mcupm:dir search;

#for logpost feature
userdebug_or_eng(`
  allow mobile_log_d domain:dir r_dir_perms;
  allow mobile_log_d domain:{file lnk_file} r_file_perms;
  allow mobile_log_d dnsproxyd_socket:sock_file w_file_perms;
  allow mobile_log_d self:udp_socket create_socket_perms_no_ioctl;
  allow mobile_log_d netd:unix_stream_socket connectto;
  allow mobile_log_d self:tcp_socket getopt;
  allow mobile_log_d fwmarkd_socket:sock_file w_file_perms;
  set_prop(mobile_log_d, system_mtk_mobile_log_post_prop)
')

#mobile itself property
set_prop(mobile_log_d, system_mtk_mobile_log_prop)

#wifi driver log property
get_prop(mobile_log_d, system_mtk_wifisa_log_prop)

# purpose: allow mobile_log_d to read persist.vendor.mtk.aee
get_prop(mobile_log_d, system_mtk_persist_mtk_aee_prop)
