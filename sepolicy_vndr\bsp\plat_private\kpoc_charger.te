# ==============================================
# Policy File of /system/bin/kpoc_charger Executable File

# ==============================================
# Common SEPolicy Rule
# ==============================================
type kpoc_charger_exec, system_file_type, exec_type, file_type;
typeattribute kpoc_charger coredomain;

# Date : WK15.32
# Operation : Migration
# Purpose : Start kpoc_charger
init_daemon_domain(kpoc_charger)

# Use light HAL
hal_client_domain(kpoc_charger, hal_light)

# Use health HAL
hal_client_domain(kpoc_charger, hal_health)

# Date : WK15.32
# Operation : Migration
# Purpose : Interact with kernel to perform kpoc_charger
allow kpoc_charger block_device:dir search;
allow kpoc_charger graphics_device:dir search;
allow kpoc_charger graphics_device:chr_file rw_file_perms;
allow kpoc_charger input_device:dir r_dir_perms;
allow kpoc_charger input_device:chr_file rw_file_perms;
allow kpoc_charger self:capability { sys_nice net_admin sys_boot sys_admin };
allow kpoc_charger self:netlink_kobject_uevent_socket { create bind read setopt };
allow kpoc_charger sysfs:dir r_dir_perms;
allow kpoc_charger kmsg_device:chr_file { getattr w_file_perms };
allow kpoc_charger rtc_device:chr_file rw_file_perms;

# Date : WK15.44
# Operation : Migration
# Purpose : add sepolicy for nand platform which use mtd_device
allow kpoc_charger mtd_device:dir search;
allow kpoc_charger mtd_device:chr_file r_file_perms;

# Date : WK17.25
# Operation : Android O migration
# Purpose : add sepolicy for accessing rootfs and sysfs_leds
allow kpoc_charger rootfs:file r_file_perms;
allow kpoc_charger sysfs_leds:dir r_dir_perms;

# Date : WK18.20
# Operation : Android P migration
# Purpose: add sepolicy for sysfs_batteryinfo
allow kpoc_charger sysfs_batteryinfo:dir r_dir_perms;

# Purpose: add sepolicy for sysfs_power
allow kpoc_charger sysfs_power:file rw_file_perms;

# Purpose: add sepolicy for sysfs_dt_firmware_android
r_dir_file(kpoc_charger, sysfs_dt_firmware_android)
allow kpoc_charger proc_cmdline:file r_file_perms;

# Purpose: add sepolicy for BatteryNotify
allow kpoc_charger sysfs_battery_warning:file r_file_perms;

# Operation: Android R Migration
# Purpose : access vbus
allow kpoc_charger sysfs_vbus:file r_file_perms;

# Date : WK20.37
# Operation : common-kernel-5.4
# Purpose: add sepolicy for acessing metadata_file
allow kpoc_charger metadata_file:dir search;

#Date: WK20.52
# Purpose : DRM / DRI GPU driver required
allow kpoc_charger gpu_device:dir search;

