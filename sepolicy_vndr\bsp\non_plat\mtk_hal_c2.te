#============= mtk_hal_c2 for SVP on legacy vcodec ==============
allow mtk_hal_c2 mobicore_user_device:chr_file rw_file_perms;
allow mtk_hal_c2 proc_m4u:file r_file_perms;
allowxperm mtk_hal_c2 proc_m4u:file ioctl {
 MTK_M4U_T_SEC_INIT
 MTK_M4U_T_CONFIG_PORT
 MTK_M4U_T_CACHE_SYNC
 MTK_M4U_T_CONFIG_PORT_ARRAY
};

hal_client_domain(mtk_hal_c2, hal_teei_capi)
allow mtk_hal_c2 teei_client_device:chr_file rw_file_perms;
