# ==============================================
# Policy File of /vendor/bin/thermalloadalgod_exec Executable File

# ==============================================
# Type Declaration
# ==============================================
type thermalloadalgod ,domain;
type thermalloadalgod_exec, exec_type, file_type, vendor_file_type;
typeattribute thermalloadalgod mlstrustedsubject;

# ==============================================
# Common SEPolicy Rule
# ==============================================
init_daemon_domain(thermalloadalgod)

# Data : WK14.43
# Operation : Migration
# Purpose : thermal algorithm daemon for access driver node
allow thermalloadalgod input_device:dir rw_dir_perms;
allow thermalloadalgod input_device:file r_file_perms;

allow thermalloadalgod thermalloadalgod:netlink_socket create_socket_perms_no_ioctl;

allow thermalloadalgod thermal_manager_data_file:dir create_dir_perms;
allow thermalloadalgod thermal_manager_data_file:file create_file_perms;
allow thermalloadalgod kmsg_device:chr_file w_file_perms;

# Data : WK16.49
# Operation : SPA porting
# Purpose : thermal algorithm daemon for SPA
# For /proc/[pid]/cgroup accessing
allow thermalloadalgod proc:dir { search getattr };
allow thermalloadalgod shell:dir search;
allow thermalloadalgod platform_app:dir search;
allow thermalloadalgod platform_app:file r_file_perms;
allow thermalloadalgod priv_app:dir search;
allow thermalloadalgod priv_app:file r_file_perms;
allow thermalloadalgod system_app:dir search;
allow thermalloadalgod system_app:file r_file_perms;
allow thermalloadalgod untrusted_app:dir search;
allow thermalloadalgod untrusted_app:file r_file_perms;
allow thermalloadalgod mediaserver:dir search;
allow thermalloadalgod mediaserver:file r_file_perms;
allow thermalloadalgod proc_thermal:dir search;
allow thermalloadalgod proc_thermal:file rw_file_perms;
