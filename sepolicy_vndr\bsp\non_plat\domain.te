# ==============================================
# Common SEPolicy Rule
# ==============================================

# Date : WK15.29
# Operation : Migration
# Purpose : for device bring up, not to block early migration
allow { domain -isolated_app_all } storage_file:dir search;

# Date : W17.47
# Allow system_server to enable/disable logmuch_prop for Wi-Fi logging purpose
neverallow {
  domain
  -init
  -mtkrild
  -mtk_hal_camera
  -vendor_init
  } vendor_mtk_logmuch_prop:property_service set;

# Date : WK18.34
# Operation : Migration
# Purpose : for CTS android.os.cts.SecurityPatchTest
get_prop(domain, vendor_security_patch_level_prop)
