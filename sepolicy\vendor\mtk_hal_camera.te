# MTK Camera HAL domain
type mtk_hal_camera, domain;
hal_server_domain(mtk_hal_camera, hal_camera)

# Allow mtk_hal_camera to find Oplus camera services
allow mtk_hal_camera hal_camera_oplus_hwservice:hwservice_manager find;

# Allow basic camera operations
allow mtk_hal_camera camera_device:chr_file rw_file_perms;
allow mtk_hal_camera vendor_camera_update_data_file:dir create_dir_perms;
allow mtk_hal_camera vendor_camera_update_data_file:file create_file_perms;
allow mtk_hal_camera persist_camera_file:dir create_dir_perms;
allow mtk_hal_camera persist_camera_file:file create_file_perms;

# Allow access to vendor properties
get_prop(mtk_hal_camera, vendor_oplus_prop)
get_prop(mtk_hal_camera, system_oplus_project_prop)

# Allow binder communication
binder_use(mtk_hal_camera)
binder_call(mtk_hal_camera, servicemanager)
