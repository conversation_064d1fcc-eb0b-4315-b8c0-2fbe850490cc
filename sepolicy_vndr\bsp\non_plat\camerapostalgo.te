# ==============================================
# Policy File of /system/bin/camerapostalgo Executable File

# ==============================================
# Common SEPolicy Rule
# ==============================================

allow camerapostalgo proc_perfmgr:dir r_dir_perms;
allow camerapostalgo proc_perfmgr:file r_file_perms;
allowxperm camerapostalgo proc_perfmgr:file ioctl {
  PERFMGR_FPSGO_QUEUE
  PERFMGR_FPSGO_DEQUEUE
  PERFMGR_FPSGO_QUEUE_CONNECT
  PERFMGR_FPSGO_BQID
};

allow camerapostalgo proc_ged:file r_file_perms;
allowxperm camerapostalgo proc_ged:file ioctl { proc_ged_ioctls };
allow camerapostalgo debugfs_ion:dir search;

# ipc call
hal_client_domain(camerapostalgo, hal_mtk_mms)
hal_client_domain(camerapostalgo, hal_graphics_allocator)
allow camerapostalgo hal_graphics_mapper_hwservice:hwservice_manager find;
allow camerapostalgo hal_configstore_default:binder call;
allow camerapostalgo mtk_hal_gpu_hwservice:hwservice_manager find;

