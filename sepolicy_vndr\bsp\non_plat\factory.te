# ==============================================
# Policy File of /vendor/bin/factory Executable File

# ==============================================
# Common SEPolicy Rule
# ==============================================

# Date: WK17.46
allow factory kmsg_device:chr_file w_file_perms;
allow factory dm_device:blk_file rw_file_perms;
allow factory sysfs_fs_ext4_features:dir search;
allow factory sysfs_fs_ext4_features:file r_file_perms;
allow factory system_block_device:blk_file getattr;
allow factory vendor_block_device:blk_file getattr;
allow factory cache_block_device:blk_file getattr;
allow factory protect1_block_device:blk_file getattr;
allow factory protect2_block_device:blk_file getattr;

# Purpose : Allow factory to call android.hardware.audio@2.0-service-mediatek
binder_call(factory, hal_audio_default)
allow factory hal_audio_default:binder call;
allow factory mtk_audiohal_data_file:dir r_dir_perms;
allow factory audio_device:chr_file rw_file_perms;
allow factory audio_device:dir w_dir_perms;

# Purpose : adsp
allow factory adsp_device:chr_file rw_file_perms;

# Purpose : Allow factory to get usb_state
allow factory sysfs_android_usb:dir search;
allow factory sysfs_android_usb:file r_file_perms;

# Date : 2020/05/12
allow factory nfcstackp_vendor:unix_stream_socket connectto;

# Date : 2020/05/12
# Purpose : Add availablities to set property
set_prop(factory, vendor_mtk_nfc_nfcstackp_enable_prop)

allow factory fingerprint_device:chr_file rw_file_perms;
allow factory tmpfs:chr_file rw_file_perms;
allow factory self:netlink_socket create_socket_perms_no_ioctl;

# Data : 2021/4/21
# Purpose : add permission for /proc/mtk_usb
allow factory proc_usb_plat:dir search;
allow factory proc_usb_plat:file rw_file_perms;