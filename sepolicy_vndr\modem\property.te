#=============allow mtkmal to start volte==============

vendor_internal_prop(vendor_mtk_ctl_volte_imcb_prop)
vendor_internal_prop(vendor_mtk_ctl_volte_stack_prop)
vendor_internal_prop(vendor_mtk_ctl_volte_ua_prop)
vendor_restricted_prop(vendor_mtk_md_volte_prop)
typeattribute vendor_mtk_md_volte_prop mtk_core_property_type;

#=============allow wifi offload deamon  ==============
vendor_restricted_prop(vendor_mtk_wod_prop)
vendor_restricted_prop(vendor_mtk_persist_wod_prop)

typeattribute vendor_mtk_wod_prop mtk_core_property_type;
typeattribute vendor_mtk_persist_wod_prop mtk_core_property_type;

#=============allow volte md status deamon  ==============
vendor_internal_prop(vendor_mtk_md_status_prop)
