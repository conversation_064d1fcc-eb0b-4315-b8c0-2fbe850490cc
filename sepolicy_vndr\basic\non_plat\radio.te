# ==============================================
# Common SEPolicy Rule
# ==============================================

# Purpose : allow to access kpd driver file
allow radio sysfs_keypad_file:dir r_dir_perms;
allow radio sysfs_keypad_file:file w_file_perms;

# Date : 2014/12/13
# Operation : IT
# Purpose : for bluetooth relayer mode
allow radio block_device:dir search;
allow radio ttyGS_device:chr_file rw_file_perms;

# Date : 2016/07/05
# Purpose :
#   Write IMEI - presanity item write imei should read the file on storage
#   Swift APK integration - access TTL scripts and logs on external storage
#   eng mode camera - save iamges files and log files on external storage
#   eng mode ygps - save location information on external storage
allow radio media_rw_data_file:dir create_dir_perms;
allow radio media_rw_data_file:file create_file_perms;

# Date : 2016/08/02
# Purpose :
#   Swift APK integration - access ccci dir/file
allow radio ccci_mdinit:dir r_dir_perms;

# Date : WK17.03
# Operation : O Migration
# Purpose : HIDL for rilproxy
binder_call(radio, hal_telephony)

#Dat: 2017/02/14
#Purpose: allow get telephony Sensitive property
get_prop(radio, vendor_mtk_telephony_sensitive_prop)

# Date : WK17.26
# Operation : O Migration
# Purpose : HIDL for imsa
binder_call(radio, mtk_hal_imsa)

# Date : WK1727 2017/07/04
# Operation : IT
# Purpose : Allow to use HAL imsa
hal_client_domain(radio, hal_mtk_imsa)

#Dat: 2017/06/29
#Purpose: For audio parameter tuning
binder_call(radio, hal_audio_default)

# Date : WK18.16
# Operation: P migration
# Purpose: Allow radio to get vendor_mtk_tel_switch_prop
get_prop(radio, vendor_mtk_tel_switch_prop)
