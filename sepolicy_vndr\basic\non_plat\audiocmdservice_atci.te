# ==============================================
# Policy File of /vendor/bin/audiocmdservice_atci Executable File

# ==============================================
# Type Declaration
# ==============================================
type audiocmdservice_atci, domain;
type audiocmdservice_atci_exec, exec_type, file_type, vendor_file_type;

# ==============================================
# Common SEPolicy Rule
# ==============================================
init_daemon_domain(audiocmdservice_atci)

allow audiocmdservice_atci self:unix_stream_socket create_socket_perms;

# Access to storages for audio tuning tool to read/write tuning result
allow audiocmdservice_atci mnt_user_file:dir rw_dir_perms;
allow audiocmdservice_atci { mnt_user_file storage_file }:lnk_file rw_file_perms;
allow audiocmdservice_atci bootdevice_block_device:blk_file rw_file_perms;

# can route /dev/binder traffic to /dev/vndbinder
vndbinder_use(audiocmdservice_atci)
binder_call(audiocmdservice_atci, hal_audio_default)

hal_client_domain(audiocmdservice_atci, hal_audio)

#To access the file at /dev/kmsg
allow audiocmdservice_atci kmsg_device:chr_file w_file_perms;

userdebug_or_eng(`
  allow audiocmdservice_atci self:capability { sys_nice fowner chown fsetid setuid ipc_lock net_admin };
')
