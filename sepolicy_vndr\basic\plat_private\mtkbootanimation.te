# ==============================================
# Common SEPolicy Rule
# ==============================================

typeattribute mtkbootanimation coredomain;

type mtkbootanimation_exec, system_file_type, exec_type, file_type;

init_daemon_domain(mtkbootanimation)

# Date : WK14.37
# Operation : Migration
# Purpose : for opetator
set_prop(mtkbootanimation, system_mtk_bootani_prop)

hal_client_domain(mtkbootanimation, hal_configstore)
hal_client_domain(mtkbootanimation, hal_graphics_allocator)
hal_client_domain(mtkbootanimation, hal_graphics_composer)
binder_use(mtkbootanimation)
binder_call(mtkbootanimation, surfaceflinger)
binder_call(mtkbootanimation, audioserver)

allow mtkbootanimation gpu_device:chr_file rw_file_perms;

# /oem access
allow mtkbootanimation oemfs:dir search;
allow mtkbootanimation oemfs:file r_file_perms;

allow mtkbootanimation audio_device:dir r_dir_perms;
allow mtkbootanimation audio_device:chr_file rw_file_perms;

allow mtkbootanimation surfaceflinger_service:service_manager find;

# Allow access to ion memory allocation device
allow mtkbootanimation ion_device:chr_file rw_file_perms;
allow mtkbootanimation hal_graphics_allocator:fd use;

# Fences
allow mtkbootanimation hal_graphics_composer:fd use;

# Read access to pseudo filesystems.
allow mtkbootanimation proc_meminfo:file r_file_perms;
r_dir_file(mtkbootanimation, cgroup)

# System file accesses.
allow mtkbootanimation system_file:dir r_dir_perms;

# Date : WK14.32
# Operation : Migration
# Purpose : for playing boot tone
binder_call(mtkbootanimation, mediaserver)
allow mtkbootanimation mediaserver_service:service_manager find;

# Purpose : for playing bootanimation audio
allow mtkbootanimation audioserver_service:service_manager find;

# Date : WK14.37
# Operation : Migration
# Purpose : for opetator
set_prop(mtkbootanimation, debug_prop)

# Date : WK14.46
# Operation : Migration
# /data/resource-cache
allow mtkbootanimation resourcecache_data_file:dir search;
allow mtkbootanimation resourcecache_data_file:file r_file_perms;

# Data : WK16.42
# Operator: Whitney bring up
# Purpose: call surfaceflinger due to powervr
allow mtkbootanimation surfaceflinger:fifo_file rw_file_perms;

# Date : W16.42
# Operation : Integration
# Purpose : DRM / DRI GPU driver required
allow mtkbootanimation gpu_device:dir search;

