# ==============================================
# Common SEPolicy Rule
# ==============================================
##########################
# Filesystem types
#
##########################
# Proc Filesystem types
#
# MOTA permission
# Purpose: Allow GoogleOtaBinder to access proc
# path="/proc/cmdline"
type mota_proc_file, fs_type, proc_type;

# Purpose : Camera need read cl_cam_status
# Package: com.mediatek.camera
type proc_cl_cam_status, fs_type, proc_type;

# Date : 2020/03/25
# Operation: R migration
# Purpose: mtk EM battery settings
type proc_battery_cmd, fs_type, proc_type;

# Date : 2020/03/25
# Operation: R migration
# Purpose : mtk EM flash reading
type proc_flash, fs_type, proc_type;
type proc_partition, fs_type, proc_type;

##########################
# Sys Filesystem types
#
# define new type
# path = "/sys/devices/platform/(charger|mt-battery)/BatteryNotify"
type sysfs_battery_warning, fs_type, sysfs_type;

# define new type for sn process
# path = "/sys/class/android_usb/android0/iSerial"
# path = "/sys/devices/platform/mt_usb/cmode"
# path = "/sys/class/udc/musb-hdrc/device/cmode"
type sysfs_android0_usb, fs_type, sysfs_type;
type sysfs_usb_plat, fs_type, sysfs_type;

# Date : 2020/03/25
# Operation: R migration
# Purpose: mtk EM battery settings
type sysfs_battery_temp, fs_type, sysfs_type;
type sysfs_battery_consumption, fs_type, sysfs_type;
type sysfs_power_on_vol, fs_type, sysfs_type;
type sysfs_power_off_vol, fs_type, sysfs_type;
type sysfs_fg_disable, fs_type, sysfs_type;
type sysfs_dis_nafg, fs_type, sysfs_type;

# Date : 2020/06/01
# Operation: R migration
# Purpose : Add permission for acess /sys/devices/platform/mhl@0/extcon/HDMI_audio_extcon/state.
type sysfs_HDMI_audio_extcon_state, fs_type, sysfs_type;

# Date : 2018/11/01 -> 2020/06/23
# Operation: R migration
# Purpose : mtk EM c2k bypass read usb file
type sys_usb_rawbulk, fs_type, sysfs_type;

# wifi throttle
type sysfs_thermald, fs_type, sysfs_type;

##########################
# Debug Filesystem types
#

##########################
# File types
# Core domain data file types
#
# ATCI data file
type atci_data_file, file_type, data_file_type, core_data_file_type;

##########################
# EngineerMode app data file types
#
# for engineermode to access own app files
type em_app_data_file, file_type, data_file_type, core_data_file_type, app_data_file_type;
