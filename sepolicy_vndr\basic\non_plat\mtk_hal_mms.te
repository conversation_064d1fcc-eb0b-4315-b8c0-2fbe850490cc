# ==============================================
# Policy File of /vendor/bin/hw/vendor.mediatek.hardware.mms@1.0-service Executable File

# ==============================================
# Type Declaration
# ==============================================

type mtk_hal_mms, domain, mtk_safe_halserverdomain_type;
type mtk_hal_mms_exec, exec_type, file_type, vendor_file_type;

# ==============================================
# Common SEPolicy Rule
# ==============================================

# Setup for domain transition
init_daemon_domain(mtk_hal_mms)

# Allow a set of permissions required for a domain to be a server which provides a HAL implementation over HWBinder.
hal_server_domain(mtk_hal_mms, hal_mtk_mms)

# Purpose : Allow to use kernel driver
allow mtk_hal_mms graphics_device:chr_file rw_file_perms;
allow mtk_hal_mms ion_device:chr_file r_file_perms;
allow mtk_hal_mms mtk_cmdq_device:chr_file r_file_perms;
allow mtk_hal_mms mtk_mdp_device:chr_file r_file_perms;
allow mtk_hal_mms mtk_mdp_sync_device:chr_file r_file_perms;
allow mtk_hal_mms sw_sync_device:chr_file r_file_perms;
allow mtk_hal_mms proc_ged:file r_file_perms;
allowxperm mtk_hal_mms proc_ged:file ioctl { proc_ged_ioctls };

# Purpose : Allow to use allocator for JPEG
hal_client_domain(mtk_hal_mms, hal_allocator)
hal_client_domain(mtk_hal_mms, hal_graphics_allocator)
hal_client_domain(mtk_hal_mms, hal_mtk_pq)

# Purpose : Allow to use graphics allocator fd for gralloc_extra
allow mtk_hal_mms hal_graphics_allocator_default:fd use;
allow mtk_hal_mms debugfs_ion:dir search;
allow mtk_hal_mms merged_hal_service:fd use;

# Purpose : VDEC/VENC device node
allow mtk_hal_mms Vcodec_device:chr_file rw_file_perms;

allow mtk_hal_mms proc_mtk_jpeg:file r_file_perms;
allowxperm mtk_hal_mms proc_mtk_jpeg:file ioctl {
     JPG_BRIDGE_ENC_IO_INIT
     JPG_BRIDGE_ENC_IO_CONFIG
     JPG_BRIDGE_ENC_IO_WAIT
     JPG_BRIDGE_ENC_IO_DEINIT
     JPG_BRIDGE_ENC_IO_START
     };

# Purpose : Allow to use mms by JPEG with handle
allow mtk_hal_mms platform_app:fd use;

# Purpose : Allow Miravision to set Sharpness
allow mtk_hal_mms system_app:fd use;

# Purpose : Allow to set property for AIPQ
allow mtk_hal_mms apusys_device:chr_file rw_file_perms;
